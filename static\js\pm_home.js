// 项目首页和统计功能模块

/**
 * 初始化首页和统计功能
 * @param {Object} Vue Vue实例
 * @param {Object} myTodoTasks 我的待办任务引用
 * @param {Object} myCreatedTasks 我创建的任务引用
 * @param {Object} ElementPlus ElementPlus实例
 * @returns {Object} 包含首页和统计功能的对象
 */
export function initializeHomeFeatures(Vue, myTodoTasks, myCreatedTasks, ElementPlus) {
    const { ref, computed } = Vue;

    // 首页相关数据
    const homeLoading = ref(false);
    const activeTodoTab = ref('待完成');
    const activeCreatedTab = ref('待完成');
    const statsTimeRange = ref('month');  // 默认显示本月
    const customDateRange = ref(null);  // 自定义日期范围
    
    // 获取北京时间的今天和昨天日期
    const today = new Date();
    today.setHours(today.getHours() + 8); // 调整为北京时间 UTC+8
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // 快速创建任务功能
    const quickTaskName = ref('');
    const quickTaskDeadline = ref(today.toISOString().split('T')[0]);  // 默认为今天
    const quickTaskProjectId = ref(null);  // 项目ID
    const quickTaskUrgency = ref('一般');  // 任务紧急程度
    
    // 快速创建我的要求任务功能
    const quickCreatedTaskName = ref('');
    const quickCreatedTaskDeadline = ref(today.toISOString().split('T')[0]);  // 默认为今天
    const quickCreatedTaskProjectId = ref(null);  // 项目ID
    const quickCreatedTaskAssignees = ref([]);  // 负责人ID数组，改为多选
    const quickCreatedTaskUrgency = ref('一般');  // 任务紧急程度
    
    // 可用的负责人列表
    const availableAssignees = ref([]);
    
    // 添加负责人搜索
    const assigneeSearchQuery = ref('');
    
    // 处理全选/取消全选负责人
    const handleSelectAllQuickTaskAssignees = () => {
        if (quickCreatedTaskAssignees.value.length === availableAssignees.value.length) {
            // 如果已经全选，则取消全选
            quickCreatedTaskAssignees.value = [];
        } else {
            // 否则全选所有负责人
            quickCreatedTaskAssignees.value = availableAssignees.value.map(assignee => assignee.value);
        }
    };
    
    // 根据搜索词过滤负责人列表
    const filteredAssignees = computed(() => {
        if (!assigneeSearchQuery.value) {
            return availableAssignees.value;
        }
        
        const query = assigneeSearchQuery.value.toLowerCase();
        return availableAssignees.value.filter(assignee => {
            return assignee.label.toLowerCase().includes(query) || 
                   (assignee.department && assignee.department.toLowerCase().includes(query));
        });
    });
    
    // 获取当前日期
    const currentDate = new Date();
    const formattedDate = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月${currentDate.getDate()}日`;
    const greeting = currentDate.getHours() < 12 ? '早上好' : currentDate.getHours() < 18 ? '下午好' : '晚上好';

    // 获取统计时间范围标签
    const getStatsTimeRangeLabel = () => {
        if (statsTimeRange.value === 'today') {
            return '今日统计';
        } else if (statsTimeRange.value === 'all') {
            return '全部';
        } else if (statsTimeRange.value === 'week') {
            return '本周统计';
        } else if (statsTimeRange.value === 'month') {
            return '本月统计';
        } else if (statsTimeRange.value === 'custom' && customDateRange.value) {
            return '区间统计';
        } else {
            return '总计';
        }
    };

    // 处理统计时间范围变化
    const handleStatsTimeRangeChange = (value) => {
        console.log('Stats time range changed:', value);
        statsTimeRange.value = value;

        // 构建API请求参数
        let params = new URLSearchParams({
            time_range: value
        });

        // 如果是自定义日期范围，添加日期参数
        if (value === 'custom') {
            if (!customDateRange.value) {
                // 如果是首次选择自定义，设置默认为最近7天
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - 7);
                customDateRange.value = [
                    startDate.toISOString().split('T')[0],
                    endDate.toISOString().split('T')[0]
                ];
            }
            params.append('start_date', customDateRange.value[0]);
            params.append('end_date', customDateRange.value[1]);
        }

        // 显示加载状态
        /*homeLoading.value = true;

        // 调用后端API获取统计数据
        fetch(`/projectmanagement/api/stats_time_range?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新统计数据
                    statsData.value = {
                        total: data.stats.total,
                        completed: data.stats.completed,
                        in_progress: data.stats.in_progress,
                        overdue: data.stats.overdue,
                        time_range_desc: data.stats.time_range_desc
                    };

                    // 记录日志
                    console.log('统计数据更新:', statsData.value);
                } else {
                    throw new Error(data.message || '获取统计数据失败');
                }
            })
            .catch(error => {
                console.error('获取统计数据失败:', error);
                ElementPlus.ElMessage({
                    message: error.message || '获取统计数据失败',
                    type: 'error'
                });
            })
            .finally(() => {
                homeLoading.value = false;
            });*/
    };

    // 处理自定义日期范围变化
    const handleCustomDateRangeChange = (value) => {
        console.log('Custom date range changed:', value);
        if (value && value.length === 2) {
            customDateRange.value = value;
            console.log('Updated custom date range:', customDateRange.value);
            // 这里不再调用loadHomeData，将由外部调用
            
            // 时间范围变化后重新渲染图表 - 由外部触发
        }
    };
    
    // 处理任务标签页切换
    const handleTabChange = (type, tabName) => {
        console.log(`Tab changed: ${type} -> ${tabName}`, {
            statsTimeRange: statsTimeRange.value,
            customDateRange: customDateRange.value
        });
        
        if (type === 'todo') {
            activeTodoTab.value = tabName;
            // 根据标签页设置默认截止日期
            if (tabName === '待完成') {
                quickTaskDeadline.value = today.toISOString().split('T')[0];
            } else if (tabName === '已逾期') {
                quickTaskDeadline.value = yesterday.toISOString().split('T')[0];
            }
        } else if (type === 'created') {
            activeCreatedTab.value = tabName;
            // 根据标签页设置默认截止日期
            if (tabName === '待完成') {
                quickCreatedTaskDeadline.value = today.toISOString().split('T')[0];
            } else if (tabName === '已逾期') {
                quickCreatedTaskDeadline.value = yesterday.toISOString().split('T')[0];
            }
        }
        
        // 返回渲染图表的函数供外部调用
        return () => {
            // 需要外部调用renderTodoTimeDistribution和renderTodoDailyTracking函数
            console.log('Tab changed, charts should be re-rendered');
        };
    };

    // 重点关注数据
    const focusStats = computed(() => {
        const now = new Date();
        
        // 根据选择的日期范围设置起止时间
        let startDate = new Date();
        startDate.setHours(0, 0, 0, 0);  // 设置为当天0点
        let endDate = new Date();
        endDate.setHours(23, 59, 59, 999); // 设置为当天结束
        
        // 上一个时间段的起止时间（用于同比计算）
        let prevStartDate = new Date(startDate);
        let prevEndDate = new Date(endDate);
        
        if (statsTimeRange.value === 'today') {
            // 今天的时间范围，已设置好
            // 设置"上一个"时间段为昨天
            prevStartDate.setDate(prevStartDate.getDate() - 1);
            prevEndDate.setDate(prevEndDate.getDate() - 1);
        } else if (statsTimeRange.value === 'week') {
            // 设置为本周
            const day = startDate.getDay() || 7;
            startDate.setDate(startDate.getDate() - day + 1); // 设置为本周一
            endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 6); // 设置为本周日
            endDate.setHours(23, 59, 59, 999);
            
            // 设置"上一个"时间段为上周
            prevStartDate = new Date(startDate);
            prevStartDate.setDate(prevStartDate.getDate() - 7);
            prevEndDate = new Date(endDate);
            prevEndDate.setDate(prevEndDate.getDate() - 7);
            
            console.log('Week date range for focusStats:', {
                startDate: startDate.toLocaleString('zh-CN'),
                endDate: endDate.toLocaleString('zh-CN'),
                prevStartDate: prevStartDate.toLocaleString('zh-CN'),
                prevEndDate: prevEndDate.toLocaleString('zh-CN')
            });
        } else if (statsTimeRange.value === 'month') {
            // 设置为本月1号
            startDate.setDate(1);
            // 设置为本月最后一天
            endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
            endDate.setHours(23, 59, 59, 999);
            
            // 设置"上一个"时间段为上月
            prevStartDate = new Date(startDate);
            prevStartDate.setMonth(prevStartDate.getMonth() - 1);
            prevEndDate = new Date(startDate.getFullYear(), startDate.getMonth(), 0);
            prevEndDate.setHours(23, 59, 59, 999);
        } else if (statsTimeRange.value === 'custom') {
            if (customDateRange.value && customDateRange.value.length === 2) {
                // 自定义日期范围
                startDate = new Date(customDateRange.value[0] + ' 00:00:00');
                endDate = new Date(customDateRange.value[1] + ' 23:59:59');
                
                // 计算日期差值
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                // 设置上一个时间段为相同长度
                prevStartDate = new Date(startDate);
                prevStartDate.setDate(prevStartDate.getDate() - diffDays);
                prevEndDate = new Date(startDate);
                prevEndDate.setDate(prevEndDate.getDate() - 1);
                prevEndDate.setHours(23, 59, 59, 999);
            } else {
                // 如果没有有效的自定义日期范围，默认使用本月
                startDate.setDate(1);
                endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
                endDate.setHours(23, 59, 59, 999);
                
                // 设置"上一个"时间段为上月
                prevStartDate = new Date(startDate);
                prevStartDate.setMonth(prevStartDate.getMonth() - 1);
                prevEndDate = new Date(startDate.getFullYear(), startDate.getMonth(), 0);
                prevEndDate.setHours(23, 59, 59, 999);
            }
        }
        
        // 确保统计逻辑一致：统计"我的任务"（分配给我的）和"我的要求"（我创建的）
        // 从两个数组中获取任务并去重
        const todoTaskIds = new Set(myTodoTasks.value.map(task => task.id));
        const createdTaskIds = new Set(myCreatedTasks.value.map(task => task.id));
        
        // 合并任务IDs
        const allTaskIds = new Set([...todoTaskIds, ...createdTaskIds]);
        
        // 从所有任务中找出匹配的任务
        let allTasks = [];
        for (const taskId of allTaskIds) {
            // 先从我的待办任务中查找
            let task = myTodoTasks.value.find(t => t.id === taskId);
            // 如果没找到，从我创建的任务中查找
            if (!task) {
                task = myCreatedTasks.value.find(t => t.id === taskId);
            }
            if (task) {
                allTasks.push(task);
            }
        }
        
        // 分析任务创建日期字段的分布情况
        const createdDateAnalysis = {
            total: allTasks.length,
            hasCreatedDate: 0,
            hasCreatedAt: 0,
            hasBoth: 0,
            hasNeither: 0,
            samples: []
        };
        
        allTasks.forEach(task => {
            if (task.created_date && task.created_at) {
                createdDateAnalysis.hasBoth++;
            } else if (task.created_date) {
                createdDateAnalysis.hasCreatedDate++;
            } else if (task.created_at) {
                createdDateAnalysis.hasCreatedAt++;
            } else {
                createdDateAnalysis.hasNeither++;
            }
            
            // 保存一些样本
            if (createdDateAnalysis.samples.length < 5) {
                createdDateAnalysis.samples.push({
                    id: task.id,
                    name: task.name,
                    created_date: task.created_date,
                    created_at: task.created_at
                });
            }
        });
        
        console.log('任务创建日期分析:', createdDateAnalysis);
        
        // 当前时间段内的任务（根据截止日期过滤）
        const filteredTasks = allTasks.filter(task => {
            // 获取任务的截止日期
            if (!task.deadline) {
                // 没有截止日期的任务不计入统计
                return false;
            }
            const deadlineDate = new Date(task.deadline + ' 00:00:00');
            // 判断截止日期是否在所选时间范围内
            return deadlineDate >= startDate && deadlineDate <= endDate;
        });
        
        // 当前时间段完成的任务
        const completedTasks = filteredTasks.filter(task => task.completed_date).length;
        
        // 上一个时间段的任务（根据截止日期过滤）
        const prevFilteredTasks = allTasks.filter(task => {
            if (!task.deadline) return false;
            const deadlineDate = new Date(task.deadline + ' 00:00:00');
            return deadlineDate >= prevStartDate && deadlineDate <= prevEndDate;
        });
        
        // 上一个时间段完成的任务
        const prevCompletedTasks = prevFilteredTasks.filter(task => task.completed_date).length;
        
        // 当前时间段新建的任务（使用created_at字段过滤）
        const createdTasks = allTasks.filter(task => {
            // 既检查created_date（旧版本兼容）又检查created_at（新版本）
            const createdDateStr = task.created_at || task.created_date;
            if (!createdDateStr) return false;
            const createdDate = new Date(createdDateStr);
            return createdDate >= startDate && createdDate <= endDate;
        }).length;
        
        // 上一个时间段新建的任务（使用created_at字段过滤）
        const prevCreatedTasks = allTasks.filter(task => {
            // 既检查created_date（旧版本兼容）又检查created_at（新版本）
            const createdDateStr = task.created_at || task.created_date;
            if (!createdDateStr) return false;
            const createdDate = new Date(createdDateStr);
            return createdDate >= prevStartDate && createdDate <= prevEndDate;
        }).length;
        
        // 计算同比变化百分比
        const completedIncrease = prevCompletedTasks > 0
            ? Math.round(((completedTasks - prevCompletedTasks) / prevCompletedTasks) * 100)
            : (completedTasks > 0 ? 100 : 0);
        
        const createdIncrease = prevCreatedTasks > 0
            ? Math.round(((createdTasks - prevCreatedTasks) / prevCreatedTasks) * 100)
            : (createdTasks > 0 ? 100 : 0);
        
        // 有截止时间的任务（只统计"我要办"的任务）
        const assignedTasks = myTodoTasks.value.filter(task => {
            // 只统计在选定时间范围内的任务
            if (!task.deadline) return false;
            const deadlineDate = new Date(task.deadline + ' 00:00:00');
            return deadlineDate >= startDate && deadlineDate <= endDate;
        });
        
        const tasksWithDeadline = assignedTasks.length; // 因为上面已经过滤了没有截止日期的任务
        const percentWithDeadline = 100; // 因为上面已经过滤了没有截止日期的任务，所以占比是100%
        
        // 获取人性化的时间范围描述
        let timeRangeDesc = '本月';
        if (statsTimeRange.value === 'today') {
            timeRangeDesc = '今日';
        } else if (statsTimeRange.value === 'week') {
            timeRangeDesc = '本周';
        } else if (statsTimeRange.value === 'custom') {
            timeRangeDesc = '所选时间';
        }
        
        let prevTimeRangeDesc = '上月';
        if (statsTimeRange.value === 'today') {
            prevTimeRangeDesc = '昨日';
        } else if (statsTimeRange.value === 'week') {
            prevTimeRangeDesc = '上周';
        } else if (statsTimeRange.value === 'custom') {
            prevTimeRangeDesc = '前一时段';
        }
        
        console.log('focusStats calculation results:', {
            timeRange: statsTimeRange.value,
            completedTasks,
            prevCompletedTasks,
            createdTasks,
            prevCreatedTasks,
            completedIncrease,
            createdIncrease,
            dateRanges: {
                currentPeriod: {startDate: startDate.toLocaleString('zh-CN'), endDate: endDate.toLocaleString('zh-CN')},
                previousPeriod: {startDate: prevStartDate.toLocaleString('zh-CN'), endDate: prevEndDate.toLocaleString('zh-CN')}
            },
            taskSample: allTasks.length > 0 ? {
                id: allTasks[0].id,
                name: allTasks[0].name,
                created_date: allTasks[0].created_date,
                created_at: allTasks[0].created_at,
                createdDateUsed: allTasks[0].created_at || allTasks[0].created_date
            } : '没有任务数据'
        });
        
        return {
            completedTasks,
            createdTasks,
            completedIncrease,
            createdIncrease,
            tasksWithDeadline,
            percentWithDeadline,
            timeRangeDesc,
            prevTimeRangeDesc
        };
    });

    // 统计数据计算
    const statsData = computed(() => {
        // 根据选择的时间范围过滤任务
        const now = new Date();
        let startDate = new Date();
        startDate.setHours(0, 0, 0, 0);  // 设置为当天0点
        let endDate = new Date();
        endDate.setHours(23, 59, 59, 999); // 设置为当天结束
        
        if (statsTimeRange.value === 'today') {
            // 今天凌晨，不需要额外处理
        } else if (statsTimeRange.value === 'week') {
            // 设置为本周，确保与 focusStats 保持一致
            const day = startDate.getDay() || 7;
            startDate.setDate(startDate.getDate() - day + 1); // 设置为本周一
            endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 6); // 设置为本周日
            endDate.setHours(23, 59, 59, 999);
            
            console.log('Week date range for statsData:', {
                startDate: startDate.toLocaleString('zh-CN'),
                endDate: endDate.toLocaleString('zh-CN')
            });
        } else if (statsTimeRange.value === 'month') {
            // 本月1号凌晨
            startDate.setDate(1);
            // 设置为本月最后一天
            endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
            endDate.setHours(23, 59, 59, 999);
        } else if (statsTimeRange.value === 'custom' && customDateRange.value && customDateRange.value.length === 2) {
            // 自定义日期范围
            startDate = new Date(customDateRange.value[0] + ' 00:00:00');
            endDate = new Date(customDateRange.value[1] + ' 23:59:59');
        }

        // 确保统计逻辑一致：统计"我的任务"（分配给我的）和"我的要求"（我创建的）
        // 从两个数组中获取任务并去重
        const todoTaskIds = new Set(myTodoTasks.value.map(task => task.id));
        const createdTaskIds = new Set(myCreatedTasks.value.map(task => task.id));
        
        // 合并任务IDs
        const allTaskIds = new Set([...todoTaskIds, ...createdTaskIds]);
        
        // 从所有任务中找出匹配的任务
        let allTasks = [];
        for (const taskId of allTaskIds) {
            // 先从我的待办任务中查找
            let task = myTodoTasks.value.find(t => t.id === taskId);
            // 如果没找到，从我创建的任务中查找
            if (!task) {
                task = myCreatedTasks.value.find(t => t.id === taskId);
            }
            if (task) {
                allTasks.push(task);
            }
        }
        
        console.log('所有任务统计:', {
            我的待办: myTodoTasks.value.length,
            我创建的: myCreatedTasks.value.length,
            去重后总数: allTasks.length
        });
        
        // 过滤在时间范围内的任务（根据截止时间）
        const filteredTasks = allTasks.filter(task => {
            if(statsTimeRange.value === 'all'){
                 return true;
            }
            // 获取任务的截止日期
            if (!task.deadline) {
                // 没有截止日期的任务不计入统计
                return false;
            }
            const deadlineDate = new Date(task.deadline + ' 00:00:00');
            // 判断截止日期是否在所选时间范围内
            return deadlineDate >= startDate && deadlineDate <= endDate;
        });

        console.log(`统计时间范围: ${statsTimeRange.value}`, {
            startDate: startDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
            endDate: endDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
            totalTasks: allTasks.length,
            filteredTasks: filteredTasks.length
        });

        // 计算统计数据
        const finished = filteredTasks.filter(task => task.completed_date).length;

        const overdue = filteredTasks.filter(task => {
            if (task.completed_date) return false;
            if (!task.deadline) return false;
            // 修改逾期判定逻辑：只取日期部分进行比较，与SQL的CURDATE()保持一致
            const deadlineDate = new Date(task.deadline + ' 00:00:00');
            const today = new Date();
            today.setHours(0, 0, 0, 0); // 设置为当天的开始时间
            return deadlineDate < today;
        }).length;
        const unfinished = filteredTasks.length - finished-overdue;
        console.log('statsData calculation results:', {
            timeRange: statsTimeRange.value,
            totalTasks: filteredTasks.length,
            finished,
            unfinished,
            overdue
        });

        return {
            unfinished,
            finished,
            total: filteredTasks.length,
            overdue
        };
    });

    // 过滤待办任务
    const filteredTodoTasks = computed(() => {
        // 不再根据日期范围过滤，直接获取所有任务
        const now = new Date();
        console.log('Filtering todo tasks without date range, total tasks:', myTodoTasks.value.length);
        
        // 按照选项卡状态进行过滤
        let result = [];
        if (activeTodoTab.value === '待完成') {
            result = myTodoTasks.value.filter(task => !task.completed_date);
        } else if (activeTodoTab.value === '已逾期') {
            result = myTodoTasks.value.filter(task => {
                if (task.completed_date) return false;
                if (!task.deadline) return false;
                // 修改逾期判定逻辑：只比较日期部分
                const deadlineDate = new Date(task.deadline + ' 00:00:00');
                const today = new Date();
                today.setHours(0, 0, 0, 0); // 设置为当天的开始时间
                return deadlineDate < today;
            });
        }
        
        console.log(`Final filtered todo tasks (${activeTodoTab.value}):`, result.length);
        return result;
    });

    // 过滤我发起的任务
    const filteredCreatedTasks = computed(() => {
        // 不再根据日期范围过滤，直接获取所有任务
        const now = new Date();
        console.log('Filtering created tasks without date range, total tasks:', myCreatedTasks.value.length);
        
        // 按照选项卡状态进行过滤
        let result = [];
        if (activeCreatedTab.value === '待完成') {
            result = myCreatedTasks.value.filter(task => !task.completed_date);
        } else if (activeCreatedTab.value === '已逾期') {
            result = myCreatedTasks.value.filter(task => {
                if (task.completed_date) return false;
                if (!task.deadline) return false;
                // 修改逾期判定逻辑：只比较日期部分
                const deadlineDate = new Date(task.deadline + ' 00:00:00');
                const today = new Date();
                today.setHours(0, 0, 0, 0); // 设置为当天的开始时间
                return deadlineDate < today;
            });
        }
        
        console.log(`Final filtered created tasks (${activeCreatedTab.value}):`, result.length);
        return result;
    });

    // 我要办的任务概览统计
    const filteredTodoStats = computed(() => {
        // 根据选择的日期范围设置起止时间
        let startDate = new Date();
        startDate.setHours(0, 0, 0, 0);  // 设置为当天0点
        let endDate = new Date();
        endDate.setHours(23, 59, 59, 999); // 设置为当天结束
        
        if (statsTimeRange.value === 'today') {
            // 今天的时间范围，已设置好
        } else if (statsTimeRange.value === 'week') {
            // 设置为本周，确保与 focusStats 保持一致
            const day = startDate.getDay() || 7;
            startDate.setDate(startDate.getDate() - day + 1); // 设置为本周一
            endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 6); // 设置为本周日
            endDate.setHours(23, 59, 59, 999);
            
            console.log('Week date range for filteredTodoStats:', {
                startDate: startDate.toLocaleString('zh-CN'),
                endDate: endDate.toLocaleString('zh-CN')
            });
        } else if (statsTimeRange.value === 'month') {
            // 设置为本月1号
            startDate.setDate(1);
            // 设置为本月最后一天
            endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
            endDate.setHours(23, 59, 59, 999);
        } else if (statsTimeRange.value === 'custom') {
            if (customDateRange.value && customDateRange.value.length === 2) {
                startDate = new Date(customDateRange.value[0] + ' 00:00:00');
                endDate = new Date(customDateRange.value[1] + ' 23:59:59');
            } else {
                // 如果没有有效的自定义日期范围，默认使用本月
                startDate.setDate(1);
                endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
                endDate.setHours(23, 59, 59, 999);
            }
        }
        
        // 确保统计逻辑一致：统计"我的任务"（分配给我的）和"我的要求"（我创建的）
        // 从两个数组中获取任务并去重
        const todoTaskIds = new Set(myTodoTasks.value.map(task => task.id));
        const createdTaskIds = new Set(myCreatedTasks.value.map(task => task.id));
        
        // 合并任务IDs
        const allTaskIds = new Set([...todoTaskIds, ...createdTaskIds]);
        
        // 从所有任务中找出匹配的任务
        let allTasks = [];
        for (const taskId of allTaskIds) {
            // 先从我的待办任务中查找
            let task = myTodoTasks.value.find(t => t.id === taskId);
            // 如果没找到，从我创建的任务中查找
            if (!task) {
                task = myCreatedTasks.value.find(t => t.id === taskId);
            }
            if (task) {
                allTasks.push(task);
            }
        }
        
        // 首先根据日期范围过滤任务
        const dateFilteredTasks = allTasks.filter(task => {
            if(statsTimeRange.value === 'all'){
                 return true;
            }
            // 统一处理无截止日期的任务，与 statsData 和 focusStats 保持一致
            if (!task.deadline) {
                // 没有截止日期的任务不计入统计
                return false;
            }
            
            const deadlineDate = new Date(task.deadline + ' 00:00:00');
            // 判断截止日期是否在所选时间范围内
            return deadlineDate >= startDate && deadlineDate <= endDate;
        });
        
        console.log('filteredTodoStats calculation results:', {
            timeRange: statsTimeRange.value,
            totalTasks: dateFilteredTasks.length,
            completed: dateFilteredTasks.filter(task => task.completed_date).length
        });
        
        // 计算总数
        const total = dateFilteredTasks.length;
        
        // 计算已完成数量
        const completed = dateFilteredTasks.filter(task => task.completed_date).length;
        

        
        // 计算已逾期任务数量
        const now = new Date();
        const overdue = dateFilteredTasks.filter(task => {
            if (task.completed_date) return false; // 已完成的不算逾期
            if (!task.deadline) return false; // 没有截止日期的不算逾期
            // 修改逾期判定逻辑：只比较日期部分
            const deadlineDate = new Date(task.deadline + ' 00:00:00');
            const today = new Date();
            today.setHours(0, 0, 0, 0); // 设置为当天的开始时间
            return deadlineDate < today;
        }).length;
        // 计算未完成数量
        const unfinished = total - completed-overdue;
        // 计算没有设置截止日期的任务数量 - 由于我们已经过滤掉了没有截止日期的任务，这个值应该是0
        const noDeadline = 0;
        
        // 计算今天到期的任务数量
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        const dueToday = dateFilteredTasks.filter(task => {
            if (task.completed_date) return false; // 已完成的不算今日到期
            if (!task.deadline) return false; // 没有截止日期的不算今日到期
            const deadlineDate = new Date(task.deadline);
            deadlineDate.setHours(0, 0, 0, 0);
            return deadlineDate.getTime() === today.getTime();
        }).length;
        
        return {
            total,
            completed,
            unfinished,
            overdue,
            noDeadline,
            dueToday
        };
    });

    // 快速创建任务方法
    const createQuickTask = (currentProject, projects, loadHomeData) => {
        if (!quickTaskName.value.trim()) return;
        
        // 使用选择的项目或找到第一个可用项目
        let targetProject = null;
        
        // 如果已经选择了项目，直接使用
        if (quickTaskProjectId.value) {
            targetProject = projects.find(p => p.id === quickTaskProjectId.value);
        }
        
        // 如果没有选择项目，使用第一个可用项目
        if (!targetProject) {
            targetProject = projects.length > 0 ? projects[0] : null;
            if (targetProject) {
                quickTaskProjectId.value = targetProject.id;
            }
        }
        
        if (!targetProject) {
            ElementPlus.ElMessage.warning('没有可用项目，无法创建任务');
            return;
        }
        
        // 显示加载状态
        const loadingInstance = ElementPlus.ElLoading.service({
            target: '.home-task-section',
            text: '创建任务中...',
            fullscreen: false,
            background: 'rgba(255, 255, 255, 0.7)'
        });
        
        // 构建任务数据 - 只需要名称，其他使用默认值
        const taskData = {
            name: quickTaskName.value,
            project_id: targetProject.id,
            description: '',
            urgency: quickTaskUrgency.value,  // 使用选择的紧急程度
            deadline: quickTaskDeadline.value,
        };
        
        // 发送请求创建任务
        fetch('/projectmanagement/api/create_task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(taskData)
        }).then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '创建任务失败');
                });
            }
            return response.json();
        }).then(data => {
            if (data.success) {
                // 刷新首页数据
                if (loadHomeData) {
                    loadHomeData();
                }
                
                // 清空输入框
                quickTaskName.value = '';
                
                // 显示成功消息
                ElementPlus.ElMessage({
                    message: '任务创建成功',
                    type: 'success'
                });
            } else {
                throw new Error(data.message || '创建任务失败');
            }
        }).catch(error => {
            ElementPlus.ElMessage({
                message: error.message,
                type: 'error'
            });
        }).finally(() => {
            loadingInstance.close();
        });
    };

    // 辅助函数：格式化日期为YYYY-MM-DD
    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    // 处理快速任务日期变化
    const handleQuickTaskDateChange = (date) => {
        quickTaskDeadline.value = date;
    };
    
    // 聚焦日期选择器
    const focusQuickTaskDate = () => {
        // 通过点击隐藏的日期选择器来打开它
        const datePicker = document.querySelector('.quick-task-date .el-date-picker__wrapper');
        if (datePicker) {
            datePicker.click();
        }
    };

    // 处理快速任务项目变化
    const handleQuickTaskProjectChange = (projectId) => {
        quickTaskProjectId.value = projectId;
    };
    
    // 初始化快速任务项目
    const initQuickTaskProject = (projects) => {
        // 设置为第一个可用项目
        if (projects && projects.length > 0 && !quickTaskProjectId.value) {
            quickTaskProjectId.value = projects[0].id;
        }
        
        // 同样为"我的要求"任务设置默认项目
        if (projects && projects.length > 0 && !quickCreatedTaskProjectId.value) {
            quickCreatedTaskProjectId.value = projects[0].id;
        }
    };

    // 快速创建"我的要求"任务
    const createQuickCreatedTask = (currentProject, projects, loadHomeData) => {
         if (quickCreatedTaskAssignees.value.length ===0) {
            ElementPlus.ElMessage.warning('请至少选择一个负责人!');
            return;
        }
        if (!quickCreatedTaskName.value.trim()) return;
        
        // 使用选择的项目或找到第一个可用项目
        let targetProject = null;
        
        // 如果已经选择了项目，直接使用
        if (quickCreatedTaskProjectId.value) {
            targetProject = projects.find(p => p.id === quickCreatedTaskProjectId.value);
        }
        
        // 如果没有选择项目，使用第一个可用项目
        if (!targetProject) {
            targetProject = projects.length > 0 ? projects[0] : null;
            if (targetProject) {
                quickCreatedTaskProjectId.value = targetProject.id;
            }
        }
        
        if (!targetProject) {
            ElementPlus.ElMessage.warning('没有可用项目，无法创建任务');
            return;
        }
        
        // 显示加载状态
        const loadingInstance = ElementPlus.ElLoading.service({
            target: '.home-task-section:nth-child(2)',
            text: '创建任务中...',
            fullscreen: false,
            background: 'rgba(255, 255, 255, 0.7)'
        });
        
        // 构建任务数据
        const taskData = {
            name: quickCreatedTaskName.value,
            project_id: targetProject.id,
            description: '',
            urgency: quickCreatedTaskUrgency.value,  // 使用选择的紧急程度
            deadline: quickCreatedTaskDeadline.value,
            is_created_by_me: true, // 标记为"我的要求"
        };
        
        // 如果选择了负责人，添加到任务数据中
        if (quickCreatedTaskAssignees.value.length > 0) {
            taskData.assignee_ids = quickCreatedTaskAssignees.value;
            // 如果选择的负责人不是项目成员，添加到非项目成员列表
            taskData.non_project_member_ids = quickCreatedTaskAssignees.value.filter(id => {
                const selectedAssignee = availableAssignees.value.find(a => a.value === id);
                return !selectedAssignee || !selectedAssignee.isProjectMember;
            });
        }
        
        // 发送请求创建任务
        fetch('/projectmanagement/api/create_task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(taskData)
        }).then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '创建任务失败');
                });
            }
            return response.json();
        }).then(data => {
            if (data.success) {
                // 刷新首页数据
                if (loadHomeData) {
                    loadHomeData();
                }
                
                // 清空输入框和选择的负责人
                quickCreatedTaskName.value = '';
                quickCreatedTaskAssignees.value = [];
                
                // 显示成功消息
                ElementPlus.ElMessage({
                    message: '任务创建成功',
                    type: 'success'
                });
            } else {
                throw new Error(data.message || '创建任务失败');
            }
        }).catch(error => {
            ElementPlus.ElMessage({
                message: error.message,
                type: 'error'
            });
        }).finally(() => {
            loadingInstance.close();
        });
    };
    
    // 处理"我的要求"快速任务日期变化
    const handleQuickCreatedTaskDateChange = (date) => {
        quickCreatedTaskDeadline.value = date;
    };
    
    // 聚焦"我的要求"日期选择器
    const focusQuickCreatedTaskDate = () => {
        // 通过点击隐藏的日期选择器来打开它
        const datePickerWrappers = document.querySelectorAll('.quick-task-date .el-date-picker__wrapper');
        if (datePickerWrappers && datePickerWrappers.length > 1) {
            datePickerWrappers[1].click(); // 选择第二个日期选择器
        }
    };
    
    // 处理"我的要求"快速任务项目变化
    const handleQuickCreatedTaskProjectChange = (projectId) => {
        quickCreatedTaskProjectId.value = projectId;
        // 当项目变化时，重新加载该项目的可用负责人
        loadProjectAssignees(projectId);
    };
    
    // 处理"我的要求"快速任务负责人变化
    const handleQuickCreatedTaskAssigneeChange = (assigneeId) => {
        // 检查负责人是否已经被选中
        const index = quickCreatedTaskAssignees.value.indexOf(assigneeId);
        if (index === -1) {
            // 如果未选中，则添加到选中列表
            quickCreatedTaskAssignees.value.push(assigneeId);
        } else {
            // 如果已选中，则从选中列表中移除
            quickCreatedTaskAssignees.value.splice(index, 1);
        }
    };
    
    // 加载项目成员作为可分配负责人
    const loadProjectAssignees = (projectId) => {
        if (!projectId) return;
        
        // 重置当前选择的负责人
        quickCreatedTaskAssignees.value = [];
        
        // 先获取当前项目成员，然后获取全部在职员工
        fetch(`/projectmanagement/api/project_members?project_id=${projectId}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '获取项目成员失败');
                    });
                }
                return response.json();
            })
            .then(projectData => {
                if (!projectData.success) {
                    throw new Error(projectData.message || '获取项目成员失败');
                }
                
                // 获取项目成员ID列表
                const projectMemberIds = (projectData.members || []).map(member => member.id);
                
                // 然后获取所有在职员工
                return fetch('/projectmanagement/api/employees')
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.message || '获取员工数据失败');
                            });
                        }
                        return response.json();
                    })
                    .then(employeeData => {
                        if (!employeeData.success) {
                            throw new Error(employeeData.message || '获取员工数据失败');
                        }
                        
                        // 转换成可选择的负责人格式，标记是否为项目成员
                        availableAssignees.value = (employeeData.employees || []).map(employee => {
                            // 确保ID比较时使用字符串或数字一致的格式
                            const employeeId = String(employee.id);
                            const isProjectMember = projectMemberIds.some(id => 
                                String(id) === employeeId
                            );
                            
                            return {
                                value: employee.id,
                                label: employee.name,
                                department: employee.department || '未知部门',
                                isProjectMember: isProjectMember
                            };
                        });
                    });
            })
            .catch(error => {
                console.error('加载项目成员失败:', error);
                ElementPlus.ElMessage({
                    message: error.message,
                    type: 'error'
                });
                availableAssignees.value = [];
            });
    };

    // 处理快速任务紧急程度变化
    const handleQuickTaskUrgencyChange = (urgency) => {
        quickTaskUrgency.value = urgency;
    };
    
    // 处理"我的要求"快速任务紧急程度变化
    const handleQuickCreatedTaskUrgencyChange = (urgency) => {
        quickCreatedTaskUrgency.value = urgency;
    };

    // 统计任务对话框数据
    const statsTaskDialog = ref({
        visible: false,
        loading: false,
        title: '',
        tasks: [],
        currentPage: 1,
        pageSize: 10,
        total: 0,
        type: '',
        timeRange: ''
    });

    // 处理统计项点击
    const handleStatsItemClick = async (type) => {
        statsTaskDialog.value.visible = true;
        statsTaskDialog.value.loading = true;
        statsTaskDialog.value.type = type;
        statsTaskDialog.value.timeRange = statsTimeRange.value;
        statsTaskDialog.value.currentPage = 1;
        
        // 设置对话框标题
        const titles = {
            unfinished: '未完成任务',
            finished: '已完成任务',
            total: '全部任务',
            overdue: '已逾期任务'
        };
        statsTaskDialog.value.title = `${titles[type]} - ${getStatsTimeRangeLabel()}`;
        
        // 加载任务数据
        await loadStatsTaskData();
    };

    // 加载统计任务数据
    const loadStatsTaskData = async () => {
        try {
            // 构建请求参数
            const params = new URLSearchParams({
                type: statsTaskDialog.value.type,
                time_range: statsTaskDialog.value.timeRange,
                page: statsTaskDialog.value.currentPage,
                page_size: statsTaskDialog.value.pageSize
            });

            // 如果是自定义日期范围，添加日期参数
            if (statsTaskDialog.value.timeRange === 'custom' && customDateRange.value) {
                params.append('start_date', customDateRange.value[0]);
                params.append('end_date', customDateRange.value[1]);
            }

            const response = await fetch(`/projectmanagement/api/stats_task_details?${params.toString()}`);
            const data = await response.json();

            if (data.success) {
                statsTaskDialog.value.tasks = data.tasks;
                statsTaskDialog.value.total = data.pagination.total;
            } else {
                throw new Error(data.message || '获取任务详情失败');
            }
        } catch (error) {
            console.error('获取任务详情失败:', error);
            ElementPlus.ElMessage({
                message: error.message || '获取任务详情失败',
                type: 'error'
            });
        } finally {
            statsTaskDialog.value.loading = false;
        }
    };

    // 处理页码变化
    const handleStatsTaskPageChange = (newPage) => {
        statsTaskDialog.value.currentPage = newPage;
        loadStatsTaskData();
    };

    // 处理每页数量变化
    const handleStatsTaskPageSizeChange = (newSize) => {
        statsTaskDialog.value.pageSize = newSize;
        statsTaskDialog.value.currentPage = 1;
        loadStatsTaskData();
    };

    return {
        homeLoading,
        activeTodoTab,
        activeCreatedTab,
        statsTimeRange,
        customDateRange,
        formattedDate,
        greeting,
        getStatsTimeRangeLabel,
        handleStatsTimeRangeChange,
        handleCustomDateRangeChange,
        handleTabChange,
        focusStats,
        statsData,
        filteredTodoTasks,
        filteredCreatedTasks,
        filteredTodoStats,
        quickTaskName,
        quickTaskDeadline,
        quickTaskProjectId,
        quickTaskUrgency,
        createQuickTask,
        formatDate,
        handleQuickTaskDateChange,
        handleQuickTaskUrgencyChange,
        focusQuickTaskDate,
        handleQuickTaskProjectChange,
        initQuickTaskProject,
        quickCreatedTaskName,
        quickCreatedTaskDeadline,
        quickCreatedTaskProjectId,
        quickCreatedTaskAssignees,
        quickCreatedTaskUrgency,
        availableAssignees,
        assigneeSearchQuery,
        filteredAssignees,
        createQuickCreatedTask,
        handleQuickCreatedTaskDateChange,
        handleQuickCreatedTaskUrgencyChange,
        focusQuickCreatedTaskDate,
        handleQuickCreatedTaskProjectChange,
        handleQuickCreatedTaskAssigneeChange,
        loadProjectAssignees,
        handleSelectAllQuickTaskAssignees,
        statsTaskDialog,
        handleStatsItemClick,
        handleStatsTaskPageChange,
        handleStatsTaskPageSizeChange
    };
} 