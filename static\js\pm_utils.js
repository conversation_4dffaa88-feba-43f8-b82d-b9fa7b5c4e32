// 项目管理工具函数库

/**
 * 初始化项目数据和工具函数
 * @param {Object} initData 初始化数据
 * @param {Object} Vue Vue实例
 * @param {Object} ElementPlus ElementPlus实例
 * @returns {Object} 包含各种工具函数和状态的对象
 */
export function initializeProjectManagement(initData, Vue, ElementPlus) {
    const { ref, computed } = Vue;

    // 用户信息
    const userInfo = ref(initData.userData);

    // 项目相关
    const projects = ref(initData.projectsData.map(project => ({
        ...project,
        member_count: project.member_count || 0
    })));
    const currentProject = ref(initData.currentProjectData);
    const projectSearch = ref('');
    const manageProjectSearch = ref(''); // 项目管理视图的搜索关键字

    // 视图相关
    const currentView = ref(initData.viewTypeData);
    const searchQuery = ref(initData.searchQueryData);
    const tasks = ref(initData.tasksData);
    const tableLoading = ref(false);

    // 导航菜单
    const navItems = ref([
        { label: '首页', value: 'home', icon: 'House', badge: 0 },
        // { label: '项目统计', value: 'stats', icon: 'DataAnalysis' },
        { label: '任务统计', value: 'task_stats', icon: 'PieChart' },
        { label: '项目管理', value: 'manage', icon: 'Management' },
        { label: '模板管理', value: 'templates', icon: 'Files' },
        // { label: '任务审批', value: 'approval', icon: 'Stamp', badge: 0 },
        { label: '搜索任务', value: 'search', icon: 'Search' },
        { label: '我的关注', value: 'received', icon: 'Star' },
        { label: '已完成的任务', value: 'completed', icon: 'Check' }

    ]);

    // 任务抽屉
    const drawer = ref({
        visible: false,
        loading: false,
        error: null,
        task: null,
        taskId: null,
        title: '任务详情',
        projectName: '',
        replyMessage: ''
    });
    
    // 项目管理弹窗
    const projectDialog = ref({
        visible: false,
        loading: false,
        title: '项目详情',
        project: {},
        members: [],
        availableUsers: [],
        canManageMembers: false,
        newMember: {
            userId: '',
            role: 'member'
        },
        deleteConfirmName: ''
    });

    // 过滤后的项目列表
    const filteredProjects = computed(() => {
        if (!projectSearch.value) return projects.value;
        const search = projectSearch.value.toLowerCase();
        return projects.value.filter(p => 
            p.name.toLowerCase().includes(search)
        );
    });
    
    // 可管理的项目列表（超级管理员可见所有项目，普通用户只能看到自己是管理员的项目）
    const filteredManageableProjects = computed(() => {
        // 超级管理员可以管理所有项目
        if (userInfo.value.role === '超级管理员') {
            return projects.value;
        }
        
        // 普通用户只能看到自己是管理员的项目
        return projects.value.filter(project => {
            // 检查用户在项目中的角色
            return project.user_role === '管理员' || project.user_role === 'admin';
        });
    });
    
    // 用于项目管理视图的搜索功能，根据名称和描述进行过滤
    const filteredManageProjects = computed(() => {
        if (!manageProjectSearch.value) return filteredManageableProjects.value;
        
        const search = manageProjectSearch.value.toLowerCase();
        return filteredManageableProjects.value.filter(project => 
            project.name.toLowerCase().includes(search) || 
            (project.description && project.description.toLowerCase().includes(search))
        );
    });

    // 页面标题
    const pageTitle = computed(() => {
        if (currentView.value === 'search') {
            return searchQuery.value ? `搜索结果: ${searchQuery.value}` : "搜索任务";
        } else if (currentView.value === 'completed') {
            return "已完成的任务";
        } else if (currentView.value === 'created') {
            return "我发起的任务";
        } else if (currentView.value === 'received') {
            return "抄送给我的任务";
        } else if (currentView.value === 'manage') {
            return "项目管理";
        } else if (currentView.value === 'stats') {
            return "项目统计";
        } else if (currentView.value === 'templates') {
            return "模板管理";
        } else if (currentView.value === 'template_details') {
            return "模板详情";
        } else if (currentView.value === 'approval') {
            return "任务审批";
        } else if (currentView.value === 'home') {
            return ""; // Empty string for home view
        } else if (currentProject.value && currentProject.value.id) {
            return "项目统计";
        } else {
            return "无可见项目";
        }
    });
    
    // 打开项目管理弹窗
    const openProjectManagement = (project) => {
        projectDialog.value.visible = true;
        projectDialog.value.loading = true;
        
        // 确保project对象包含所有必要字段
        projectDialog.value.project = {
            id: project.id,
            name: project.name,
            description: project.description || '暂无项目描述',
            member_count: project.member_count || 0,
            // 添加编辑用的字段
            editName: project.name,
            editDescription: project.description || ''
        };
        
        projectDialog.value.title = `项目管理：${project.name}`;
        
        // 加载项目成员
        loadProjectMembers(project.id);
        
        // 加载可用用户列表（用于添加成员）
        loadAvailableUsers(project.id);

        // 加载项目的删除审核设置
        try {
            if (window.taskDeleteApproval && typeof window.taskDeleteApproval.getProjectDeleteSettings === 'function') {
                window.taskDeleteApproval.getProjectDeleteSettings(project.id);
            }
        } catch (error) {
            console.error('加载项目删除审核设置失败:', error);
        }
    };
    
    // 加载项目成员
    const loadProjectMembers = (projectId) => {
        fetch(`/projectmanagement/api/project_members?project_id=${projectId}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '获取项目成员失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 更新项目成员列表
                    projectDialog.value.members = data.members || [];
                    projectDialog.value.canManageMembers = data.can_manage || false;
                    
                    // 更新项目信息
                    if (data.project) {
                        projectDialog.value.project = {
                            ...projectDialog.value.project,
                            ...data.project
                        };
                        
                        // 更新项目列表中的成员数量
                        const index = projects.value.findIndex(p => p.id === projectId);
                        if (index !== -1) {
                            projects.value[index].member_count = data.project.member_count || 0;
                        }
                    }
                } else {
                    throw new Error(data.message || '获取项目成员失败');
                }
            })
            .catch(error => {
                console.error('加载项目成员失败:', error);
                ElementPlus.ElMessage({
                    message: `加载项目成员失败: ${error.message}`,
                    type: 'error'
                });
            })
            .finally(() => {
                projectDialog.value.loading = false;
            });
    };
    
    // 加载可用用户列表
    const loadAvailableUsers = (projectId) => {
        if (!projectId) {
            console.error('加载可用用户失败: 缺少项目ID');
            return;
        }
        
        fetch(`/projectmanagement/api/available_users?project_id=${projectId}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '获取可用用户失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    projectDialog.value.availableUsers = data.users || [];
                } else {
                    throw new Error(data.message || '获取可用用户失败');
                }
            })
            .catch(error => {
                console.error('加载可用用户失败:', error);
                ElementPlus.ElMessage({
                    message: `加载可用用户失败: ${error.message}`,
                    type: 'error'
                });
            });
    };

    return {
        userInfo,
        projects,
        currentProject,
        projectSearch,
        manageProjectSearch,
        filteredProjects,
        filteredManageableProjects,
        filteredManageProjects,
        currentView,
        searchQuery,
        tasks,
        tableLoading,
        navItems,
        drawer,
        pageTitle,
        projectDialog,
        openProjectManagement,
        loadProjectMembers,
        loadAvailableUsers
    };
} 