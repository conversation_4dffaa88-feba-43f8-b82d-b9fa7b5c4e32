<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% endblock %}</title>
{#    <link rel="preload" href="{{ url_for('static', filename='fonts/AlibabaPuHuiTi-3-55-Regular.woff2') }}" as="font" type="font/woff2" crossorigin>#}
    <link rel="icon" type="image/png" href="{{ cdn_static('images/logo_mini.png') }}">
    <link href="{{ cdn_static('css/tailwind.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ cdn_static('css/element-plus.css') }}" />
    <script src="{{ cdn_static('js/vue.global.prod.js') }}"></script>
    <script src="{{ cdn_static('js/element-plus.js') }}"></script>
    <script src="{{ cdn_static('js/element-plus-icons-vue.js') }}"></script>
    <link href="{{ cdn_static('css/base_layout_common.css') }}" rel="stylesheet">
    <!-- 添加移动端样式 -->
    <link href="{{ cdn_static('css/mobile_layout.css') }}" rel="stylesheet">
    {% if enable_difys %}
    <style>
  #dify-chatbot-bubble-button {
    background-color: #1C64F2 !important;
  }
  #dify-chatbot-bubble-window {
    width: 24rem !important;
    height: 40rem !important;
  }
  
  /* 在移动端设备上隐藏difys小蓝点 */
  @media (max-width: 1023px) {
    #dify-chatbot-bubble-button {
      display: none !important;
    }
  }
</style>
<script>
 window.difyChatbotConfig = {
  token: 'FcVhNrTxOlkBmkOT',
  baseUrl: '/difys_proxy',
  systemVariables: {
    // user_id: 'YOU CAN DEFINE USER ID HERE',
  },
 }
</script>
<script
 src="/difys_proxy/embed.min.js"
 id="FcVhNrTxOlkBmkOT"
 defer>
</script>
{% endif %}
    {% block extra_css %}{% endblock %}
    
    <!-- 水印样式 -->
    <style>
        .watermark-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9999;
            overflow: hidden;
            opacity: 0.08;
        }
        .watermark-item {
            position: absolute;
            user-select: none;
            white-space: nowrap;
            color: #000;
            font-size: 16px;
            transform: rotate(-30deg);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div id="app">
        <div class="main-wrapper">
            <!-- 移除顶部导航栏，改为浮动菜单按钮 -->
            <div class="mobile-menu-toggle" id="menuToggle">
                <div class="menu-toggle">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="nav-arrow">
                        <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                </div>
            </div>

            <!-- 移动端侧边导航抽屉 -->
            <div class="mobile-nav-drawer" id="mobileNavDrawer">
                <div class="mobile-nav-header">
                    <!-- 移除logo，直接使用用户头像和名字 -->
                    <div class="mobile-nav-user-info">
                        <img class="mobile-nav-user-avatar" 
                             src="{{ user_info.avatarUrl if user_info and user_info.avatarUrl else 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHJ4PSI4IiBmaWxsPSIjRTVFN0VCIi8+PHBhdGggZD0iTTIwIDIwQzIyLjc2MTQgMjAgMjUgMTcuNzYxNCAyNSAxNUMyNSAxMi4yMzg2IDIyLjc2MTQgMTAgMjAgMTBDMTcuMjM4NiAxMCAxNSAxMi4yMzg2IDE1IDE1QzE1IDE3Ljc2MTQgMTcuMjM4NiAyMCAyMCAyMFoiIGZpbGw9IiM5NDk0OTQiLz48cGF0aCBkPSJNMjAgMjIuNUMxNS44NTc5IDIyLjUgMTIuNSAyNS44NTc5IDEyLjUgMzBIMjcuNUMyNy41IDI1Ljg1NzkgMjQuMTQyMSAyMi41IDIwIDIyLjVaIiBmaWxsPSIjOTQ5NDk0Ii8+PC9zdmc+' }}" 
                             alt="用户头像"
                             title="{{ user_info.username if user_info else '访客' }}">
                        <div class="mobile-nav-username">{{ user_info.nick if user_info else '访客' }}</div>
                    </div>
                    <div class="mobile-nav-close" id="mobileNavClose">&times;</div>
                </div>
                
                <!-- 移动端一级导航菜单 -->
                <div class="mobile-nav-menu">
                    {% for nav_item in nav_items %}
                        {% if not nav_item.mobile_hidden %}
                        <div class="mobile-nav-item {% if (request.path == nav_item.url) or (nav_item.is_ai_hub and request.path.startswith('/ai_hub')) or (nav_item.url == url_for('project_management_route') and request.path.startswith('/project')) %}active{% endif %}"
                             data-url="{{ nav_item.url }}"
                             data-ai-hub="{{ 'true' if nav_item.is_ai_hub else 'false' }}"
                             data-coming-soon="{{ 'true' if nav_item.is_coming_soon else 'false' }}"
                             data-has-children="{{ 'true' if nav_item.url == url_for('project_management_route') or nav_item.url == url_for('reward_coin_index') or (nav_item.is_ai_hub) else 'false' }}"
                             data-module-id="{{ 'project_management' if nav_item.url == url_for('project_management_route') else ('reward_coin' if nav_item.url == url_for('reward_coin_index') else ('ai_hub' if nav_item.is_ai_hub else '')) }}"
                             data-message="{{ nav_item.coming_soon_message if nav_item.coming_soon_message else '功能开发中，敬请期待！' }}">
                            <div class="mobile-nav-item-content">
                                <img src="{{ cdn_static(nav_item.icon) }}"
                                     alt="{{ nav_item.title }}"
                                     class="mobile-nav-icon">
                                <span class="mobile-nav-title">{{ nav_item.title }}</span>
                                <div class="mobile-nav-arrow">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="9 18 15 12 9 6"></polyline>
                                    </svg>
                                </div>
                            </div>

                            <!-- 二级导航容器 -->
                            <div class="mobile-subnav-container">
                                <!-- 二级导航项将通过JavaScript动态添加 -->
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
                
                <div class="mobile-nav-footer">
                    <form method="POST" action="{{ url_for('logout') }}" class="logout-form">
                        <button type="submit" class="mobile-nav-logout">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                <polyline points="16 17 21 12 16 7"></polyline>
                                <line x1="21" y1="12" x2="9" y2="12"></line>
                            </svg>
                            <span>退出登录</span>
                        </button>
                    </form>
                    <!-- <div class="icp-info" style="font-size: 12px; color: rgba(255, 255, 255, 0.65); margin: 10px; text-align: center;">
                        粤ICP备2024267547号-2
                    </div> -->
                </div>
            </div>
            
            <!-- 移动端导航遮罩层 -->
            <div class="mobile-nav-overlay" id="mobileNavOverlay"></div>

            <!-- 桌面端左侧导航 -->
            <div class="left-nav desktop-only">
                <div class="nav-user">
                    <img class="nav-user-avatar" 
                         src="{{ user_info.avatarUrl if user_info and user_info.avatarUrl else 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHJ4PSI4IiBmaWxsPSIjRTVFN0VCIi8+PHBhdGggZD0iTTIwIDIwQzIyLjc2MTQgMjAgMjUgMTcuNzYxNCAyNSAxNUMyNSAxMi4yMzg2IDIyLjc2MTQgMTAgMjAgMTBDMTcuMjM4NiAxMCAxNSAxMi4yMzg2IDE1IDE1QzE1IDE3Ljc2MTQgMTcuMjM4NiAyMCAyMCAyMFoiIGZpbGw9IiM5NDk0OTQiLz48cGF0aCBkPSJNMjAgMjIuNUMxNS44NTc5IDIyLjUgMTIuNSAyNS44NTc5IDEyLjUgMzBIMjcuNUMyNy41IDI1Ljg1NzkgMjQuMTQyMSAyMi41IDIwIDIyLjVaIiBmaWxsPSIjOTQ5NDk0Ii8+PC9zdmc+' }}" 
                         alt="用户头像"
                         title="{{ user_info.username if user_info else '访客' }}"
                         onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHJ4PSI4IiBmaWxsPSIjRTVFN0VCIi8+PHBhdGggZD0iTTIwIDIwQzIyLjc2MTQgMjAgMjUgMTcuNzYxNCAyNSAxNUMyNSAxMi4yMzg2IDIyLjc2MTQgMTAgMjAgMTBDMTcuMjM4NiAxMCAxNSAxMi4yMzg2IDE1IDE1QzE1IDE3Ljc2MTQgMTcuMjM4NiAyMCAyMCAyMFoiIGZpbGw9IiM5NDk0OTQiLz48cGF0aCBkPSJNMjAgMjIuNUMxNS44NTc5IDIyLjUgMTIuNSAyNS44NTc5IDEyLjUgMzBIMjcuNUMyNy41IDI1Ljg1NzkgMjQuMTQyMSAyMi41IDIwIDIyLjVaIiBmaWxsPSIjOTQ5NDk0Ii8+PC9zdmc+';">
                </div>
                <div class="nav-items">
                    {% for nav_item in nav_items %}
                        <div class="nav-line {% if (request.path == nav_item.url) or (nav_item.is_ai_hub and request.path.startswith('/ai_hub')) or (nav_item.url == url_for('project_management_route') and request.path.startswith('/project')) %}active{% endif %}"
                             data-url="{{ nav_item.url }}"
                             data-ai-hub="{{ 'true' if nav_item.is_ai_hub else 'false' }}"
                             data-coming-soon="{{ 'true' if nav_item.is_coming_soon else 'false' }}"
                             data-message="{{ nav_item.coming_soon_message if nav_item.coming_soon_message else '功能开发中，敬请期待！' }}"
                             onclick="handleNavClick(this)">
                            <div class="nav-item">
                                <img src="{{ cdn_static(nav_item.icon) }}"
                                     alt="{{ nav_item.title }}"
                                     class="nav-icon">
                                {# <span class="nav-tooltip">{{ nav_item.title }}</span> #}
                            </div>
                            <div class="nav_item-title">{{ nav_item.title }}</div>
                        </div>
                    {% endfor %}
                </div>
                <div class="nav-bottom">
                    <form method="POST" action="{{ url_for('logout') }}" class="logout-form">
                        <button type="submit" class="nav-item">
                            <i class="menu-icon logout-icon"></i>
                            <span class="nav-tooltip">退出登录</span>
                        </button>
                    </form>
                    <!-- <div class="icp-info" style="font-size: 12px; color: rgba(255, 255, 255, 0.65); margin: 10px; text-align: center;">
                        粤ICP备2024267547号-2
                    </div> -->
                </div>
            </div>

            <div class="main-content">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <div id="watermark-container" class="watermark-container"></div>

    <script src="{{ cdn_static('js/base_layout_common.js') }}"></script>
    <!-- 添加移动端导航脚本 -->
    <script src="{{ cdn_static('js/mobile_navigation.js') }}"></script>
    {% block extra_js %}{% endblock %}
    <script>
        // 添加显示消息的函数
        function showMessage(message) {
            ElementPlus.ElMessage({
                message: message,
                type: 'info'
            });
        }
        
        // 水印实现
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('watermark-container');
            const username = "{{ user_info.nick if user_info else '访客' }}";
            const watermarkText = username;
            const density = 1.2; // 水印密度调整为1.2
            
            // 计算窗口尺寸和水印个数
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const itemWidth = 300; // 增加每个水印的间距
            const itemHeight = 200; // 增加每个水印的间距
            
            // 计算需要的水印行列数
            const cols = Math.ceil(windowWidth * density / itemWidth);
            const rows = Math.ceil(windowHeight * density / itemHeight);
            
            // 创建水印
            for (let i = 0; i < rows; i++) {
                for (let j = 0; j < cols; j++) {
                    const watermark = document.createElement('div');
                    watermark.className = 'watermark-item';
                    watermark.innerText = watermarkText;
                    watermark.style.left = (j * itemWidth / density) + 'px';
                    watermark.style.top = (i * itemHeight / density) + 'px';
                    container.appendChild(watermark);
                }
            }
            
            // 监听窗口大小变化，重新生成水印
            window.addEventListener('resize', function() {
                container.innerHTML = '';
                const newWindowWidth = window.innerWidth;
                const newWindowHeight = window.innerHeight;
                
                const newCols = Math.ceil(newWindowWidth * density / itemWidth);
                const newRows = Math.ceil(newWindowHeight * density / itemHeight);
                
                for (let i = 0; i < newRows; i++) {
                    for (let j = 0; j < newCols; j++) {
                        const watermark = document.createElement('div');
                        watermark.className = 'watermark-item';
                        watermark.innerText = watermarkText;
                        watermark.style.left = (j * itemWidth / density) + 'px';
                        watermark.style.top = (i * itemHeight / density) + 'px';
                        container.appendChild(watermark);
                    }
                }
            });
            
            // 防止水印被篡改
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
                        // 如果水印被移除，重新添加
                        if (!document.getElementById('watermark-container') || 
                            document.getElementById('watermark-container').children.length === 0) {
                            location.reload();
                        }
                    }
                });
            });
            
            // 监视整个document以防止水印被删除
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    </script>
</body>
</html> 