from flask import render_template, request, redirect, url_for, session, flash, jsonify, current_app
import datetime
import json
import pymysql
import os
from project_management_statistics import init_project_management_statistics
from project_management_members import init_project_management_members
from project_management_projects import init_project_management_projects
from project_management_tasks import init_project_management_tasks
from project_management_templates import init_project_management_templates
from project_management_task_delete_approval import init_project_management_task_delete_approval
# 导入钉钉消息发送函数
from dingtalk_send_msg import send_task_assigned_message, get_access_token, send_task_cc_message
from logger_config import setup_logger
logger = setup_logger()
# 用户角色常量
ROLE_SUPER_ADMIN = '超级管理员'
ROLE_ADMIN = '管理员'
ROLE_NORMAL = '普通人'
ROLE_MARGINAL = '边缘人'

# 紧急程度常量
URGENCY_URGENT = '紧急'
URGENCY_NORMAL = '一般'

def init_project_management(app, mysql=None, employee_db=None):
    """初始化项目管理模块，注册路由并配置MySQL"""
    
    # 如果未提供mysql实例，创建一个
    if mysql is None:
        from flask_mysqldb import MySQL
        # 配置MySQL
        app.config['MYSQL_HOST'] = '************'
        app.config['MYSQL_PORT'] = 3306
        app.config['MYSQL_USER'] = 'user1'
        app.config['MYSQL_PASSWORD'] = 'Yx123456.'
        app.config['MYSQL_DB'] = 'database'
        app.config['MYSQL_CURSORCLASS'] = 'DictCursor'
        app.config['MYSQL_INIT_COMMAND'] = "SET time_zone = '+08:00'"  # 设置时区为北京时间
        
        # 初始化MySQL
        mysql = MySQL(app)
    
    # 初始化统计相关的路由
    init_project_management_statistics(app, mysql)
    
    # 获取redis_manager
    redis_manager = app.config.get('redis_manager')
    
    # 初始化各个模块，传递redis_manager
    init_project_management_members(app, mysql, employee_db, redis_manager)
    init_project_management_projects(app, mysql)
    init_project_management_tasks(app, mysql, employee_db)
    init_project_management_templates(app, mysql)
    init_project_management_task_delete_approval(app, mysql, employee_db)
    # 初始化员工数据库连接
    if employee_db is None:
        # 创建到员工数据库的连接
        employee_db_config = {
            'host': '************',
            'port': 3306,
            'user': 'root',
            'password': 'Yx123456.',
            'database': 'personnel_administration',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        # 创建连接函数，以便需要时创建新连接
        def get_employee_db_connection():
            return pymysql.connect(**employee_db_config)
    
    # 辅助函数：根据employee_id查询员工信息
    def get_employee_by_id(employee_id):
        """从员工数据库获取特定员工信息"""
        try:
            # 创建新连接
            conn = get_employee_db_connection()
            with conn.cursor() as cursor:
                # 获取特定员工信息
                cursor.execute("SELECT * FROM employee_roster_cut WHERE userid = %s", (employee_id,))
                employee = cursor.fetchone()
            conn.close()
            return employee
        except Exception as e:
            print(f"获取员工数据错误: {str(e)}")
            return None
            
    # 以下是原有辅助函数
    def get_user_by_id(user_id):
        cur = mysql.connection.cursor()
        cur.execute("SELECT * FROM project_management_t_users WHERE id = %s", [user_id])
        user = cur.fetchone()
        cur.close()
        return user

    def get_user_projects(user_id):
        """获取用户可见的项目列表"""
        cur = mysql.connection.cursor()
        
        # 获取所有项目及用户在项目中的角色
        cur.execute("""
            SELECT p.*, up.role as user_role
            FROM project_management_t_projects p
            LEFT JOIN project_management_t_user_projects up ON p.id = up.project_id AND up.user_id = %s
            WHERE p.id = 1 OR up.user_id = %s OR EXISTS (
                SELECT 1 FROM project_management_t_users WHERE id = %s AND role = %s
            )
        """, [user_id, user_id, user_id, ROLE_SUPER_ADMIN])
        
        projects = cur.fetchall()
        cur.close()
        
        # 处理项目数据，添加角色信息
        result = []
        for project in projects:
            # 检查是否为超级管理员
            if get_user_role(user_id) == ROLE_SUPER_ADMIN:
                role_info = None
                user_role = 'superadmin'  # 超级管理员在所有项目中都是超级管理员
            # 对于项目ID为1的主项目，所有用户默认为普通成员
            elif project['id'] == 1 and not project['user_role']:
                role_info = None
                user_role = 'member'  # 默认为普通成员
            # 检查是否为项目管理员
            elif project['user_role'] == ROLE_ADMIN:
                role_info = None
                user_role = 'admin'
            # 检查是否为边缘人
            elif project['user_role'] == ROLE_MARGINAL:
                # 检查是否为边缘人
                role_info = '成员权限'
                user_role = 'marginal'
            # 普通成员
            elif project['user_role'] == ROLE_NORMAL:
                role_info = None
                user_role = 'member'
            else:
                role_info = None
                user_role = project['user_role'] or 'member'  # 默认为普通成员
                
            project_info = {
                'id': project['id'],
                'name': project['name'],
                'description': project['description'],
                'role_info': role_info,
                'user_role': user_role  # 添加用户在项目中的角色
            }
            result.append(project_info)
            
        return result

    def get_user_role(user_id):
        """获取用户的全局角色"""
        user = get_user_by_id(user_id)
        return user['role'] if user else None

    def get_user_project_role(user_id, project_id):
        """获取用户在特定项目中的角色"""
        cur = mysql.connection.cursor()
        cur.execute("""
            SELECT role FROM project_management_t_user_projects 
            WHERE user_id = %s AND project_id = %s
        """, [user_id, project_id])
        result = cur.fetchone()
        cur.close()
        
        if result:
            return result['role']
        elif get_user_role(user_id) == ROLE_SUPER_ADMIN:
            return ROLE_SUPER_ADMIN
        elif project_id == 1:  # 对于ID为1的主项目，所有用户默认为普通人角色
            return ROLE_NORMAL
        else:
            return None

    def get_project_tasks(project_id, user_id=None):
        """获取项目中的任务列表"""
        cur = mysql.connection.cursor()
        
        # 检查用户是否为超级管理员、项目管理员或普通成员
        is_admin = False
        is_marginal = False
        
        if user_id:
            # 检查是否为超级管理员
            user_role = get_user_role(user_id)
            if user_role == ROLE_SUPER_ADMIN:
                is_admin = True
            else:
                # 检查用户在项目中的角色
                project_role = get_user_project_role(user_id, project_id)
                if project_role == ROLE_ADMIN:
                    is_admin = True
                elif project_role == ROLE_MARGINAL:
                    is_marginal = True
        
        # 获取请求中的月份参数
        month = request.args.get('month')
        month_condition = ""
        month_params = []
        
        if month:
            try:
                # 如果提供了月份参数，添加日期筛选条件
                month_condition = "AND DATE_FORMAT(t.deadline, '%%Y-%%m') = %s"
                month_params = [month]
            except Exception as e:
                print(f"Error processing month parameter: {str(e)}")
                month_condition = ""
                month_params = []
        # 获取请求中的创建月份参数
        create_month = request.args.get('create_month')
        if create_month:
            # 如果提供了月份参数，添加日期筛选条件
            month_condition += " AND DATE_FORMAT(t.created_at, '%%Y-%%m') = %s "
            month_params += [create_month]
        #状态筛选
        status = request.args.get('status')
        if status == 'overdue': #逾期未完成
            month_condition += " AND t.deadline < CURRENT_DATE() AND t.completed_date IS NULL "
        elif status == 'overdue_completed': #逾期完成
            month_condition += " AND t.completed_date IS NOT NULL AND DATE(t.completed_date) > t.deadline "
        elif status == 'in_progress': #进行中
            month_condition += " AND t.completed_date IS NULL AND (t.deadline IS NULL OR t.deadline >= CURRENT_DATE()) "
        elif status == 'on_time_completed': #按时完成
            month_condition += " AND t.completed_date IS NOT NULL AND (t.deadline IS NULL OR DATE(t.completed_date) <= t.deadline) "
        elif status == 'due_today': #今日到期
            month_condition += " AND DATE(t.deadline) = CURRENT_DATE() AND t.completed_date IS NULL "
        elif status == 'completed': #已完成
            month_condition += " AND t.completed_date IS NOT NULL "
        elif status == 'incomplete': #未完成
            month_condition += " AND t.completed_date IS NULL "

        search_term = request.args.get('search_term', '')
        if search_term:
            search_term = f"%{search_term}%"
            month_params +=[search_term, search_term, search_term, search_term]
            month_condition += f"""   AND ( 
                       t.name LIKE %s OR  
                       t.description LIKE %s OR 
                       u.name LIKE %s OR 
                       assignee.name LIKE %s 
                   ) """

        # 获取所有任务 或者 只获取边缘人负责的任务
        if is_marginal:
            # 对边缘人只获取其负责的任务
            query = """
                SELECT DISTINCT  t.*, p.name as parent_name, u.name as creator_name,
                       CASE 
                           WHEN t.parent_id IS NULL THEN t.id 
                           ELSE t.parent_id 
                       END as sort_group,
                       CASE 
                           WHEN t.parent_id IS NULL THEN 0
                           ELSE 1
                       END as is_child
                FROM project_management_t_tasks t
                LEFT JOIN project_management_t_tasks p ON t.parent_id = p.id
                LEFT JOIN project_management_t_users u ON t.creator_id = u.id 
                 LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id  
                LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id  
                WHERE t.project_id = %s
                AND t.id IN (
                    SELECT task_id FROM project_management_t_task_assignees
                    WHERE user_id = %s
                )
            """ + month_condition + """
            ORDER BY sort_group DESC, is_child, t.created_at DESC
        """
            cur.execute(query, [project_id, user_id] + month_params)
        else:
            # 非边缘人可以看到所有任务
            query = """
                SELECT DISTINCT t.*, p.name as parent_name, u.name as creator_name,
                       CASE 
                           WHEN t.parent_id IS NULL THEN t.id 
                           ELSE t.parent_id 
                       END as sort_group,
                       CASE 
                           WHEN t.parent_id IS NULL THEN 0
                           ELSE 1
                       END as is_child
                FROM project_management_t_tasks t
                LEFT JOIN project_management_t_tasks p ON t.parent_id = p.id
                LEFT JOIN project_management_t_users u ON t.creator_id = u.id
                LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id  
                LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id 
                WHERE t.project_id = %s
            """ + month_condition + """
            ORDER BY sort_group DESC, is_child, t.created_at DESC
        """
            cur.execute(query, [project_id] + month_params)
        
        tasks = cur.fetchall()
        
        # 获取每个任务的负责人
        task_list = []
        for task in tasks:
            # 获取任务负责人
            cur.execute("""
                SELECT u.name, u.id
                FROM project_management_t_task_assignees ta
                JOIN project_management_t_users u ON ta.user_id = u.id
                WHERE ta.task_id = %s
            """, [task['id']])
            
            assignees = cur.fetchall()
            assignees_names = ', '.join([a['name'] for a in assignees])
            
            # 检查当前用户是否是负责人之一
            is_assignee = False
            if user_id:
                for assignee in assignees:
                    if assignee['id'] == user_id:
                        is_assignee = True
                        break
            
            # 检查当前用户是否是创建者
            is_creator = task['creator_id'] == user_id
            
            # 检查用户是否可以完成任务 (管理员/超管/负责人/发起人)
            can_complete = is_creator or is_assignee or is_admin
            
            # 准备任务数据
            task_data = {
                'id': task['id'],
                'project_id': task['project_id'],  # 添加project_id
                'name': task['name'],
                'description': task['description'],
                'deadline': task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else None,
                'completed_date': task['completed_date'].strftime('%Y-%m-%d') if task['completed_date'] else None,
                'urgency': task['urgency'],
                'is_child': task['parent_id'] is not None,
                'parent_id': task['parent_id'],
                'assignees': assignees_names,
                'creator_name': task['creator_name'],
                'can_complete': can_complete,
                'is_creator': is_creator,
                'is_assignee': is_assignee,
                'created_at': task['created_at'].strftime('%Y-%m-%d %H:%M:%S') if task['created_at'] else None  # 移除时间增量
            }
            task_list.append(task_data)
        
        cur.close()
        return task_list

    def get_task_logs(task_id):
        """获取任务的沟通记录和日志"""
        cur = mysql.connection.cursor()
        cur.execute("""
            SELECT l.*, u.name as sender_name
            FROM project_management_t_task_logs l
            LEFT JOIN project_management_t_users u ON l.user_id = u.id
            WHERE l.task_id = %s
            ORDER BY l.created_at DESC
        """, [task_id])
        
        logs = cur.fetchall()
        cur.close()
        
        result = []
        for log in logs:
            # 移除时间增量，直接使用数据库时间
            beijing_time = log['created_at'] 
            
            log_data = {
                'id': log['id'],
                'sender': log['sender_name'] if log['user_id'] else '系统',
                'time': beijing_time.strftime('%m-%d %H:%M') if beijing_time else '未知时间',
                'message': log['message'],
                'type': 'log'  # 添加类型标识，这是一条日志
            }
            result.append(log_data)
            
        return result
    
    def get_task_replies(task_id):
        """获取任务的回复记录"""
        cur = mysql.connection.cursor()
        cur.execute("""
            SELECT r.*, u.name as sender_name
            FROM project_management_t_task_replies r
            JOIN project_management_t_users u ON r.user_id = u.id
            WHERE r.task_id = %s
            ORDER BY r.created_at DESC
        """, [task_id])
        
        replies = cur.fetchall()
        cur.close()
        
        result = []
        for reply in replies:
            # 移除时间增量，直接使用数据库时间
            beijing_time = reply['created_at']
            
            reply_data = {
                'id': reply['id'],
                'sender': reply['sender_name'],
                'time': beijing_time.strftime('%m-%d %H:%M') if beijing_time else '未知时间',
                'message': reply['reply_content'],
                'type': 'reply'  # 添加类型标识，这是一条回复
            }
            result.append(reply_data)
            
        return result
    
    def get_task_communication(task_id):
        """获取任务的所有沟通记录（日志和回复）"""
        logs = get_task_logs(task_id)
        replies = get_task_replies(task_id)
        
        # 合并日志和回复，并按时间排序
        communication = logs + replies
        communication.sort(key=lambda x: x['time'], reverse=True)
        
        return communication

    def get_task_details(task_id):
        """获取任务详情"""
        cur = mysql.connection.cursor()
        cur.execute("""
            SELECT t.*, u.name as creator_name, p.name as project_name
            FROM project_management_t_tasks t
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id
            JOIN project_management_t_projects p ON t.project_id = p.id
            WHERE t.id = %s
        """, [task_id])
        task = cur.fetchone()
        
        if not task:
            cur.close()
            return None
        
        # 获取任务负责人
        cur.execute("""
            SELECT u.name
            FROM project_management_t_task_assignees ta
            JOIN project_management_t_users u ON ta.user_id = u.id
            WHERE ta.task_id = %s
        """, [task_id])
        
        assignees = cur.fetchall()
        assignees_names = ','.join([a['name'] for a in assignees])
        
        # 获取任务抄送人
        cur.execute("""
            SELECT u.id as userid, u.name
            FROM project_management_t_task_cc_users tc
            JOIN project_management_t_users u ON tc.user_id = u.id
            WHERE tc.task_id = %s
        """, [task_id])
        
        cc_users = cur.fetchall()
        cc_users_names = ','.join([a['name'] for a in cc_users])
        # 获取任务沟通记录（日志和回复）
        communication = get_task_communication(task_id)
        
        # 处理日期，确保截止日期正确显示
        deadline = None
        if task['deadline']:
            # 数据库中存储的是日期，不含时区信息，已经是正确的日期
            deadline = task['deadline'].strftime('%Y-%m-%d')
            
        completed_date = None
        if task['completed_date']:
            # 完成日期同样是日期，不含时区信息
            completed_date = task['completed_date'].strftime('%Y-%m-%d')
            
        # 格式化创建时间
        created_at = None
        if task['created_at']:
            # 移除时间增量，直接使用数据库时间
            beijing_time = task['created_at'] 
            created_at = beijing_time.strftime('%Y-%m-%d %H:%M')
        
        # 父任务名称
        parentTaskName = None
        if task['parent_id']:
            cur.execute("""
                    SELECT *
                    FROM project_management_t_tasks
                    WHERE id = %s
                """, [task['parent_id']])
            parentTask = cur.fetchone()
            if parentTask:
                parentTaskName=parentTask['name']
        
        task_data = {
            'id': task['id'],
            'project_id': task['project_id'],
            'name': task['name'],
            'description': task['description'],
            'deadline': deadline,
            'completed_date': completed_date,
            'urgency': task['urgency'],
            'assignees': assignees_names,
            'creator_name': task['creator_name'],
            'logs': communication,  # 现在包含了日志和回复
            'is_child': task['parent_id'] is not None,
            'parent_id': task['parent_id'],
            'created_at': created_at,  # 添加创建时间
            'project_name': task['project_name'],  # 添加项目名称
            'parentTaskName': parentTaskName,  # 添加父任务名称
            'cc_users': cc_users_names if cc_users_names else  "无"  # 添加抄送人列表
        }
        
        cur.close()
        return task_data

    def get_user_created_tasks(user_id):
        """获取用户创建的任务"""
        cur = mysql.connection.cursor()
        
        # 获取所有用户创建的任务，跨项目
        cur.execute("""
            SELECT t.*, p.name as project_name, u.name as creator_name
            FROM project_management_t_tasks t
            JOIN project_management_t_projects p ON t.project_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id
            WHERE t.creator_id = %s
            ORDER BY t.created_at DESC
        """, [user_id])
        
        tasks = cur.fetchall()
        
        # 获取用户角色信息
        user_role = get_user_role(user_id)
        is_super_admin = (user_role == ROLE_SUPER_ADMIN)
        
        # 获取每个任务的负责人
        task_list = []
        for task in tasks:
            # 获取任务负责人
            cur.execute("""
                SELECT u.name, u.id
                FROM project_management_t_task_assignees ta
                JOIN project_management_t_users u ON ta.user_id = u.id
                WHERE ta.task_id = %s
            """, [task['id']])
            
            assignees = cur.fetchall()
            assignees_names = ', '.join([a['name'] for a in assignees])
            
            # 检查是否是项目管理员
            project_role = get_user_project_role(user_id, task['project_id'])
            is_project_admin = (project_role == ROLE_ADMIN)
            
            # 准备任务数据
            task_data = {
                'id': task['id'],
                'name': task['name'],
                'description': task['description'],
                'deadline': task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else None,
                'completed_date': task['completed_date'].strftime('%Y-%m-%d') if task['completed_date'] else None,
                'created_at': task['created_at'].strftime('%Y-%m-%d %H:%M') if task['created_at'] else None,
                'urgency': task['urgency'],
                'project_name': task['project_name'],
                'assignees': assignees_names,
                'creator_name': task['creator_name'],
                'can_complete': True,  # 发起人可以完成任务
                'project_id': task['project_id']
            }
            task_list.append(task_data)
        
        cur.close()
        return task_list
        
    def get_user_assigned_tasks(user_id):
        """获取分配给用户的任务"""
        cur = mysql.connection.cursor()
        
        # 获取所有分配给用户的任务，跨项目
        cur.execute("""
            SELECT t.*, p.name as project_name, u.name as creator_name
            FROM project_management_t_tasks t
            JOIN project_management_t_projects p ON t.project_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id
            JOIN project_management_t_task_assignees ta ON t.id = ta.task_id AND ta.user_id = %s
            ORDER BY t.created_at DESC
        """, [user_id])
        
        tasks = cur.fetchall()
        
        # 获取用户角色信息
        user_role = get_user_role(user_id)
        is_super_admin = (user_role == ROLE_SUPER_ADMIN)
        
        # 获取每个任务的负责人
        task_list = []
        for task in tasks:
            # 获取任务负责人
            cur.execute("""
                SELECT u.name, u.id
                FROM project_management_t_task_assignees ta
                JOIN project_management_t_users u ON ta.user_id = u.id
                WHERE ta.task_id = %s
            """, [task['id']])
            
            assignees = cur.fetchall()
            assignees_names = ', '.join([a['name'] for a in assignees])
            
            # 检查是否是项目管理员
            project_role = get_user_project_role(user_id, task['project_id'])
            is_project_admin = (project_role == ROLE_ADMIN)
            
            # 准备任务数据
            task_data = {
                'id': task['id'],
                'name': task['name'],
                'description': task['description'],
                'deadline': task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else None,
                'completed_date': task['completed_date'].strftime('%Y-%m-%d') if task['completed_date'] else None,
                'created_at': task['created_at'].strftime('%Y-%m-%d %H:%M') if task['created_at'] else None,
                'urgency': task['urgency'],
                'project_name': task['project_name'],
                'assignees': assignees_names,
                'creator_name': task['creator_name'],
                'can_complete': True,  # 任务负责人可以完成任务
                'project_id': task['project_id']
            }
            task_list.append(task_data)
        
        cur.close()
        return task_list

    def get_completed_tasks(user_id=None, page=1, page_size=14):
        """获取所有已完成的任务（带分页）

        规则：
        1. 超级管理员可以查看全部数据
        2. 管理员或普通人可以查看自己所在的所有项目数据
        3. 边缘人只能看自己的数据
        """
        import time
        start_time = time.time()
        logger.info(f"开始获取已完成任务（分页） - 页码: {page}, 每页大小: {page_size}")
        
        cur = mysql.connection.cursor()

        # 获取用户角色信息
        role_start_time = time.time()
        user_role = None
        is_super_admin = False
        if user_id:
            user_role = get_user_role(user_id)
            is_super_admin = (user_role == ROLE_SUPER_ADMIN)
        role_end_time = time.time()
        logger.info(f"获取用户角色耗时: {(role_end_time - role_start_time)*1000:.2f}ms")

        # 基础查询
        params_start_time = time.time()
        base_query = """
            FROM project_management_t_tasks t
            JOIN project_management_t_projects p ON t.project_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id 
            LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id 
            LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id 
            WHERE t.completed_date IS NOT NULL 
        """
        params = []

        # 处理搜索条件
        search_term = request.args.get('search_term', '')
        if search_term:
            search_term = f"%{search_term}%"
            params = [search_term, search_term, search_term, search_term]
            base_query += f"""   
                AND ( 
                    t.name LIKE %s OR  
                    t.description LIKE %s OR 
                    u.name LIKE %s OR 
                    assignee.name LIKE %s 
                ) 
            """

        # 处理权限过滤
        if user_id and not is_super_admin:
            # 获取用户是边缘人的所有项目ID
            cur.execute("""
                SELECT project_id 
                FROM project_management_t_user_projects 
                WHERE user_id = %s AND role = %s
            """, [user_id, ROLE_MARGINAL])

            marginal_projects = cur.fetchall()
            is_marginal_in_projects = {p['project_id'] for p in marginal_projects}

            if is_marginal_in_projects:
                # 边缘人: 只能看到自己负责的任务
                base_query += """ 
                    AND (
           
                        t.id IN (
                            SELECT task_id FROM project_management_t_task_assignees
                            WHERE user_id = %s
                        )
                         OR 
                         t.creator_id =%s
                    )
                """
                params.append(user_id)
                params.append(user_id)
            else:
                # 管理员或普通人: 可以看到自己所在项目的所有数据
                base_query += """ 
                    AND (
                        t.project_id = 1 OR  -- 主项目所有人可见
                        t.project_id IN (
                            SELECT project_id FROM project_management_t_user_projects 
                            WHERE user_id = %s
                        )
                    )
                """
                params.append(user_id)

        # 处理状态筛选
        status = request.args.get('status')
        if status == 'overdue_completed':  # 逾期完成
            base_query += " AND t.completed_date IS NOT NULL AND DATE(t.completed_date) > t.deadline "
        elif status == 'on_time_completed':  # 按时完成
            base_query += " AND t.completed_date IS NOT NULL AND (t.deadline IS NULL OR DATE(t.completed_date) <= t.deadline) "
        
        # 处理月份筛选
        # month = request.args.get('month')
        # if month:
        #     base_query += " AND DATE_FORMAT(t.completed_date, '%%Y-%%m') = %s "
        #     params.append(month)
        params_end_time = time.time()
        logger.info(f"处理请求参数耗时: {(params_end_time - params_start_time)*1000:.2f}ms")
        
        # 计算总记录数
        count_start_time = time.time()
        count_query = f"SELECT COUNT(DISTINCT t.id) as total {base_query}"
        cur.execute(count_query, params)
        total_count = cur.fetchone()['total']
        count_end_time = time.time()
        logger.info(f"计算总记录数耗时: {(count_end_time - count_start_time)*1000:.2f}ms, 总记录数: {total_count}")
        
        # 计算分页参数
        page = int(page)
        page_size = int(page_size)
        offset = (page - 1) * page_size

        # 获取分页数据
        data_start_time = time.time()
        data_query = f"""
            SELECT DISTINCT t.*, p.name as project_name, u.name as creator_name
            {base_query}
            ORDER BY t.completed_date DESC
            LIMIT %s OFFSET %s
        """
        cur.execute(data_query, params + [page_size, offset])
        tasks = cur.fetchall()
        data_end_time = time.time()
        logger.info(f"获取分页数据耗时: {(data_end_time - data_start_time)*1000:.2f}ms, 获取到 {len(tasks)} 条记录")

        # 获取每个任务的负责人并处理结果
        task_list = []
        detail_start_time = time.time()
        for task in tasks:
            # 获取任务负责人
            cur.execute("""
                SELECT u.name, u.id
                FROM project_management_t_task_assignees ta
                JOIN project_management_t_users u ON ta.user_id = u.id
                WHERE ta.task_id = %s
            """, [task['id']])

            assignees = cur.fetchall()
            assignees_names = ', '.join([a['name'] for a in assignees])

            # 检查当前用户是否是负责人之一
            is_assignee = False
            if user_id:
                for assignee in assignees:
                    if assignee['id'] == user_id:
                        is_assignee = True
                        break

            # 检查当前用户是否是创建者
            is_creator = user_id and task['creator_id'] == user_id

            # 检查是否是项目管理员
            is_project_admin = False
            if user_id:
                project_role = get_user_project_role(user_id, task['project_id'])
                is_project_admin = (project_role == ROLE_ADMIN)

            # 检查用户是否可以完成任务 (管理员/超管/负责人/发起人)
            can_complete = is_creator or is_assignee or is_super_admin or is_project_admin

            # 准备任务数据
            task_data = {
                'id': task['id'],
                'name': task['name'],
                'description': task['description'],
                'deadline': task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else None,
                'completed_date': task['completed_date'].strftime('%Y-%m-%d') if task['completed_date'] else None,
                'created_at': task['created_at'].strftime('%Y-%m-%d %H:%M') if task['created_at'] else None,
                'urgency': task['urgency'],
                'project_name': task['project_name'],
                'assignees': assignees_names,
                'creator_name': task['creator_name'],
                'can_complete': can_complete,
                'project_id': task['project_id']
            }
            task_list.append(task_data)
        detail_end_time = time.time()
        logger.info(f"处理任务详情耗时: {(detail_end_time - detail_start_time)*1000:.2f}ms")

        cur.close()
        
        end_time = time.time()
        logger.info(f"获取已完成任务总耗时: {(end_time - start_time)*1000:.2f}ms")

        # 返回任务列表和分页信息
        return {
            'tasks': task_list,
            'pagination': {
                'total': total_count,
                'page': page,
                'page_size': page_size
            }
        }

    def search_tasks(query, user_id, page=1, page_size=14):
        """搜索任务
        Args:
            query: 搜索关键词
            user_id: 当前用户ID
            page: 页码（从1开始）
            page_size: 每页记录数
        Returns:
            包含匹配的任务列表和分页信息的字典
        """
        import time
        start_time = time.time()
        logger.info(f"开始搜索任务 - 关键词: {query}, 用户ID: {user_id}, 页码: {page}, 每页大小: {page_size}")
        
        cur = mysql.connection.cursor()
        
        # 获取用户角色信息
        role_start_time = time.time()
        user_role = get_user_role(user_id)
        is_super_admin = (user_role == ROLE_SUPER_ADMIN)
        role_end_time = time.time()
        logger.info(f"获取用户角色耗时: {(role_end_time - role_start_time)*1000:.2f}ms")
        
        # 构建搜索条件
        params_start_time = time.time()
        search_term = f"%{query}%"
        
        # 基础查询条件
        base_query = """
            FROM project_management_t_tasks t
            JOIN project_management_t_projects p ON t.project_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id
            LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id
            LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id
            WHERE (
                t.name LIKE %s OR 
                t.description LIKE %s OR
                u.name LIKE %s OR
                assignee.name LIKE %s
            )
        """
        
        # 权限过滤条件
        if not is_super_admin:
            base_query += """
                AND (
                    t.creator_id = %s OR  -- 是任务创建者
                    ta.user_id = %s OR    -- 是任务负责人
                    EXISTS (              -- 是项目管理员
                        SELECT 1 FROM project_management_t_user_projects up 
                        WHERE up.project_id = t.project_id 
                        AND up.user_id = %s 
                        AND up.role = 'admin'
                    ) OR
                    EXISTS (             -- 是项目普通成员（非边缘人）
                        SELECT 1 FROM project_management_t_user_projects up 
                        WHERE up.project_id = t.project_id 
                        AND up.user_id = %s 
                        AND up.role NOT IN ('marginal')
                    )
                )
            """
            params = [search_term, search_term, search_term, search_term, user_id, user_id, user_id, user_id]
        else:
            params = [search_term, search_term, search_term, search_term]
            

        # 先计算总记录数
        count_start_time = time.time()
        count_query = f"SELECT COUNT(DISTINCT t.id) as total {base_query}"
        cur.execute(count_query, params)
        total_count = cur.fetchone()['total']
        count_end_time = time.time()
        logger.info(f"计算总记录数耗时: {(count_end_time - count_start_time)*1000:.2f}ms, 总记录数: {total_count}")
        
        # 计算分页参数
        page = int(page)
        page_size = int(page_size)
        offset = (page - 1) * page_size
        
        # 获取分页数据
        data_start_time = time.time()
        data_query = f"""
            SELECT DISTINCT t.*, p.name as project_name, u.name as creator_name
            {base_query}
            ORDER BY t.created_at DESC
            LIMIT %s OFFSET %s
        """
        cur.execute(data_query, params + [page_size, offset])
        tasks = cur.fetchall()
        data_end_time = time.time()
        logger.info(f"获取分页数据耗时: {(data_end_time - data_start_time)*1000:.2f}ms, 获取到 {len(tasks)} 条记录")
        
        # 获取每个任务的负责人
        task_list = []
        detail_start_time = time.time()
        for task in tasks:
            # 获取任务负责人
            cur.execute("""
                SELECT u.name, u.id
                FROM project_management_t_task_assignees ta
                JOIN project_management_t_users u ON ta.user_id = u.id
                WHERE ta.task_id = %s
            """, [task['id']])
            
            assignees = cur.fetchall()
            assignees_names = ', '.join([a['name'] for a in assignees])
            
            # 检查当前用户是否是负责人之一
            is_assignee = False
            for assignee in assignees:
                if assignee['id'] == user_id:
                    is_assignee = True
                    break
            
            # 检查当前用户是否是创建者
            is_creator = task['creator_id'] == user_id
            
            # 检查是否是项目管理员
            project_role = get_user_project_role(user_id, task['project_id'])
            is_project_admin = (project_role == ROLE_ADMIN)
            
            # 检查用户是否可以完成任务 (管理员/超管/负责人/发起人)
            can_complete = is_creator or is_assignee or is_super_admin or is_project_admin
            
            # 准备任务数据
            task_data = {
                'id': task['id'],
                'name': task['name'],
                'description': task['description'],
                'deadline': task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else None,
                'completed_date': task['completed_date'].strftime('%Y-%m-%d') if task['completed_date'] else None,
                'created_at': task['created_at'].strftime('%Y-%m-%d %H:%M') if task['created_at'] else None,
                'urgency': task['urgency'],
                'project_name': task['project_name'],
                'assignees': assignees_names,
                'creator_name': task['creator_name'],
                'can_complete': can_complete,
                'project_id': task['project_id']
            }
            task_list.append(task_data)
        detail_end_time = time.time()
        logger.info(f"处理任务详情耗时: {(detail_end_time - detail_start_time)*1000:.2f}ms")
        
        cur.close()
        
        end_time = time.time()
        logger.info(f"搜索任务总耗时: {(end_time - start_time)*1000:.2f}ms")
        
        # 返回任务列表和分页信息
        return {
            'tasks': task_list,
            'pagination': {
                'total': total_count,
                'page': page,
                'page_size': page_size
            }
        }

    @app.route('/projectmanagement')
    def project_index():
        if 'user_id' in session:
            return redirect(url_for('project_main'))
        return redirect(url_for('project_login'))

    @app.route('/projectmanagement/login', methods=['GET', 'POST'])
    def project_login():
        if request.method == 'POST':
            user_id = request.form['user_id']
            
            # 检查用户是否存在
            user = get_user_by_id(user_id)
            
            if user:
                # 用户存在，直接登录
                session['user_id'] = user['id']
                session['user_name'] = user['name']
                session['user_role'] = user['role']
                return redirect(url_for('project_main'))
            else:
                # 用户不存在，从员工数据库获取用户信息并创建账号
                try:
                    # 获取员工信息
                    employee = get_employee_by_id(user_id)
                    
                    if employee:
                        # 用户在员工数据库中存在，创建用户
                        cur = mysql.connection.cursor()
                        try:
                            cur.execute("""
                                INSERT INTO project_management_t_users (id, name, role)
                                VALUES (%s, %s, %s)
                            """, [user_id, employee.get('sys00_name'), '普通人'])
                            
                            mysql.connection.commit()
                            
                            # 创建用户成功后登录
                            session['user_id'] = user_id
                            session['user_name'] = employee.get('sys00_name')
                            session['user_role'] = '普通人'
                            
                            return redirect(url_for('project_main'))
                        except Exception as db_error:
                            # 数据库错误处理
                            return render_template('login.html', error=f'创建用户账号失败: {str(db_error)}')
                        finally:
                            cur.close()
                    else:
                        # 用户不在员工数据库中
                        return render_template('login.html', error='找不到该员工信息，请确认工号是否正确')
                except Exception as e:
                    # 处理其他异常
                    return render_template('login.html', error=f'系统错误，请联系管理员: {str(e)}')
        
        return render_template('login.html')

    @app.route('/projectmanagement/logout')
    def project_logout():
        session.clear()
        return redirect(url_for('project_login'))

    @app.route('/projectmanagement/main')
    def project_main():
        # 检查用户是否已登录
        if 'user_id' not in session:
            # 尝试从主应用的 token 中恢复用户信息
            try:
                from flask import current_app
                # 获取主应用中的session token
                session_token = session.get('token')
                
                # 如果没有token，重定向到登录页
                if not session_token:
                    return redirect(url_for('login'))
                
                # 使用主应用的验证函数验证token
                user_info = None
                if hasattr(current_app, 'config') and 'redis_manager' in current_app.config:
                    # 导入需要的模块
                    from app import verify_token_and_ua
                    
                    # 验证token
                    user_info = verify_token_and_ua(session_token, request.headers.get('User-Agent'))
                
                # 如果无法获取用户信息，重定向到登录页
                if not user_info:
                    return redirect(url_for('login'))
                
                # 从用户信息中获取userid并设置session
                userid = user_info.get('userid')
                if not userid:
                    return redirect(url_for('login'))
                
                # 从数据库获取用户角色
                user_role = '普通人'  # 默认角色
                user_name = user_info.get('nick', '未命名用户')
                
                try:
                    with mysql.connection.cursor() as cursor:
                        # 查询用户在项目管理系统中的角色
                        cursor.execute("SELECT role FROM project_management_t_users WHERE id = %s", [userid])
                        user_result = cursor.fetchone()
                        
                        if user_result:
                            user_role = user_result['role']
                        else:
                            # 在项目管理系统中创建用户
                            cursor.execute("""
                                INSERT INTO project_management_t_users (id, name, role, created_at)
                                VALUES (%s, %s, %s, NOW())
                                ON DUPLICATE KEY UPDATE name = VALUES(name), role = VALUES(role)
                            """, [userid, user_name, user_role])
                            mysql.connection.commit()
                except Exception as e:
                    # 记录错误但继续使用默认角色
                    if hasattr(current_app, 'logger'):
                        current_app.logger.error(f"恢复用户角色时出错: {str(e)}")
                    print(f"恢复用户角色时出错: {str(e)}")
                
                # 设置项目管理系统所需的session
                session['user_id'] = userid
                session['user_name'] = user_name
                session['user_role'] = user_role
                
                print(f"从主应用token恢复用户信息: {userid}, {user_name}, {user_role}")
                
            except Exception as e:
                # 恢复失败，重定向到登录页
                if hasattr(current_app, 'logger'):
                    current_app.logger.error(f"从主应用token恢复用户信息失败: {str(e)}")
                print(f"从主应用token恢复用户信息失败: {str(e)}")
                return redirect(url_for('login'))
        
        user_id = session['user_id']
        user_name = session['user_name']
        user_role = session['user_role']
        
        # 获取视图类型
        view_type = request.args.get('view_type', 'home')
        
        # 获取用户可见的项目列表
        projects = get_user_projects(user_id)
        
        # 根据视图类型获取任务
        tasks = []
        totalTasks = 0
        current_project = None
        search_query = None
        
        # 处理不同视图类型
        if view_type == 'search':
            # 搜索任务视图
            search_query = request.args.get('query', '')
            if search_query:
                # 获取分页参数
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 14, type=int)
                result = search_tasks(search_query, user_id, page, page_size)
                tasks = result['tasks']
                totalTasks = result['pagination']['total']
            else:
                tasks = []
                totalTasks = 0
            title = f"搜索结果: {search_query}" if search_query else "搜索任务"
            
        elif view_type == 'completed':
            # 已完成的任务视图
            # 获取分页参数
            page = request.args.get('page', 1, type=int)
            page_size = request.args.get('page_size', 14, type=int)
            result = get_completed_tasks(user_id, page, page_size)
            tasks = result['tasks']
            totalTasks = result['pagination']['total']
            title = "已完成的任务"
            
        elif view_type == 'created':
            # 我发起的任务视图
            tasks = get_user_created_tasks(user_id)
            title = "我发起的任务"
            
        # elif view_type == 'received':
        #     # 我收到的任务视图
        #     tasks = get_user_assigned_tasks(user_id)
        #     title = "我收到的任务"
            
        else:
            # 项目任务视图（默认视图 stats 或 manage）
            # 获取当前选中的项目
            project_name = request.args.get('project')
            project_id = request.args.get('project_id')
            
            # 优先使用项目名称查找
            if project_name:
                # 根据名称查找项目
                for p in projects:
                    if p['name'] == project_name:
                        current_project = p
                        break
            # 如果没找到，尝试使用项目ID
            elif project_id:
                # 根据ID查找项目
                for p in projects:
                    if str(p['id']) == project_id:
                        current_project = p
                        break
            
            # 如果没有指定项目或指定的项目不可见，则选择第一个可见项目
            # if not current_project and projects:
            #     current_project = projects[0]
            
            # 如果有可见项目，获取项目中的任务
            if current_project:
                # 获取分页参数
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 14, type=int)
                result= get_project_tasks_paginated(current_project['id'], user_id,page,page_size)
                tasks=result['tasks']
                totalTasks=result['pagination']['total']
                title = f"项目任务: {current_project['name']}"
            else:
                title = "无可见项目"
        
        # 如果没有选中的项目，则创建一个空的项目对象
        if not current_project:
            current_project = {'id': None, 'name': title}
        
        # 获取选中的任务
        selected_task_id = request.args.get('task_id')
        selected_task = None
        
        if selected_task_id:
            # 在任务列表中寻找选中的任务
            for task in tasks:
                if str(task['id']) == selected_task_id:
                    selected_task = get_task_details(task['id'])
                    break
        
        return render_template('project_management_main.html', 
                            user_name=user_name,
                            user_role=user_role,
                            projects=projects,
                            tasks=tasks,
                            totalTasks=totalTasks,
                            current_project=current_project,
                            selected_task=selected_task,
                            selected_task_id=selected_task_id,
                            view_type=view_type,
                            search_query=search_query,
                            title=title)

    

    # 添加新的API路由，用于获取不同视图的任务数据
    @app.route('/projectmanagement/api/view')
    def project_api_view():
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        user_id = session['user_id']
        
        # 获取视图类型
        view_type = request.args.get('view_type', 'stats')
        
        # 根据视图类型获取任务
        tasks = []
        
        try:
            # 处理不同视图类型
            if view_type == 'search':
                # 搜索任务视图
                search_query = request.args.get('query', '')
                if search_query:
                    # 获取分页参数
                    page = request.args.get('page', 1, type=int)
                    page_size = request.args.get('page_size', 14, type=int)
                    result = search_tasks(search_query, user_id, page, page_size)
                    tasks = result['tasks']
                    pagination = result['pagination']
                    
                    return jsonify({
                        'success': True,
                        'tasks': tasks,
                        'pagination': pagination
                    })
                
            elif view_type == 'completed':
                # 已完成的任务视图
                # 获取分页参数
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 14, type=int)
                result = get_completed_tasks(user_id, page, page_size)
                tasks = result['tasks']
                pagination = result['pagination']
                
                return jsonify({
                    'success': True,
                    'tasks': tasks,
                    'pagination': pagination
                })
                
            elif view_type == 'created':
                # 我发起的任务视图
                tasks = get_user_created_tasks(user_id)
                
            elif view_type == 'received':
                # 我收到的任务视图
                tasks = get_user_assigned_tasks(user_id)
                
            else:
                # 项目任务视图（默认视图 stats 或 manage）
                # 获取当前选中的项目
                project_id = request.args.get('project_id')
                
                # 如果有项目ID，获取项目中的任务
                if project_id:
                    
                    # 获取当前项目信息
                    projects = get_user_projects(user_id)
                    current_project = None
                    
                    for p in projects:
                        if str(p['id']) == project_id:
                            current_project = p
                            break
                    
                    if current_project:
                            #tasks = get_project_tasks(project_id, user_id)
                        # 获取分页参数
                        page = request.args.get('page', 1, type=int)
                        page_size = request.args.get('page_size', 14, type=int)
                        result= get_project_tasks_paginated(project_id, user_id,page,page_size)
                        tasks=result['tasks']
                        totalTasks=result['pagination']['total']

                        return jsonify({
                            'success': True,
                            'tasks': tasks,
                            'totalTasks': totalTasks,
                            'current_project': current_project
                        })
            
            return jsonify({
                'success': True,
                'tasks': tasks
            })
            
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500
    
    def get_project_tasks_paginated(project_id, user_id=None, page=1, page_size=14):
        """获取项目中的任务列表（带分页）"""
        import time
        start_time = time.time()
        logger.info(f"开始执行分页查询 - 项目ID: {project_id}, 页码: {page}, 每页大小: {page_size}")
        
        cur = mysql.connection.cursor()
        
        # 检查用户是否为超级管理员、项目管理员或普通成员
        is_admin = False
        is_marginal = False
        
        role_start_time = time.time()
        if user_id:
            # 检查是否为超级管理员
            user_role = get_user_role(user_id)
            if user_role == ROLE_SUPER_ADMIN:
                is_admin = True
            else:
                # 检查用户在项目中的角色
                project_role = get_user_project_role(user_id, project_id)
                if project_role == ROLE_ADMIN:
                    is_admin = True
                elif project_role == ROLE_MARGINAL:
                    is_marginal = True
        role_end_time = time.time()
        logger.info(f"获取用户角色耗时: {(role_end_time - role_start_time)*1000:.2f}ms")
        
        # 获取请求中的月份参数
        params_start_time = time.time()
        month = request.args.get('month')
        month_condition = ""
        month_params = []
        
        if month:
            try:
                # 如果提供了月份参数，添加日期筛选条件
                month_condition = "AND DATE_FORMAT(t.deadline, '%%Y-%%m') = %s"
                month_params = [month]
            except Exception as e:
                print(f"Error processing month parameter: {str(e)}")
                month_condition = ""
                month_params = []
        # 获取请求中的创建月份参数
        create_month = request.args.get('create_month')
        if create_month:
            # 如果提供了月份参数，添加日期筛选条件
            month_condition += " AND DATE_FORMAT(t.created_at, '%%Y-%%m') = %s "
            month_params += [create_month]
        #状态筛选
        status = request.args.get('status')
        if status == 'overdue': #逾期未完成
            month_condition += " AND t.deadline < CURRENT_DATE() AND t.completed_date IS NULL "
        elif status == 'overdue_completed': #逾期完成
            month_condition += " AND t.completed_date IS NOT NULL AND DATE(t.completed_date) > t.deadline "
        elif status == 'in_progress': #进行中
            month_condition += " AND t.completed_date IS NULL AND (t.deadline IS NULL OR t.deadline >= CURRENT_DATE()) "
        elif status == 'on_time_completed': #按时完成
            month_condition += " AND t.completed_date IS NOT NULL AND (t.deadline IS NULL OR DATE(t.completed_date) <= t.deadline) "
        elif status == 'due_today': #今日到期
            month_condition += " AND DATE(t.deadline) = CURRENT_DATE() AND t.completed_date IS NULL "
        elif status == 'completed': #已完成
            month_condition += " AND t.completed_date IS NOT NULL "
        elif status == 'incomplete': #未完成
            month_condition += " AND t.completed_date IS NULL "

        search_term = request.args.get('search_term', '')
        if search_term:
            search_term = f"%{search_term}%"
            month_params +=[search_term, search_term, search_term, search_term]
            month_condition += f"""   AND ( 
                       t.name LIKE %s OR  
                       t.description LIKE %s OR 
                       u.name LIKE %s OR 
                       assignee.name LIKE %s 
                   ) """
        params_end_time = time.time()
        logger.info(f"处理请求参数耗时: {(params_end_time - params_start_time)*1000:.2f}ms")

        # 先计算总数量（不分页）
        count_start_time = time.time()
        count_query_base = """
            SELECT COUNT(DISTINCT t.id) as total
            FROM project_management_t_tasks t
            LEFT JOIN project_management_t_tasks p ON t.parent_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id 
            LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id  
            LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id  
            WHERE t.project_id = %s
        """
        
        # 获取所有任务 或者 只获取边缘人负责的任务
        if is_marginal:
            # 对边缘人只获取其负责的任务的总数
            count_query = count_query_base + """
                AND t.id IN (
                    SELECT task_id FROM project_management_t_task_assignees
                    WHERE user_id = %s
                )
            """ + month_condition
            cur.execute(count_query, [project_id, user_id] + month_params)
        else:
            # 非边缘人可以看到所有任务的总数
            count_query = count_query_base + month_condition
            cur.execute(count_query, [project_id] + month_params)
        
        total_count = cur.fetchone()['total']
        count_end_time = time.time()
        logger.info(f"计算总数量SQL耗时: {(count_end_time - count_start_time)*1000:.2f}ms, 总记录数: {total_count}")
        
        # 计算分页参数
        page = int(page)
        page_size = int(page_size)
        offset = (page - 1) * page_size
        
        # 构建主查询SQL（包含任务信息和负责人信息）
        data_start_time = time.time()
        
        # 使用GROUP_CONCAT直接获取负责人信息
        base_query = """
            SELECT 
                t.*,
                p.name as parent_name,
                u.name as creator_name,
                CASE 
                    WHEN t.parent_id IS NULL THEN t.id 
                    ELSE t.parent_id 
                END as sort_group,
                CASE 
                    WHEN t.parent_id IS NULL THEN 0
                    ELSE 1
                END as is_child,
                GROUP_CONCAT(DISTINCT assignee.name SEPARATOR ', ') as assignees_names,
                GROUP_CONCAT(DISTINCT assignee.id) as assignee_ids
            FROM project_management_t_tasks t
            LEFT JOIN project_management_t_tasks p ON t.parent_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id
            LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id  
            LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id 
            WHERE t.project_id = %s
        """
        
        if is_marginal:
            # 对边缘人只获取其负责的任务
            query = base_query + """
                AND t.id IN (
                    SELECT task_id FROM project_management_t_task_assignees
                    WHERE user_id = %s
                )
            """ + month_condition + """
            GROUP BY t.id
            ORDER BY sort_group DESC, is_child, t.created_at DESC
            LIMIT %s OFFSET %s
            """
            cur.execute(query, [project_id, user_id] + month_params + [page_size, offset])
        else:
            # 非边缘人可以看到所有任务
            query = base_query + month_condition + """
            GROUP BY t.id
            ORDER BY sort_group DESC, is_child, t.created_at DESC
            LIMIT %s OFFSET %s
            """
            cur.execute(query, [project_id] + month_params + [page_size, offset])
        
        tasks = cur.fetchall()
        data_end_time = time.time()
        logger.info(f"获取分页数据SQL耗时: {(data_end_time - data_start_time)*1000:.2f}ms, 获取到 {len(tasks)} 条记录")
        
        # 处理任务数据
        task_list = []
        process_start_time = time.time()
        for task in tasks:
            # 获取负责人ID列表
            assignee_ids = task['assignee_ids'].split(',') if task['assignee_ids'] else []
            
            # 检查当前用户是否是负责人之一
            is_assignee = False
            if user_id and assignee_ids:
                is_assignee = str(user_id) in assignee_ids
            
            # 检查当前用户是否是创建者
            is_creator = task['creator_id'] == user_id
            
            # 检查用户是否可以完成任务 (管理员/超管/负责人/发起人)
            can_complete = is_creator or is_assignee or is_admin
            
            # 准备任务数据
            task_data = {
                'id': task['id'],
                'project_id': task['project_id'],
                'name': task['name'],
                'description': task['description'],
                'deadline': task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else None,
                'completed_date': task['completed_date'].strftime('%Y-%m-%d') if task['completed_date'] else None,
                'urgency': task['urgency'],
                'is_child': task['parent_id'] is not None,
                'parent_id': task['parent_id'],
                'assignees': task['assignees_names'] or '',
                'creator_name': task['creator_name'],
                'can_complete': can_complete,
                'is_creator': is_creator,
                'is_assignee': is_assignee,
                'created_at': task['created_at'].strftime('%Y-%m-%d %H:%M:%S') if task['created_at'] else None
            }
            task_list.append(task_data)
        
        process_end_time = time.time()
        logger.info(f"处理任务数据耗时: {(process_end_time - process_start_time)*1000:.2f}ms")
        
        cur.close()
        
        end_time = time.time()
        total_time = end_time - start_time
        logger.info(f"分页查询总耗时: {total_time*1000:.2f}ms")
        
        # 返回分页数据和分页信息
        return {
            'tasks': task_list,
            'pagination': {
                'total': total_count,
                'page': page,
                'page_size': page_size
            }
        }

    # 修改后的API路由，使用分页函数
    @app.route('/projectmanagement/api/project_tasks')
    def project_api_project_tasks():
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        user_id = session['user_id']
        project_id = request.args.get('project_id')
        stats_only = request.args.get('stats', 'false').lower() == 'true'
        
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 14, type=int)
        
        if not project_id:
            return jsonify({'success': False, 'message': '缺少项目ID'}), 400
        
        try:
            tasks = []
            cur = mysql.connection.cursor()
            
            # 处理"全部项目"的情况
            if project_id == 'all':
                # 如果只需要统计信息，直接从数据库查询所有可见项目的任务状态
                if stats_only:
                    # 获取时间范围参数
                    start_date = request.args.get('start_date')
                    end_date = request.args.get('end_date')
                    
                    # 构建时间筛选条件
                    date_filter = ""
                    filter_params = []
                    
                    # 获取用户可见的所有项目ID
                    cur.execute("""
                        SELECT DISTINCT p.id
                        FROM project_management_t_projects p
                        LEFT JOIN project_management_t_user_projects up ON p.id = up.project_id
                        LEFT JOIN project_management_t_users u ON u.id = %s
                        WHERE up.user_id = %s OR u.role = '超级管理员'
                    """, [user_id, user_id])
                    
                    projects = cur.fetchall()
                    if not projects:
                        # 如果用户没有可见项目，返回空结果
                        return jsonify({
                            'success': True,
                            'stats': {
                                'total': 0,
                                'completed': 0,
                                'in_progress': 0,
                                'overdue': 0
                            }
                        })
                    
                    # 构建项目ID列表
                    project_ids = [p['id'] for p in projects]
                    project_ids_str = ','.join(['%s'] * len(project_ids))
                    filter_params.extend(project_ids)
                    
                    if start_date and end_date:
                        try:
                            start_date_obj = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
                            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
                            date_filter = "AND DATE(created_at) >= %s AND DATE(created_at) <= %s"
                            filter_params.extend([start_date_obj, end_date_obj])
                        except ValueError as e:
                            return jsonify({'success': False, 'message': f'日期格式错误: {str(e)}'}), 400
                    
                    # 直接查询所有任务状态，不考虑边缘人权限
                    cur.execute(f"""
                        SELECT 
                            COUNT(*) as total,
                            SUM(CASE WHEN completed_date IS NOT NULL THEN 1 ELSE 0 END) as completed,
                            SUM(CASE 
                                WHEN completed_date IS NULL AND deadline < CURDATE() THEN 1 
                                ELSE 0 
                            END) as overdue,
                            SUM(CASE 
                                WHEN completed_date IS NULL AND (deadline IS NULL OR deadline >= CURDATE()) THEN 1 
                                ELSE 0 
                            END) as in_progress
                        FROM project_management_t_tasks 
                        WHERE project_id IN ({project_ids_str}) {date_filter}
                    """, filter_params)
                    
                    result = cur.fetchone()
                    cur.close()
                    
                    stats = {
                        'total': result['total'] or 0,
                        'completed': result['completed'] or 0,
                        'in_progress': result['in_progress'] or 0,
                        'overdue': result['overdue'] or 0
                    }
                    
                    return jsonify({
                        'success': True,
                        'stats': stats
                    })
                else:
                    # 获取用户可见的所有项目
                    projects = get_user_projects(user_id)
                    
                    # 获取所有项目的任务
                    for project in projects:
                        project_tasks = get_project_tasks(project['id'], user_id)
                        tasks.extend(project_tasks)
            else:
                # 如果只需要统计信息，直接从数据库获取所有任务状态
                if stats_only:
                    # 获取时间范围参数
                    start_date = request.args.get('start_date')
                    end_date = request.args.get('end_date')
                    
                    # 构建时间筛选条件
                    date_filter = ""
                    filter_params = [project_id]
                    
                    if start_date and end_date:
                        try:
                            start_date_obj = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
                            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
                            date_filter = "AND DATE(created_at) >= %s AND DATE(created_at) <= %s"
                            filter_params.extend([start_date_obj, end_date_obj])
                        except ValueError as e:
                            return jsonify({'success': False, 'message': f'日期格式错误: {str(e)}'}), 400
                    
                    # 直接查询所有任务状态，不考虑用户权限
                    cur.execute(f"""
                        SELECT 
                            COUNT(*) as total,
                            SUM(CASE WHEN completed_date IS NOT NULL THEN 1 ELSE 0 END) as completed,
                            SUM(CASE 
                                WHEN completed_date IS NULL AND deadline < CURDATE() THEN 1 
                                ELSE 0 
                            END) as overdue,
                            SUM(CASE 
                                WHEN completed_date IS NULL AND (deadline IS NULL OR deadline >= CURDATE()) THEN 1 
                                ELSE 0 
                            END) as in_progress
                        FROM project_management_t_tasks 
                        WHERE project_id = %s {date_filter}
                    """, filter_params)
                    
                    result = cur.fetchone()
                    cur.close()
                    
                    stats = {
                        'total': result['total'] or 0,
                        'completed': result['completed'] or 0,
                        'in_progress': result['in_progress'] or 0,
                        'overdue': result['overdue'] or 0
                    }
                    
                    return jsonify({
                        'success': True,
                        'stats': stats
                    })
                else:
                    # 使用新的分页函数
                    result = get_project_tasks_paginated(project_id, user_id, page, page_size)
                    return jsonify({
                        'success': True,
                        'tasks': result['tasks'],
                        'pagination': result['pagination']
                    })
            
            return jsonify({
                'success': True,
                'tasks': tasks
            })
            
        except Exception as e:
            if 'cur' in locals():
                cur.close()
            return jsonify({'success': False, 'message': str(e)}), 500
    
    # 添加搜索任务的API路由
    @app.route('/projectmanagement/api/search')
    def project_api_search():
        """API endpoint for searching tasks"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        # 获取查询参数
        query = request.args.get('query', '')
        if not query:
            return jsonify(success=True, tasks=[], pagination={'total': 0, 'page': 1, 'page_size': 14})
        
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 14, type=int)
        
        # 获取搜索结果（带分页）
        result = search_tasks(query, session['user_id'], page, page_size)
        
        return jsonify(
            success=True, 
            tasks=result['tasks'],
            pagination=result['pagination']
        )
        
    @app.route('/projectmanagement/api/delete_task/<int:task_id>', methods=['DELETE'])
    def project_api_delete_task(task_id):
        """API endpoint for deleting a task"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录"), 401
        
        user_id = session['user_id']
        
        try:
            cur = mysql.connection.cursor()
            
            # 检查任务是否存在
            cur.execute("""
                SELECT t.*, p.id as project_id  
                FROM project_management_t_tasks t
                JOIN project_management_t_projects p ON t.project_id = p.id
                WHERE t.id = %s
            """, [task_id])
            
            task = cur.fetchone()
            if not task:
                cur.close()
                return jsonify(success=False, message="任务不存在"), 404
            
            # 检查用户是否有权限删除此任务
            # 允许任务创建者、项目管理员或超级管理员删除任务
            user_role = get_user_role(user_id)
            project_role = get_user_project_role(user_id, task['project_id'])
            
            is_super_admin = user_role == ROLE_SUPER_ADMIN
            is_project_admin = project_role == ROLE_ADMIN
            is_creator = task['creator_id'] == user_id
            
            if not (is_super_admin or is_project_admin or is_creator):
                cur.close()
                return jsonify(success=False, message="您没有权限删除此任务"), 403
            
            # 检查是否有子任务
            cur.execute("SELECT COUNT(*) as count FROM project_management_t_tasks WHERE parent_id = %s", [task_id])
            result = cur.fetchone()
            
            if result and result['count'] > 0:
                # 如果有子任务，删除所有子任务
                # 注：数据库中应该已经有级联删除的触发器，但为了确保安全，这里手动删除
                cur.execute("""
                    DELETE FROM project_management_t_task_assignees 
                    WHERE task_id IN (SELECT id FROM project_management_t_tasks WHERE parent_id = %s)
                """, [task_id])
                
                cur.execute("""
                    DELETE FROM project_management_t_task_logs
                    WHERE task_id IN (SELECT id FROM project_management_t_tasks WHERE parent_id = %s)
                """, [task_id])
                
                cur.execute("""
                    DELETE FROM project_management_t_task_replies
                    WHERE task_id IN (SELECT id FROM project_management_t_tasks WHERE parent_id = %s)
                """, [task_id])
                
                cur.execute("DELETE FROM project_management_t_tasks WHERE parent_id = %s", [task_id])
            
            # 删除任务的相关记录
            cur.execute("DELETE FROM project_management_t_task_assignees WHERE task_id = %s", [task_id])
            cur.execute("DELETE FROM project_management_t_task_logs WHERE task_id = %s", [task_id])
            cur.execute("DELETE FROM project_management_t_task_replies WHERE task_id = %s", [task_id])
            
            # 删除任务本身
            cur.execute("DELETE FROM project_management_t_tasks WHERE id = %s", [task_id])
            
            # 提交事务
            mysql.connection.commit()
            cur.close()
            
            return jsonify(success=True, message="任务已成功删除")
            
        except Exception as e:
            if 'cur' in locals():
                mysql.connection.rollback()
                cur.close()
            return jsonify(success=False, message=f"删除任务失败: {str(e)}"), 500
    
    @app.route('/projectmanagement/api/team_workload')
    def project_api_team_workload():
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        user_id = session['user_id']
        project_id = request.args.get('project_id')
        
        if not project_id:
            return jsonify({'success': False, 'message': '缺少项目ID'}), 400
        
        try:
            # 获取团队负载信息
            cur = mysql.connection.cursor()
            
            # 处理"全部项目"的情况
            if project_id == 'all':
                # 获取用户可访问的所有项目ID
                cur.execute("""
                    SELECT project_id
                    FROM project_management_t_user_projects
                    WHERE user_id = %s
                """, [user_id])
                
                project_ids = [row['project_id'] for row in cur.fetchall()]
                
                if not project_ids:
                    return jsonify({
                        'success': True,
                        'member_count': 0,
                        'avg_tasks_per_member': 0,
                        'highest_workload': {'name': '无', 'tasks': 0},
                        'lowest_workload': {'name': '无', 'tasks': 0}
                    })
                
                # 构建项目ID参数列表，用于SQL IN子句
                project_ids_str = ','.join(['%s'] * len(project_ids))
                
                # 获取所有项目的唯一成员数量
                cur.execute("""
                    SELECT COUNT(DISTINCT up.user_id) as member_count
                    FROM project_management_t_user_projects up
                    WHERE up.project_id IN ({})
                """.format(project_ids_str), project_ids)
                
                member_count_result = cur.fetchone()
                member_count = member_count_result['member_count'] if member_count_result else 0
                
                # 获取所有项目的总任务数
                cur.execute("""
                    SELECT COUNT(*) as task_count
                    FROM project_management_t_tasks
                    WHERE project_id IN ({})
                """.format(project_ids_str), project_ids)
                
                task_count_result = cur.fetchone()
                task_count = task_count_result['task_count'] if task_count_result else 0
                
                # 计算人均任务数
                avg_tasks_per_member = round(task_count / member_count, 1) if member_count > 0 else 0
                
                # 获取每个成员的任务数量
                cur.execute("""
                    SELECT u.name as member_name, COUNT(ta.task_id) as task_count
                    FROM (
                        SELECT DISTINCT user_id
                        FROM project_management_t_user_projects
                        WHERE project_id IN ({})
                    ) as unique_users
                    JOIN project_management_t_users u ON unique_users.user_id = u.id
                    LEFT JOIN project_management_t_task_assignees ta ON ta.user_id = u.id
                    LEFT JOIN project_management_t_tasks t ON ta.task_id = t.id AND t.project_id IN ({})
                    GROUP BY u.id, u.name
                    ORDER BY task_count DESC
                """.format(project_ids_str, project_ids_str), project_ids + project_ids)
            else:
                # 获取项目的成员数量
                cur.execute("""
                    SELECT COUNT(DISTINCT user_id) as member_count
                    FROM project_management_t_user_projects
                    WHERE project_id = %s
                """, [project_id])
                
                member_count_result = cur.fetchone()
                member_count = member_count_result['member_count'] if member_count_result else 0
                
                # 获取项目的任务数量
                cur.execute("""
                    SELECT COUNT(*) as task_count
                    FROM project_management_t_tasks
                    WHERE project_id = %s
                """, [project_id])
                
                task_count_result = cur.fetchone()
                task_count = task_count_result['task_count'] if task_count_result else 0
                
                # 计算人均任务数
                avg_tasks_per_member = round(task_count / member_count, 1) if member_count > 0 else 0
                
                # 获取每个成员的任务数量
                cur.execute("""
                    SELECT u.name as member_name, COUNT(ta.task_id) as task_count
                    FROM project_management_t_user_projects up
                    JOIN project_management_t_users u ON up.user_id = u.id
                    LEFT JOIN project_management_t_task_assignees ta ON ta.user_id = u.id
                    LEFT JOIN project_management_t_tasks t ON ta.task_id = t.id AND t.project_id = %s
                    WHERE up.project_id = %s
                    GROUP BY u.id, u.name
                    ORDER BY task_count DESC
                """, [project_id, project_id])
            
            member_tasks = cur.fetchall()
            
            # 获取负载最高和最低的成员
            highest_workload = {'name': '无', 'tasks': 0}
            lowest_workload = {'name': '无', 'tasks': 0}
            
            if member_tasks:
                highest_workload = {
                    'name': member_tasks[0]['member_name'],
                    'tasks': member_tasks[0]['task_count']
                }
                
                lowest_workload = {
                    'name': member_tasks[-1]['member_name'],
                    'tasks': member_tasks[-1]['task_count']
                }
            
            cur.close()
            
            return jsonify({
                'success': True,
                'member_count': member_count,
                'avg_tasks_per_member': avg_tasks_per_member,
                'highest_workload': highest_workload,
                'lowest_workload': lowest_workload
            })
            
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/projectmanagement/api/update_task/<int:task_id>', methods=['POST'])
    def project_api_update_task(task_id):
        """API endpoint for updating an existing task"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        user_id = session['user_id']
        
        # 获取请求数据
        data = request.json
        name = data.get('name')
        description = data.get('description', '')
        deadline = data.get('deadline')
        urgency = data.get('urgency', '一般')
        assignee_ids = data.get('assignee_ids', [])
        non_project_member_ids = data.get('non_project_member_ids', [])  # 新增：非项目成员ID列表
        cc_users = data.get('cc_users', [])  # 获取抄送人列表
        
        # 验证必填字段
        if not name:
            return jsonify(success=False, message="任务名称不能为空")
        
        try:
            cur = mysql.connection.cursor()
            
            # 检查任务是否存在
            cur.execute("""
                SELECT t.*, p.id as project_id 
                FROM project_management_t_tasks t
                JOIN project_management_t_projects p ON t.project_id = p.id
                WHERE t.id = %s
            """, [task_id])
            
            task = cur.fetchone()
            if not task:
                cur.close()
                return jsonify(success=False, message="任务不存在")
            
            project_id = task['project_id']
            
            # 检查用户是否有权限编辑此任务
            # 允许超级管理员、项目管理员、任务创建者和任务负责人编辑
            user_role = get_user_role(user_id)
            project_role = get_user_project_role(user_id, project_id)
            is_super_admin = user_role == ROLE_SUPER_ADMIN
            is_project_admin = project_role == ROLE_ADMIN
            
            # 检查是否是任务创建者
            is_creator = task['creator_id'] == user_id
            
            # 检查是否是任务负责人
            cur.execute("""
                SELECT COUNT(*) as count 
                FROM project_management_t_task_assignees 
                WHERE task_id = %s AND user_id = %s
            """, [task_id, user_id])
            
            is_assignee = cur.fetchone()['count'] > 0
            
            if not (is_super_admin or is_project_admin or is_creator or is_assignee):
                cur.close()
                return jsonify(success=False, message="您没有权限编辑此任务")
            
            # 如果有非项目成员被指定为负责人，先将他们添加为项目边缘人
            marginal_member_info = []  # 用于收集边缘人信息，以便后续添加日志
            
            if non_project_member_ids and len(non_project_member_ids) > 0:
                for member_id in non_project_member_ids:
                    # 检查用户在系统中是否存在
                    cur.execute("SELECT id FROM project_management_t_users WHERE id = %s", [member_id])
                    user = cur.fetchone()
                    
                    if not user:
                        # 需要先从员工数据库获取用户信息，并创建用户
                        employee = get_employee_by_id(member_id)
                        if employee:
                            # 创建用户
                            cur.execute("""
                                INSERT INTO project_management_t_users (id, name, role, created_at)
                                VALUES (%s, %s, %s, NOW())
                                ON DUPLICATE KEY UPDATE name = VALUES(name)
                            """, [member_id, employee.get('sys00_name'), ROLE_NORMAL])
                    
                    # 检查用户是否已经是项目成员
                    cur.execute("""
                        SELECT role FROM project_management_t_user_projects 
                        WHERE user_id = %s AND project_id = %s
                    """, [member_id, project_id])
                    
                    user_project = cur.fetchone()
                    was_added_as_marginal = False
                    
                    # 判断是否为ID为1的主项目，如果是则不需要添加用户为边缘人
                    if not user_project and int(project_id) != 1:
                        # 添加为项目边缘人
                        cur.execute("""
                            INSERT INTO project_management_t_user_projects (project_id, user_id, role)
                            VALUES (%s, %s, %s)
                        """, [project_id, member_id, ROLE_MARGINAL])
                        was_added_as_marginal = True
                    elif user_project and user_project['role'] != ROLE_MARGINAL:
                        # 如果用户已经是项目成员且不是边缘人，则不需要修改或记录
                        continue
                    
                    # 只有当用户是新添加的边缘人时，才收集信息以便添加日志
                    if was_added_as_marginal:
                        # 收集边缘人信息以便后续添加日志
                        member_name = str(member_id)
                        cur.execute("SELECT name FROM project_management_t_users WHERE id = %s", [member_id])
                        user_result = cur.fetchone()
                        if user_result:
                            member_name = user_result['name']
                        
                        marginal_member_info.append({
                            'id': member_id,
                            'name': member_name
                        })
            
            # 修复urgency字段可能的值：将'紧急'和'普通'转换为'紧急'和'一般'
            if urgency == '普通':
                urgency = '一般'
            
            # 获取旧的负责人列表（必须在删除旧负责人关联之前获取）
            cur.execute("""
                SELECT GROUP_CONCAT(u.name SEPARATOR ', ') as assignees
                FROM project_management_t_task_assignees ta
                JOIN project_management_t_users u ON ta.user_id = u.id
                WHERE ta.task_id = %s
            """, [task_id])
            old_assignees_result = cur.fetchone()
            old_assignees = old_assignees_result['assignees'] if old_assignees_result and old_assignees_result['assignees'] else '无'
            # 获取旧的负责人id
            cur.execute("""
                            SELECT user_id 
                            FROM project_management_t_task_assignees 
                            WHERE task_id = %s
                        """, [task_id])
            results = cur.fetchall()
            old_assignee_ids  = [row["user_id"] for row in results] if results else []
            
            # 获取旧的抄送人列表（必须在删除旧抄送人关联之前获取）
            cur.execute("""
                SELECT GROUP_CONCAT(u.name SEPARATOR ', ') as cc_users
                FROM project_management_t_task_cc_users tc
                JOIN project_management_t_users u ON tc.user_id = u.id
                WHERE tc.task_id = %s
            """, [task_id])
            old_cc_users_result = cur.fetchone()
            old_cc_users = old_cc_users_result['cc_users'] if old_cc_users_result and old_cc_users_result['cc_users'] else '无'
            
            # 获取旧的抄送人ID列表
            cur.execute("""
                SELECT user_id 
                FROM project_management_t_task_cc_users 
                WHERE task_id = %s
            """, [task_id])
            results = cur.fetchall()
            old_cc_user_ids = [row["user_id"] for row in results] if results else []
            
            # 更新任务
            cur.execute("""
                UPDATE project_management_t_tasks
                SET name = %s, description = %s, urgency = %s, deadline = %s
                WHERE id = %s
            """, [name, description, urgency, deadline, task_id])

            # 更新任务负责人
            # 先删除旧的负责人关联
            cur.execute("DELETE FROM project_management_t_task_assignees WHERE task_id = %s", [task_id])
            
            # 添加新的负责人关联
            if assignee_ids and len(assignee_ids) > 0:
                for assignee_id in assignee_ids:
                    cur.execute("""
                        INSERT INTO project_management_t_task_assignees (task_id, user_id)
                        VALUES (%s, %s)
                    """, [task_id, assignee_id])
            else:
                # 如果没有指定负责人，则创建者为负责人
                cur.execute("""
                    INSERT INTO project_management_t_task_assignees (task_id, user_id)
                    VALUES (%s, %s)
                """, [task_id, task['creator_id']])
            
            # 更新抄送人
            # 先删除旧的抄送人关联
            cur.execute("DELETE FROM project_management_t_task_cc_users WHERE task_id = %s", [task_id])
            
            # 添加新的抄送人关联
            if cc_users and len(cc_users) > 0:
                for cc_user in cc_users:
                    if 'userid' in cc_user:
                        cur.execute("""
                            INSERT INTO project_management_t_task_cc_users (task_id, user_id)
                            VALUES (%s, %s)
                        """, [task_id, cc_user['userid']])
            
            # 构建详细的编辑日志消息
            log_message_parts = []
            
            # 比较任务名称变更
            if task['name'] != name:
                log_message_parts.append(f"名称由「{task['name']}」修改为「{name}」")
            
            # 比较描述变更
            if task['description'] != description:
                if not task['description'] and description:
                    log_message_parts.append("添加了任务描述")
                elif task['description'] and not description:
                    log_message_parts.append("移除了任务描述")
                else:
                    log_message_parts.append("更新了任务描述")
            
            # 比较截止日期变更
            old_deadline = task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else '无'
            new_deadline = deadline if deadline else '无'
            if old_deadline != new_deadline:
                log_message_parts.append(f"截止日期由{old_deadline}修改为{new_deadline}")
            
            # 比较紧急程度变更
            old_urgency = task['urgency']
            if old_urgency != urgency:
                log_message_parts.append(f"紧急程度由「{old_urgency}」修改为「{urgency}」")
            
            # 获取新的负责人列表
            new_assignees = '无'
            if assignee_ids and len(assignee_ids) > 0:
                placeholders = ', '.join(['%s'] * len(assignee_ids))
                cur.execute(f"""
                    SELECT GROUP_CONCAT(name SEPARATOR ', ') as assignees
                    FROM project_management_t_users
                    WHERE id IN ({placeholders})
                """, assignee_ids)
                new_assignees_result = cur.fetchone()
                new_assignees = new_assignees_result['assignees'] if new_assignees_result and new_assignees_result['assignees'] else '无'
            
            # 比较负责人变更
            if old_assignees != new_assignees:
                log_message_parts.append(f"负责人由「{old_assignees}」修改为「{new_assignees}」")
                # 筛选新的负责人出来发送信息
                send_user_ids = []  # 默认不发送通知
                if assignee_ids:
                    # 如果前端传来了非空列表，计算新添加的人
                    send_user_ids = list(set(assignee_ids) - set(old_assignee_ids))

                else:  # 如果前端传来的 assignee_ids 是空列表
                    # 这表示清空了指派人
                    # 在这种情况下，通知创建者作为默认
                    send_user_ids = [task['creator_id']]
                # 如果负责人发生变更，发送钉钉通知给新的负责人
                if send_user_ids:
                    try:
                        # 获取钉钉配置信息
                        dingtalk_appkey = os.getenv('DINGTALK_APPKEY', 'ding6hyjguivqnldmwl2')
                        dingtalk_appsecret = os.getenv('DINGTALK_APPSECRET', 'o3EI-ke49Dl9ZQZake1ioHc7f0yMB4gxYjyLECNF8ZmkD9NzF_fJyMbUhUyN10N0')
                        
                        # 获取钉钉token
                        access_token = get_access_token(dingtalk_appkey, dingtalk_appsecret)
                        
                        if access_token:
                            # 获取项目名称
                            # project_name = task['project_name']
                            
                            # 获取创建者名称
                            creator_name = session.get('user_name', '未知用户')
                            
                            # 发送通知
                            send_task_assigned_message(
                                access_token=access_token,
                                appkey=dingtalk_appkey,
                                task_name=name,
                                creator=creator_name,
                                description=description or '无描述',
                                deadline=deadline,
                                user_ids=send_user_ids
                            )
                            print(f"已发送任务通知给新负责人: {assignee_ids}")
                        else:
                            print("获取钉钉token失败，无法发送任务通知")
                    except Exception as e:
                        print(f"发送钉钉通知失败: {str(e)}")
                        # 通知失败不影响任务更新，只记录日志
            
            # 获取新的抄送人列表
            new_cc_users = '无'
            if cc_users and len(cc_users) > 0:
                cc_user_ids = [cc_user['userid'] for cc_user in cc_users if 'userid' in cc_user]
                if cc_user_ids:
                    placeholders = ', '.join(['%s'] * len(cc_user_ids))
                    cur.execute(f"""
                        SELECT GROUP_CONCAT(name SEPARATOR ', ') as cc_users
                        FROM project_management_t_users
                        WHERE id IN ({placeholders})
                    """, cc_user_ids)
                    new_cc_users_result = cur.fetchone()
                    new_cc_users = new_cc_users_result['cc_users'] if new_cc_users_result and new_cc_users_result['cc_users'] else '无'
            
            # 比较抄送人变更
            if old_cc_users != new_cc_users:
                log_message_parts.append(f"抄送人由「{old_cc_users}」修改为「{new_cc_users}」")
                
                # 如果抄送人发生变化，发送钉钉通知给新增的抄送人
                ENABLE_CC = os.getenv('ENABLE_CC', 'false').lower() == 'true'
                if cc_users and len(cc_users) > 0 and ENABLE_CC:
                    # 计算新增的抄送人ID
                    cc_user_ids = [cc_user['userid'] for cc_user in cc_users if 'userid' in cc_user]
                    new_cc_user_ids = list(set(cc_user_ids) - set(old_cc_user_ids))
                    
                    if new_cc_user_ids:
                        try:
                            # 获取钉钉token
                            dingtalk_appkey = os.getenv('DINGTALK_APPKEY', 'ding6hyjguivqnldmwl2')
                            dingtalk_appsecret = os.getenv('DINGTALK_APPSECRET', 'o3EI-ke49Dl9ZQZake1ioHc7f0yMB4gxYjyLECNF8ZmkD9NzF_fJyMbUhUyN10N0')
                            access_token = get_access_token(dingtalk_appkey, dingtalk_appsecret)
                            
                            if access_token:
                                # 获取负责人和创建者名称
                                assignee_names = []
                                creator_name = "未知用户"
                                
                                # 先获取创建者名称
                                cur.execute("SELECT name FROM project_management_t_users WHERE id = %s", [task['creator_id']])
                                creator_result = cur.fetchone()
                                if creator_result:
                                    creator_name = creator_result['name']
                                    
                                # 获取负责人名称
                                if assignee_ids and len(assignee_ids) > 0:
                                    placeholders = ', '.join(['%s'] * len(assignee_ids))
                                    cur.execute(f"SELECT name FROM project_management_t_users WHERE id IN ({placeholders})", assignee_ids)
                                    assignee_names = [row['name'] for row in cur.fetchall()]
                                else:
                                    # 如果没有指定负责人，则使用创建者作为默认负责人
                                    if creator_result:
                                        assignee_names.append(creator_name)
                                
                                # 发送通知给新增的抄送人
                                send_task_cc_message(
                                    access_token=access_token,
                                    appkey=dingtalk_appkey,
                                    task_name=name,
                                    assigness_name=', '.join(assignee_names),
                                    creator_name=creator_name,
                                    cc_username=new_cc_users,  # 已从数据库查询的格式化字符串
                                    description=description or '无描述',
                                    deadline=deadline if deadline else "无截止日期",
                                    user_ids=new_cc_user_ids
                                )
                                print(f"已发送任务抄送通知给新增抄送人: {new_cc_user_ids}")
                            else:
                                print("获取钉钉token失败，无法发送任务抄送通知")
                        except Exception as e:
                            print(f"发送钉钉抄送通知失败: {str(e)}")
                            # 通知失败不影响任务更新，只记录日志
            
            # 生成最终日志消息
            log_message = "任务已编辑"
            if log_message_parts:
                log_message = f"任务已编辑: {', '.join(log_message_parts)}"
            
            # 添加编辑任务的日志
            cur.execute("""
                INSERT INTO project_management_t_task_logs (task_id, user_id, message)
                VALUES (%s, %s, %s)
            """, [task_id, user_id, log_message])
            
            # 为每个新增的边缘人添加日志
            # 但如果是主项目(ID=1)，不添加边缘人记录
            if int(project_id) != 1:
                for member in marginal_member_info:
                    cur.execute("""
                        INSERT INTO project_management_t_task_logs (task_id, user_id, message)
                        VALUES (%s, %s, %s)
                    """, [task_id, user_id, f"用户 {member['name']} (ID:{member['id']}) 作为边缘人加入项目"])
            
            # 提交事务
            mysql.connection.commit()
            
            # 获取更新后的任务详情
            task_details = get_task_details(task_id)
            
            # 添加前端需要的额外字段
            if task_details:
                # 获取项目名称
                cur.execute("SELECT name FROM project_management_t_projects WHERE id = %s", [project_id])
                project = cur.fetchone()
                if project:
                    task_details['project_name'] = project['name']
                    task_details['project_id'] = int(project_id)
                
                # 设置任务可完成标志
                task_details['can_complete'] = is_creator or is_assignee or is_super_admin or is_project_admin
                # 设置是否是创建者
                task_details['is_creator'] = is_creator
                # 设置是否是负责人
                task_details['is_assignee'] = is_assignee
            
            cur.close()
            
            return jsonify(success=True, message="任务更新成功", task=task_details)
            
        except Exception as e:
            return jsonify(success=False, message=f"更新任务失败: {str(e)}")
    


    @app.route('/projectmanagement/api/create_task', methods=['POST'])
    def project_api_create_task():
        """API endpoint for creating a new task"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        user_id = session['user_id']
        
        # 获取请求数据
        data = request.json
        project_id = data.get('project_id')
        name = data.get('name')
        description = data.get('description', '')
        deadline = data.get('deadline')
        urgency = data.get('urgency', '一般')
        is_immediate = data.get('is_immediate', True)  # 获取是否为单人单任务
        parent_id = data.get('parent_id')
        assignee_ids = data.get('assignee_ids', [])
        non_project_member_ids = data.get('non_project_member_ids', [])  # 新增：非项目成员ID列表
        is_historical = data.get('is_historical', False)  # 新增：是否为历史任务标记
        cc_users = data.get('cc_users', [])  # 获取抄送人列表
        
        # 验证必填字段
        if not project_id:
            return jsonify(success=False, message="缺少项目ID")
        
        if not name:
            return jsonify(success=False, message="任务名称不能为空")
        
        # 检查用户是否有权限在该项目中创建任务
        # 超级管理员、项目成员可以创建任务，主项目（ID=1）所有人都可以创建任务
        user_role = get_user_role(user_id)
        
        # 如果是主项目（ID=1），所有用户都有权限创建任务
        if int(project_id) != 1:
            project_role = get_user_project_role(user_id, project_id)
            if user_role != ROLE_SUPER_ADMIN and not project_role:
                return jsonify(success=False, message="您没有权限在此项目中创建任务")
        
        try:
            # 创建任务
            cur = mysql.connection.cursor()
            
            # 如果有非项目成员被指定为负责人，先将他们添加为项目边缘人
            if non_project_member_ids and len(non_project_member_ids) > 0:
                for member_id in non_project_member_ids:
                    # 检查用户在系统中是否存在
                    cur.execute("SELECT id FROM project_management_t_users WHERE id = %s", [member_id])
                    user = cur.fetchone()
                    
                    if not user:
                        # 需要先从员工数据库获取用户信息，并创建用户
                        employee = get_employee_by_id(member_id)
                        if employee:
                            # 创建用户
                            cur.execute("""
                                INSERT INTO project_management_t_users (id, name, role, created_at)
                                VALUES (%s, %s, %s, NOW())
                                ON DUPLICATE KEY UPDATE name = VALUES(name)
                            """, [member_id, employee.get('sys00_name'), ROLE_NORMAL])
                    
                    # 检查用户是否已经是项目成员
                    cur.execute("""
                        SELECT * FROM project_management_t_user_projects 
                        WHERE user_id = %s AND project_id = %s
                    """, [member_id, project_id])
                    
                    # 判断是否为ID为1的主项目，如果是则不需要添加用户为边缘人
                    if not cur.fetchone() and int(project_id) != 1:
                        # 添加为项目边缘人
                        cur.execute("""
                            INSERT INTO project_management_t_user_projects (project_id, user_id, role)
                            VALUES (%s, %s, %s)
                        """, [project_id, member_id, ROLE_MARGINAL])
                        
                        # 不在这里记录日志，而是在后面创建任务后添加相关信息到任务日志
            
            # 修复urgency字段可能的值：将'紧急'和'普通'转换为'紧急'和'一般'
            if urgency == '普通':
                urgency = '一般'
            
            # 获取钉钉配置信息（预先获取，避免重复代码）
            dingtalk_appkey = os.getenv('DINGTALK_APPKEY', 'ding6hyjguivqnldmwl2')
            dingtalk_appsecret = os.getenv('DINGTALK_APPSECRET', 'o3EI-ke49Dl9ZQZake1ioHc7f0yMB4gxYjyLECNF8ZmkD9NzF_fJyMbUhUyN10N0')
            access_token = None
            
            try:
                access_token = get_access_token(dingtalk_appkey, dingtalk_appsecret)
            except Exception as e:
                print(f"获取钉钉token失败: {str(e)}")
            
            # 创建者名称，用于通知
            creator_name = session.get('user_name', '未知用户')
            
            # 创建的所有任务ID列表（单人单任务可能创建多个）
            created_task_ids = []
            
            # 当没有指定负责人时，默认使用创建者
            if not assignee_ids or len(assignee_ids) == 0:
                assignee_ids = [user_id]
            
            # 单人单任务：为每个负责人创建独立的任务
            if is_immediate:
                task_details_list = []  # 存储所有创建的任务详情
                
                for assignee_id in assignee_ids:
                    # 构建任务名称，标明这是单人单任务

                    
                    # 插入单独的任务
                    cur.execute("""
                        INSERT INTO project_management_t_tasks 
                        (project_id, parent_id, creator_id, name, description, urgency, deadline, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                    """, [
                        project_id,
                        parent_id,
                        user_id,
                        name,
                        description,
                        urgency,
                        deadline
                    ])
                    
                    task_id = cur.lastrowid
                    created_task_ids.append(task_id)
                    
                    # 为这个任务指定唯一的负责人
                    cur.execute("""
                        INSERT INTO project_management_t_task_assignees (task_id, user_id)
                        VALUES (%s, %s)
                    """, [task_id, assignee_id])
                    
                    # 添加抄送人
                    if cc_users and len(cc_users) > 0:
                        for cc_user in cc_users:
                            if 'userid' in cc_user:
                                cur.execute("""
                                    INSERT INTO project_management_t_task_cc_users (task_id, user_id)
                                    VALUES (%s, %s)
                                """, [task_id, cc_user['userid']])
                    
                    # 添加任务创建日志
                    log_message = "单人单任务已创建"
                    if is_historical:
                        log_message = "单人单任务已登记"
                        
                    cur.execute("""
                        INSERT INTO project_management_t_task_logs (task_id, user_id, message)
                        VALUES (%s, %s, %s)
                    """, [task_id, user_id, log_message])
                    
                    # 为非项目成员添加日志（如果适用）
                    if assignee_id in non_project_member_ids and int(project_id) != 1:
                        # 获取用户名称
                        marginal_member_name = "未知"
                        cur.execute("SELECT name FROM project_management_t_users WHERE id = %s", [assignee_id])
                        user_result = cur.fetchone()
                        if user_result:
                            marginal_member_name = user_result['name']
                        
                        # 添加边缘人日志
                        cur.execute("""
                            INSERT INTO project_management_t_task_logs (task_id, user_id, message)
                            VALUES (%s, %s, %s)
                        """, [task_id, user_id, f"用户 {marginal_member_name} (ID:{assignee_id}) 作为边缘人加入项目"])
                    
                    # 发送钉钉通知
                    if access_token:
                        try:
                            # 发送给负责人的通知
                            send_task_assigned_message(
                                access_token=access_token,
                                appkey=dingtalk_appkey,
                                task_name=name,
                                creator=creator_name,
                                description=description or '无描述',
                                deadline=deadline,
                                user_ids=[assignee_id]
                            )
                            print(f"已发送任务通知给负责人: {assignee_id}")
                        except Exception as e:
                            print(f"发送钉钉通知失败: {str(e)}")
                    
                    # 获取任务详情添加到列表
                    task_details = get_task_details(task_id)
                    if task_details:
                        # 获取项目名称
                        cur.execute("SELECT name FROM project_management_t_projects WHERE id = %s", [project_id])
                        project = cur.fetchone()
                        if project:
                            task_details['project_name'] = project['name']
                            task_details['project_id'] = int(project_id)
                        
                        # 设置任务可完成标志
                        task_details['can_complete'] = True
                        # 设置是否是创建者
                        task_details['is_creator'] = True
                        # 设置是否是负责人
                        task_details['is_assignee'] = assignee_id == user_id
                        
                        task_details_list.append(task_details)
                
                # 提交事务
                mysql.connection.commit()

                
                # 返回所有创建的任务ID和第一个任务的详情（兼容原有API）
                primary_task_details = task_details_list[0] if task_details_list else None

                # 发送给抄送人的通知
                ENABLE_CC = os.getenv('ENABLE_CC', 'false').lower() == 'true'
                if cc_users and len(cc_users) > 0 and ENABLE_CC:
                    # 获取负责人名称
                    # 动态生成占位符 (%s, %s, %s)
                    placeholders = ', '.join(['%s'] * len(assignee_ids))
                    cur.execute(f"SELECT name FROM project_management_t_users WHERE id in ({placeholders}) ", assignee_ids)
                    assignee_results = cur.fetchall()
                    send_task_notifications(access_token, dingtalk_appkey, assignee_results, name, creator_name,
                                            description, deadline, cc_users)
                cur.close()
                return jsonify(
                    success=True, 
                    message=f"已创建 {len(created_task_ids)} 个即时任务",
                    task_id=created_task_ids[0] if created_task_ids else None,
                    task=primary_task_details,
                    all_task_ids=created_task_ids,
                    all_tasks=task_details_list
                )
            
            # 普通任务：所有负责人共享同一个任务（原有逻辑）
            else:
                # 构建插入任务的SQL
                query = """
                    INSERT INTO project_management_t_tasks 
                    (project_id, parent_id, creator_id, name, description, urgency, deadline, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                """
                
                # 准备参数
                params = [
                    project_id,
                    parent_id,
                    user_id,
                    name,
                    description,
                    urgency,
                    deadline
                ]
                
                cur.execute(query, params)
                task_id = cur.lastrowid
                created_task_ids.append(task_id)
                
                # 添加任务负责人
                if assignee_ids and len(assignee_ids) > 0:
                    for assignee_id in assignee_ids:
                        cur.execute("""
                            INSERT INTO project_management_t_task_assignees (task_id, user_id)
                            VALUES (%s, %s)
                        """, [task_id, assignee_id])
                else:
                    # 如果没有指定负责人，则创建者为负责人
                    cur.execute("""
                        INSERT INTO project_management_t_task_assignees (task_id, user_id)
                        VALUES (%s, %s)
                    """, [task_id, user_id])
                
                # 添加抄送人
                if cc_users and len(cc_users) > 0:
                    for cc_user in cc_users:
                        if 'userid' in cc_user:
                            cur.execute("""
                                INSERT INTO project_management_t_task_cc_users (task_id, user_id)
                                VALUES (%s, %s)
                            """, [task_id, cc_user['userid']])
                
                # 添加一条创建任务的日志
                log_message = "历史任务已登记" if is_historical else "任务已创建"
                cur.execute("""
                    INSERT INTO project_management_t_task_logs (task_id, user_id, message)
                    VALUES (%s, %s, %s)
                """, [task_id, user_id, log_message])
                
                # 为每个非项目成员添加一条日志，说明他们被添加为边缘人
                # 对于主项目(ID=1)，不添加边缘人记录，因为所有人都默认可见
                if non_project_member_ids and len(non_project_member_ids) > 0 and int(project_id) != 1:
                    for member_id in non_project_member_ids:
                        # 检查此用户是否确实是新添加的边缘人
                        cur.execute("""
                            SELECT role FROM project_management_t_user_projects 
                            WHERE user_id = %s AND project_id = %s
                        """, [member_id, project_id])
                        member_role_result = cur.fetchone()
                        
                        # 只有当用户是边缘人且是本次操作新添加的才记录日志
                        # 通过检查角色和执行时间判断
                        if member_role_result and member_role_result['role'] == ROLE_MARGINAL:
                            marginal_member_name = "未知"
                            cur.execute("SELECT name FROM project_management_t_users WHERE id = %s", [member_id])
                            user_result = cur.fetchone()
                            if user_result:
                                marginal_member_name = user_result['name']
                            
                            # 添加边缘人日志
                            cur.execute("""
                                INSERT INTO project_management_t_task_logs (task_id, user_id, message)
                                VALUES (%s, %s, %s)
                            """, [task_id, user_id, f"用户 {marginal_member_name} (ID:{member_id}) 作为边缘人加入项目"])
                
                # 提交事务
                mysql.connection.commit()
                
                # 获取任务详情用于返回
                task_details = get_task_details(task_id)
                
                # 添加前端需要的额外字段
                if task_details:
                    # 获取项目名称
                    cur.execute("SELECT name FROM project_management_t_projects WHERE id = %s", [project_id])
                    project = cur.fetchone()
                    if project:
                        task_details['project_name'] = project['name']
                        task_details['project_id'] = int(project_id)
                    
                    # 设置任务可完成标志
                    task_details['can_complete'] = True
                    # 设置是否是创建者
                    task_details['is_creator'] = True
                    # 设置是否是负责人
                    task_details['is_assignee'] = len(assignee_ids) == 0 or user_id in assignee_ids
                    
                    # 发送钉钉通知给任务负责人
                    if access_token:
                        try:
                            # 发送给负责人的通知
                            send_task_assigned_message(
                                access_token=access_token,
                                appkey=dingtalk_appkey,
                                task_name=name,
                                creator=creator_name,
                                description=description or '无描述',
                                deadline=deadline,
                                user_ids=assignee_ids if assignee_ids else [user_id]
                            )
                            print(f"已发送任务通知给负责人: {assignee_ids if assignee_ids else [user_id]}")
                        except Exception as e:
                            print(f"发送钉钉通知失败: {str(e)}")
                            # 通知失败不影响任务创建，只记录日志
                

                # 发送给抄送人的通知
                ENABLE_CC = os.getenv('ENABLE_CC', 'false').lower() == 'true'
                if cc_users and len(cc_users) > 0 and ENABLE_CC:
                    # 获取负责人名称
                    placeholders = ', '.join(['%s'] * len(assignee_ids))
                    cur.execute(f"SELECT name FROM project_management_t_users WHERE id in ({placeholders}) ",
                                assignee_ids)
                    assignee_results = cur.fetchall()
                    send_task_notifications(access_token, dingtalk_appkey, assignee_results, name, creator_name,
                                            description, deadline, cc_users)

                cur.close()
                return jsonify(
                    success=True, 
                    message="任务创建成功", 
                    task_id=task_id, 
                    task=task_details,
                    all_task_ids=[task_id]
                )
            
        except Exception as e:
            return jsonify(success=False, message=f"创建任务失败: {str(e)}")

    def send_task_notifications( access_token, dingtalk_appkey, assignee_results,name,  creator_name,description, deadline, cc_users):
        try:
            # 获取负责人名称
            assignee_names = []
            for assignee_result in assignee_results:
                assignee_names.append(assignee_result['name'])
            # 获取抄送人名称和ID列表
            cc_user_ids = []
            cc_usernames = []
            for cc_user in cc_users:
                if 'userid' in cc_user:
                    cc_user_ids.append(cc_user['userid'])
                    # 获取抄送人名称
                    cc_usernames.append(cc_user['name'])

            # 如果有抄送人，发送抄送通知
            if cc_user_ids:
                cc_username = ', '.join(cc_usernames)
                assignee_name = ', '.join(assignee_names)
                deadline_text = deadline if deadline else "无截止日期"

                send_task_cc_message(
                    access_token=access_token,
                    appkey=dingtalk_appkey,
                    task_name=name,
                    assigness_name=assignee_name,
                    creator_name=creator_name,
                    cc_username=cc_username,
                    description=description or '无描述',
                    deadline=deadline_text,
                    user_ids=cc_user_ids
                )
                print(f"已发送任务抄送通知给: {cc_username}")

        except Exception as e:
            print(f"发送钉钉通知失败: {str(e)}")

    @app.route('/projectmanagement/api/daily_tracking')
    def project_api_daily_tracking():
        """API endpoint for getting daily task creation and completion stats"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录"), 401
        
        user_id = session['user_id']
        
        # 获取最近30天的日期
        end_date = datetime.datetime.now().date()
        start_date = end_date - datetime.timedelta(days=29)
        
        try:
            # 获取用户的所有任务（包括创建的和负责的）
            cur = mysql.connection.cursor()
            
            # 获取用户创建或负责的所有任务
            cur.execute("""
                SELECT 
                    t.id, 
                    t.name, 
                    t.created_at, 
                    t.completed_date
                FROM 
                    project_management_t_tasks t
                LEFT JOIN 
                    project_management_t_task_assignees ta ON t.id = ta.task_id
                WHERE 
                    t.creator_id = %s 
                    OR ta.user_id = %s
                    OR t.creator_id IN (
                        SELECT user_id FROM project_management_t_task_assignees 
                        WHERE task_id IN (
                            SELECT task_id FROM project_management_t_task_assignees 
                            WHERE user_id = %s
                        )
                    )
            """, [user_id, user_id, user_id])
            
            tasks = cur.fetchall()
            cur.close()
            
            # 输出调试信息
            if tasks:
                print("日期追踪任务数据样本:")
                print(f"获取到 {len(tasks)} 个任务")
                if len(tasks) > 0:
                    first_task = tasks[0]
                    print(f"第一个任务: ID={first_task['id']}, 名称={first_task['name']}")
                    print(f"创建日期: {first_task['created_at']}, 类型: {type(first_task['created_at'])}")
                    print(f"完成日期: {first_task['completed_date']}, 类型: {type(first_task['completed_date'])}")
                if len(tasks) > 1:
                    last_task = tasks[-1]
                    print(f"最后一个任务: ID={last_task['id']}, 名称={last_task['name']}")
                    print(f"创建日期: {last_task['created_at']}, 类型: {type(last_task['created_at'])}")
                    print(f"完成日期: {last_task['completed_date']}, 类型: {type(last_task['completed_date'])}")
            
            # 初始化返回数据
            result_data = []
            current_date = start_date
            
            # 对每一天进行统计
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                
                # 统计当天创建的任务数
                new_count = 0
                for task in tasks:
                    if task['created_at'] and task['created_at'].date() == current_date:
                        new_count += 1
                
                # 统计当天完成的任务数
                completed_count = 0
                for task in tasks:
                    if task['completed_date'] and task['completed_date'] == current_date:
                        completed_count += 1
                
                # 添加当天数据
                result_data.append({
                    'date': date_str,
                    'new_count': new_count,
                    'completed_count': completed_count
                })
                
                # 移至下一天
                current_date += datetime.timedelta(days=1)
            
            return jsonify(success=True, data=result_data)
            
        except Exception as e:
            print(f"获取每日任务统计失败: {str(e)}")
            return jsonify(success=False, message=f"获取每日任务统计失败: {str(e)}"), 500

    @app.route('/projectmanagement/api/cc_tasks')
    def project_api_cc_tasks():
        """API endpoint for getting tasks where the current user is CC'd"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        user_id = session['user_id']
        
        # 获取状态和搜索参数
        status = request.args.get('status')
        search_term = request.args.get('search_term', '')
        month = request.args.get('month')  # 获取月份参数
        task_type = request.args.get('task_type', '')  # 获取任务类型参数: assigned/created/cc
        
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 14, type=int)
        
        import time
        start_time = time.time()
        logger.info(f"CC任务查询 - 状态: {status}, 搜索词: {search_term}, 月份: {month}, 任务类型: {task_type}, 页码: {page}, 每页大小: {page_size}")
        
        cur = mysql.connection.cursor()
        
        # 构建基础查询
        query_params = []
        status_condition = ""
        search_condition = ""
        month_condition = ""
        task_type_condition = ""
        
        # 添加状态筛选条件
        if status and status != 'all':
            if status == 'overdue': # 逾期未完成
                status_condition = " AND t.deadline < CURRENT_DATE() AND t.completed_date IS NULL "
            elif status == 'overdue_completed': # 逾期完成
                status_condition = " AND t.completed_date IS NOT NULL AND DATE(t.completed_date) > t.deadline "
            elif status == 'in_progress': # 进行中
                status_condition = " AND t.completed_date IS NULL AND (t.deadline IS NULL OR t.deadline >= CURRENT_DATE()) "
            elif status == 'on_time_completed': # 按时完成
                status_condition = " AND t.completed_date IS NOT NULL AND (t.deadline IS NULL OR DATE(t.completed_date) <= t.deadline) "
            elif status == 'due_today': # 今日到期
                status_condition = " AND DATE(t.deadline) = CURRENT_DATE() AND t.completed_date IS NULL "
            elif status == 'completed': # 已完成
                status_condition = " AND t.completed_date IS NOT NULL "
            elif status == 'incomplete': # 未完成
                status_condition = " AND t.completed_date IS NULL "
        
        # 添加月份筛选条件
        if month:
            month_condition = " AND DATE_FORMAT(t.deadline, '%%Y-%%m') = %s "
            query_params.append(month)
        
        # 添加搜索条件
        if search_term:
            search_term = f"%{search_term}%"
            search_condition = """ AND (
                t.name LIKE %s OR 
                t.description LIKE %s OR
                u.name LIKE %s OR
                assignee.name LIKE %s
            )"""
            query_params.extend([search_term, search_term, search_term, search_term])
            
        # 添加任务类型筛选条件
        if task_type:
            if task_type == 'assigned':
                task_type_condition = " AND ta.user_id = %s "
                query_params.append(user_id)
            elif task_type == 'created':
                task_type_condition = " AND t.creator_id = %s "
                query_params.append(user_id)
            elif task_type == 'cc':
                task_type_condition = " AND tc.user_id = %s AND t.creator_id != %s AND t.id NOT IN (SELECT task_id FROM project_management_t_task_assignees WHERE user_id = %s) "
                query_params.extend([user_id, user_id, user_id])
        else:
            task_type_condition=" AND (tc.user_id = %s OR ta.user_id = %s OR t.creator_id = %s) "
            query_params.extend([user_id, user_id, user_id])
        # 先构建COUNT查询以获取总记录数
        count_start_time = time.time()
        count_query = f"""
            SELECT COUNT(DISTINCT t.id) as total
            FROM project_management_t_tasks t
            JOIN project_management_t_projects p ON t.project_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id
            LEFT JOIN project_management_t_task_cc_users tc ON t.id = tc.task_id
            LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id  
            LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id
            WHERE 1=1
            {status_condition}
            {month_condition}
            {search_condition}
            {task_type_condition}
        """
        
       
    
        # 执行COUNT查询
        cur.execute(count_query, query_params)
        total_count = cur.fetchone()['total']
        count_end_time = time.time()
        logger.info(f"CC任务计数查询耗时: {(count_end_time - count_start_time)*1000:.2f}ms, 总记录数: {total_count}")
        
        # 计算分页参数
        offset = (page - 1) * page_size
        
        # 构建数据查询
        data_start_time = time.time()
        data_query = f"""
            SELECT DISTINCT t.*, p.name as project_name, u.name as creator_name,
                     CASE 
                           WHEN t.parent_id IS NULL THEN t.id 
                           ELSE t.parent_id 
                       END as sort_group,
                       CASE 
                           WHEN t.parent_id IS NULL THEN 0
                           ELSE 1
                       END as is_child,
                     CASE
                           WHEN t.creator_id = {user_id} THEN '我创建的'
                           WHEN ta.user_id = {user_id} THEN '我负责的'
                           WHEN tc.user_id = {user_id} THEN '被抄送的'
                           ELSE '未知类型'
                     END as task_relation_type
            FROM project_management_t_tasks t
            JOIN project_management_t_projects p ON t.project_id = p.id
            LEFT JOIN project_management_t_users u ON t.creator_id = u.id
            LEFT JOIN project_management_t_task_cc_users tc ON t.id = tc.task_id
            LEFT JOIN project_management_t_task_assignees ta ON t.id = ta.task_id  
            LEFT JOIN project_management_t_users assignee ON ta.user_id = assignee.id
            WHERE 1=1
            {status_condition}
            {month_condition}
            {search_condition}
            {task_type_condition}
            ORDER BY sort_group DESC, is_child, t.created_at DESC
            LIMIT %s OFFSET %s
        """
        
        # 构建数据查询参数列表
        data_params =  query_params + [page_size, offset]
        
        # 执行数据查询
        cur.execute(data_query, data_params)
        tasks = cur.fetchall()
        data_end_time = time.time()
        logger.info(f"CC任务数据查询耗时: {(data_end_time - data_start_time)*1000:.2f}ms, 获取到 {len(tasks)} 条记录")
        
        # 获取用户角色信息
        user_role = get_user_role(user_id)
        is_super_admin = (user_role == ROLE_SUPER_ADMIN)
        
        # 获取每个任务的负责人
        task_list = []
        detail_start_time = time.time()
        for task in tasks:
            # 获取任务负责人
            cur.execute("""
                SELECT u.name, u.id
                FROM project_management_t_task_assignees ta
                JOIN project_management_t_users u ON ta.user_id = u.id
                WHERE ta.task_id = %s
            """, [task['id']])

            assignees = cur.fetchall()
            assignees_names = ', '.join([a['name'] for a in assignees])
            # 获取任务抄送人
            cur.execute("""
                                          SELECT u.name
                                          FROM project_management_t_task_cc_users ts
                                          JOIN project_management_t_users u ON ts.user_id = u.id
                                          WHERE ts.task_id = %s
                                      """, [task['id']])

            cc_users = cur.fetchall()
            cc_users_names = ','.join([a['name'] for a in cc_users])
            # 检查是否是项目管理员
            project_role = get_user_project_role(user_id, task['project_id'])
            is_project_admin = (project_role == ROLE_ADMIN)
            
            # 检查当前用户是否是负责人之一
            is_assignee = False
            for assignee in assignees:
                if assignee['id'] == user_id:
                    is_assignee = True
                    break
            
            # 检查当前用户是否是创建者
            is_creator = task['creator_id'] == user_id
            
            # 检查用户是否可以完成任务 (管理员/超管/负责人/发起人)
            can_complete = is_creator or is_assignee or is_super_admin or is_project_admin
            
            # 直接使用SQL查询返回的任务类型
            # task_type = task['task_relation_type']
            
            # 准备任务数据
            task_data = {
                'id': task['id'],
                'name': task['name'],
                'description': task['description'],
                'deadline': task['deadline'].strftime('%Y-%m-%d') if task['deadline'] else None,
                'completed_date': task['completed_date'].strftime('%Y-%m-%d') if task['completed_date'] else None,
                'created_at': task['created_at'].strftime('%Y-%m-%d %H:%M') if task['created_at'] else None,
                'urgency': task['urgency'],
                'project_name': task['project_name'],
                'assignees': assignees_names,
                'cc_users_names': cc_users_names,
                'creator_name': task['creator_name'],
                'can_complete': can_complete,
                'project_id': task['project_id'],
                'is_child': task['parent_id'] is not None,
                'parent_id': task['parent_id'],
                'task_relation_type': task['task_relation_type']  # 添加任务类型字段
            }
            task_list.append(task_data)
        
        detail_end_time = time.time()
        logger.info(f"CC任务详情查询耗时: {(detail_end_time - detail_start_time)*1000:.2f}ms")
        
        cur.close()
        
        end_time = time.time()
        total_time = end_time - start_time
        logger.info(f"CC任务查询总耗时: {total_time*1000:.2f}ms")
        
        # 返回任务列表及分页信息
        return jsonify(
            success=True, 
            tasks=task_list,
            pagination={
                'total': total_count,
                'page': page,
                'page_size': page_size
            }
        )

    return app