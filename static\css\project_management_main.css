/* 基础样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* 移除body样式，因为已经被base_layout处理 */
/* body {
    font-family: 'Microsoft YaHei', sans-serif;
    display: flex;
    height: 100vh;
    overflow: hidden;
    background-color: #f5f7fa;
    padding-left: 130px;
} */

/* 项目内容包装器 */
.project-content-wrapper {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
    position: fixed; /* 改为固定定位以确保全屏覆盖 */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5; /* 确保在合适的层级 */
}

/* 覆盖base_layout的main-content样式 */
.main-content {
    margin-left: 0 !important;
    padding: 0 !important;
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: 220px;
    min-width: 220px;
    background-color: #0a1424; /* 调整为更暗的蓝黑色 */
    color: rgba(255, 255, 255, 0.65);
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    flex-shrink: 0;
    border-right: 1px solid rgba(0, 0, 0, 0.5);
    margin-left: 130px; /* 留出左侧导航栏的宽度 */
    z-index: 10; /* 确保侧边栏显示在上层 */
    box-shadow: inset -5px 0 15px rgba(0, 0, 0, 0.2);
}
.user-info {
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    margin-bottom: 16px;
    background-color: rgba(0, 0, 0, 0.25);
}
.user-info-content {
    display: flex;
    align-items: center;
}
.user-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    color: rgba(255, 255, 255, 0.95);
    background-color: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    padding: 6px;
    font-size: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
.user-name {
    color: rgba(255, 255, 255, 0.95);
    font-weight: 500;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.3px;
}
.user-role {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    margin-left: 6px;
}
.nav-menu {
    flex-grow: 0;
    margin: 5px 0 10px 0;
    padding: 0 12px;
}
.sidebar .nav-item {
    padding: 7px 18px;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    height: auto;
    width: 100%;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    position: relative;
    border-radius: 6px;
    margin-left: 4px;
    margin-right: 4px;
    font-size: 14px;
    letter-spacing: 0.3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    line-height: 1.3;
}
.sidebar .nav-item::before {
    content: "";
    position: absolute;
    left: -4px;
    top: 0;
    bottom: 0;
    width: 0;
    background-color: #F05123;
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
.sidebar .nav-item .nav-icon {
    margin-right: 12px;
    font-size: 20px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.65);
    transition: color 0.25s ease;
}
.sidebar .nav-item .nav-label {
    flex: 1;
    transition: color 0.25s ease;
    font-size: 14px;
    font-weight: 400;
}
.sidebar .nav-item:hover {
    background-color: rgba(255, 255, 255, 0.07);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}
.sidebar .nav-item:hover .nav-icon,
.sidebar .nav-item:hover .nav-label {
    color: white;
}
.sidebar .nav-item.active {
    background-color: rgba(240, 81, 35, 0.15);
    color: #F05123;
    border-right: none;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    animation: fadeInLeft 0.3s ease-out forwards;
}
.sidebar .nav-item.active::before {
    width: 3px;
    box-shadow: 0 0 8px rgba(240, 81, 35, 0.6);
}
.sidebar .nav-item.active .nav-icon,
.sidebar .nav-item.active .nav-label {
    color: #F05123;
    font-weight: 500;
}
.projects-list {
    padding: 16px 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.06);
    margin-top: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止整体溢出 */
}

/* 项目列表上部分（固定部分） */
.projects-list-fixed {
    margin-bottom: 16px;
}

/* 项目列表可滚动部分 */
.projects-list-scrollable {
    overflow-y: auto; /* 允许滚动 */
    flex-grow: 1;
    padding-right: 4px; /* 为滚动条留出空间 */
    margin-right: -4px; /* 确保内容与其他部分对齐 */
    min-height: 100px; /* 确保最小高度 */
}

/* 添加项目列表标题样式 */
.projects-header {
    font-weight: 500;
    font-size: 15px;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    display: flex;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.3px;
}

/* 项目列表图标样式 - 不再需要 */
/* .projects-header .el-icon {
    margin-right: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
} */

.project-search {
    padding: 0;
    width: 100%;
    margin-bottom: 18px;
    background-color: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 6px;
    color: white;
    transition: all 0.3s ease;
    font-size: 13px;
    letter-spacing: 0.2px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    height: 40px;
}

.project-search .el-input__wrapper {
    background-color: transparent;
    box-shadow: none;
    padding: 0 12px;
    height: 40px;
}

.project-search .el-input__inner {
    height: 40px;
    color: white;
    background-color: transparent;
    font-size: 14px;
}

.project-search:focus-within,
.project-search:hover {
    background-color: rgba(255, 255, 255, 0.07);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
}

.project-search:focus-within {
    border-color: rgba(240, 81, 35, 0.3);
    box-shadow: 0 0 0 2px rgba(240, 81, 35, 0.1);
}

/* 自定义滚动条样式 */
.projects-list-scrollable::-webkit-scrollbar {
    width: 6px;
}

.projects-list-scrollable::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.projects-list-scrollable::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
}

.projects-list-scrollable::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* 确保项目项目有固定的高度和间距 */
.project-item {
    padding: 10px 14px;
    margin-bottom: 6px;
    cursor: pointer;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.75);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 13px;
    position: relative;
    letter-spacing: 0.2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    min-height: 36px; /* 确保最小高度 */
    display: flex;
    align-items: center;
}

.project-item::before {
    content: "";
    position: absolute;
    left: -4px;
    top: 0;
    bottom: 0;
    width: 0;
    background-color: #F05123;
    transition: width 0.25s ease-in-out;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}

.project-item:hover {
    background-color: rgba(255, 255, 255, 0.07);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.project-item.current {
    background-color: rgba(240, 81, 35, 0.15);
    color: #F05123;
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    animation: fadeInLeft 0.3s ease-out forwards;
}

.project-item.current::before {
    width: 3px;
    box-shadow: 0 0 8px rgba(240, 81, 35, 0.6);
}

/* 选中项目的图标样式 */
.project-selected-icon {
    color: #F05123;
    margin-right: 8px;
    font-size: 16px;
    filter: drop-shadow(0 0 2px rgba(240, 81, 35, 0.4));
}

.project-item .special {
    font-size: 0.85em;
    opacity: 0.7;
    margin-left: 5px;
}

/* 主内容区域样式 */
.project-main-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    min-width: 0; /* 防止内容溢出 */
    transition: all 0.3s ease; /* 添加过渡效果 */
    height: 100%;
    max-width: 100%;
    position: relative;
    border-radius: 4px; /* 添加圆角 */
}

/* 为任务表内容添加淡入效果 */
.el-table {
    transition: opacity 0.3s ease-in-out;
}

.el-table--loading {
    opacity: 0.6;
}

/* 添加内容切换过渡效果 */
.content-fade-enter-active,
.content-fade-leave-active {
    transition: opacity 0.3s ease;
}

.content-fade-enter-from,
.content-fade-leave-to {
    opacity: 0;
}

/* 当抽屉打开时，调整主内容区域的宽度 */
body.drawer-open .project-main-area {
    max-width: calc(100% - 400px); /* 确保内容区域不会被抽屉覆盖 */
    margin-right: 0; /* 移除右侧外边距 */
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* 按钮和表格样式 */
.action-buttons {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}
.btn {
    padding: 8px 16px;
    background-color: #F05123;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
.btn:hover {
    background-color: #ff6b43;
}

/* 搜索表单样式 */
.search-form {
    display: flex;
    gap: 10px;
    align-items: center;
    width: 100%;
}
.search-form input[type="text"] {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
}
.search-form .btn {
    white-space: nowrap;
}

/* 无数据提示样式 */
.no-data {
    text-align: center;
    padding: 20px;
    color: #8c8c8c;
    font-style: italic;
}

.task-list {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed; /* 防止表格扩展 */
}
.task-list th {
    text-align: left;
    padding: 12px 8px;
    border-bottom: 2px solid #f0f0f0;
    color: #8c8c8c;
}
.task-list td {
    padding: 12px 8px;
    border-bottom: 1px solid #f0f0f0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.task-list tr:hover {
    background-color: #f5f7fa;
}
.urgent {
    color: #f5222d;
    font-weight: bold;
}
.normal {
    color: #52c41a;
}
.task-parent {
    font-weight: bold;
}
.task-child .task-name {
    padding-left: 25px;
    position: relative;
}
.task-child .task-name::before {
    content: "";
    position: absolute;
    left: 8px;
    color: #8c8c8c;
}

/* 任务列表样式修改 - 为已完成任务添加样式 */
.task-list tr.completed-task td,
.el-table tr.completed-task td {
    color: #aaa; /* 使文字颜色变浅 */
}

.task-list tr.completed-task .task-name,
.el-table tr.completed-task .task-name span,
.task-name-text.completed-name {
    text-decoration: line-through; /* 为任务名称添加删除线 */
}

/* 为Element Plus表格添加兼容样式 */
.el-table .el-table__row.completed-task {
    color: #aaa;
}

.el-table .el-table__row.completed-task .task-name span {
    text-decoration: line-through;
}

/* 完成任务按钮 */
.complete-task {
    display: inline-block;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 1px solid #F05123;
    margin-right: 10px;
    vertical-align: middle;
    cursor: pointer;
    position: relative;
    background-color: transparent;
}
.complete-task:hover {
    background-color: rgba(240, 81, 35, 0.1);
}
.complete-task.completed {
    background-color: #F05123;
}
.complete-task.completed::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 11px;
    font-weight: bold;
}
.complete-task.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #d9d9d9;
}
.complete-task.disabled:hover {
    background-color: transparent;
}

/* 任务详情抽屉样式 */
.task-drawer {
    width: 400px;
    background-color: white;
    border-left: 1px solid #f0f0f0;
    position: relative;
    height: 100vh;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    margin-top: 0;
    flex-shrink: 0;
    display: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    /* 添加弹性布局 */
    display: none;
    flex-direction: column;
}

.task-drawer.open {
    display: flex; /* 改为flex布局 */
}

/* 抽屉内容区域 */
.drawer-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* 抽屉信息描述组件样式 */
.drawer-info .el-descriptions {
    margin-bottom: 20px;
}

/* 调整描述列表项的样式 */
.drawer-info .el-descriptions__cell {
    padding: 12px 16px;
}

/* 设置标签列的固定宽度 */
.drawer-info .el-descriptions__label {
    width: 90px;
    min-width: 90px;
    color: #606266;
    font-weight: 500;
}

/* 描述内容单元格样式 */
.drawer-info .el-descriptions__content {
    flex: 1;
    color: #303133;
}

/* 特别处理项目描述单元格 */
.drawer-info .el-descriptions__row:nth-child(2) .el-descriptions__cell {
    white-space: pre-wrap;
    word-break: break-word;
}

/* 其他单元格保持单行显示 */
.drawer-info .el-descriptions__row:not(:nth-child(2)) .el-descriptions__cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 确保描述内容可以换行显示 */
.drawer-info .el-descriptions__row:nth-child(2) .el-descriptions__content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
    max-height: none;
}

/* 其他字段保持固定高度 */
.drawer-info .el-descriptions__row:not(:nth-child(2)) .el-descriptions__content {
    line-height: 1.4;
    height: 32px;
    display: flex;
    align-items: center;
}

/* 回复区域固定在底部 */
.reply-section {
    position: sticky;
    bottom: 0;
    background-color: white;
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
    margin-top: 0;
    z-index: 10;
}

.reply-section .el-form-item:last-child {
    margin-bottom: 0;
}

/* 确保内容区域不会被固定的回复框遮挡 */
.communication-log {
    margin-bottom: 0;
    padding-bottom: 16px;
}

.drawer-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px; /* 增加上下左右的内边距 */
    margin-bottom: 0; /* 移除底部外边距，因为我们有足够的内边距了 */
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.drawer-title {
    font-size: 1.2em;
    margin-bottom: 0;
    font-weight: 500; /* 增加字体粗细 */
    color: #303133; /* 调整颜色，使其更加醒目 */
    padding-right: 10px; /* 在右侧增加一些空间，避免与关闭按钮太近 */
}
.drawer-close {
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: color 0.3s;
}
.drawer-close:hover {
    color: #f5222d;
}
.drawer-info {
    margin-bottom: 20px;
}
.drawer-info div {
    margin-bottom: 10px;
}
.drawer-loading, .drawer-error {
    padding: 20px;
    text-align: center;
    color: #8c8c8c;
}
.drawer-error {
    color: #f5222d;
}
.log-empty {
    padding: 10px 0;
    color: #8c8c8c;
    font-style: italic;
    text-align: center;
}
.log-title {
    font-weight: bold;
    margin-bottom: 10px;
}
.log-item {
    position: relative;
    padding-right: 20px;
    margin-bottom: 8px;
}
.log-tag {
    margin-left: 4px;
    margin-right: 4px;
    font-size: 0.75em;
}
.log-sender {
    font-weight: bold;
    display: inline;
}
.log-time {
    color: #8c8c8c;
    font-size: 0.85em;
    display: inline;
}
.log-content {
    margin-top: 4px;
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 14px;
    line-height: 1.6;
}

/* 当抽屉打开时调整内容包装器 */
body.drawer-open .project-content-wrapper {
    width: 100%; /* 保持宽度不变 */
}

/* 项目管理网格布局 */
.project-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
    min-height: calc(100vh - 200px); /* 确保最小高度足够 */
}

/* 空状态容器样式 */
.project-management-grid .el-empty {
    grid-column: 1 / -1; /* 跨越所有列 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 400px; /* 设置最小高度 */
    margin: auto;
}

/* 项目卡片样式 */
.project-card {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    height: 200px; /* 固定高度 */
}

.project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.project-card-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
}

/* 项目卡片描述区域增强 */
.project-card-desc.rich-text-container {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: 16px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 1.5;
}

.project-card-desc.rich-text-container p {
    margin: 0 0 5px 0;
}

.project-card-desc.rich-text-container img {
    max-height: 30px;
    max-width: 100%;
    object-fit: contain;
    margin: 2px 0;
    vertical-align: middle;
}

.project-card-desc.rich-text-container ul,
.project-card-desc.rich-text-container ol {
    padding-left: 20px;
    margin: 5px 0;
}

.project-card-desc.rich-text-container a {
    color: #F05123;
    text-decoration: none;
}

.project-card-desc.rich-text-container a:hover {
    text-decoration: underline;
}

.project-card-desc.rich-text-container blockquote {
    margin: 5px 0;
    padding-left: 10px;
    border-left: 3px solid #ddd;
    color: #666;
}

.project-card-desc.rich-text-container code {
    background-color: #f5f7fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
}

.project-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.project-card-members {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
}

.project-card-actions {
    display: flex;
    gap: 8px;
}

/* 当没有项目时的空状态样式 */
.project-management-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 200px);
    text-align: center;
}

.project-management-empty .el-empty {
    padding: 40px;
}

.project-management-empty .el-empty__description {
    margin-top: 20px;
    color: var(--el-text-color-secondary);
}

/* 确保内容区域占满可用空间 */
.project-content-wrapper {
    display: flex;
    min-height: 100vh;
}

.project-main-area {
    flex: 1;
    padding: 10px 0 20px 20px;
    display: flex;
    flex-direction: column;
}

/* 调整操作按钮区域的样式 */
.action-buttons {
    margin-bottom: 20px;
    padding: 0 0px;
}

/* 项目管理模态窗口 */
.project-modal-content {
    margin-bottom: 20px;
    min-height: 500px;
}

.project-management-tabs .el-tabs__header {
    margin-right: 30px;
    width: 180px;
}

.project-management-tabs .el-tabs__item {
    height: auto;
    line-height: 1.5;
    padding: 20px;
    font-size: 15px;
}

.project-management-tabs .tab-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 0;
}

.project-management-tabs .tab-label .el-icon {
    font-size: 24px !important;
    margin-bottom: 10px;
}

.project-modal-section {
    margin-bottom: 30px;
    padding: 0 20px;
}

.project-modal-section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    color: #333;
    display: flex;
    align-items: center;
}

.project-members-list {
    margin-top: 20px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

.project-member-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.3s ease;
}

.project-member-item:hover {
    background-color: #f9f9f9;
}

.project-member-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f2f2f2;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #666;
}

.project-member-info {
    flex: 1;
    padding: 0 15px;
}

.project-member-name {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.project-member-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 3px;
}

.project-member-role {
    font-size: 13px;
    color: #F05123;
    background-color: #fff0eb;
    padding: 4px 12px;
    border-radius: 12px;
    display: inline-block;
    line-height: 1.4;
    white-space: nowrap;
}

.project-member-department {
    font-size: 12px;
    color: #909399;
    background-color: #f5f7fa;
    padding: 2px 8px;
    border-radius: 10px;
}

.project-member-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-left: auto;
    padding-left: 20px;
}

/* 为活跃导航项添加动画效果 */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 自定义复选框颜色 */
.el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #67c23a !important;
    border-color: #67c23a !important;
}

/* 修改复选框为圆形 */
.el-checkbox__inner {
    border-radius: 50% !important;
    width: 18px !important;
    height: 18px !important;
}

.el-checkbox__inner::after {
    height: 8px !important;
    left: 6px !important;
}

/* 任务名称文本样式 */
.task-name-text {
    transition: all 0.3s ease;
    vertical-align: middle;
    line-height: 1.5;
}

/* 确保复选框和文字对齐 */
.el-table .task-name {
    display: flex;
    align-items: center;
}

.el-table .task-name .el-checkbox {
    margin-right: 15px;
}

/* 自定义任务复选框 */
.task-checkbox {
    margin-right: 15px !important;
}

.task-checkbox .el-checkbox__inner {
    width: 20px !important;
    height: 20px !important;
    border-radius: 50% !important;
}

.task-checkbox .el-checkbox__inner::after {
    height: 9px !important;
    left: 7px !important;
    top: 3px !important;
}

/* 删除项目确认区域 */
.delete-project-confirm {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
    margin-top: 20px;
}

.delete-project-confirm p {
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
}

.delete-project-confirm strong {
    color: #f56c6c;
    font-weight: bold;
}

/* 使警告更加突出 */
.project-modal-section .el-alert--warning {
    border: 1px solid rgba(230, 162, 60, 0.2);
    background-color: rgba(230, 162, 60, 0.1);
}

/* 确保表格列宽一致 */
.el-table th {
    text-align: left !important;
    font-weight: 500 !important;
}

.el-table--empty .el-table__body td {
    text-align: center !important;
}

.el-table--empty .el-table__empty-text {
    width: 100% !important;
}

/* 设置列宽 */
.el-table .cell {
    word-break: normal !important;
    white-space: nowrap !important;
}

/* 任务时间分布图表样式 */
#todoTimeDistribution {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
    margin-top: 10px;
    width: 100%;
    max-width: 100%;
}

/* 底部时间分布组件样式 */
.home-dashboard > .home-task-overview {
    margin-top: 20px;
    width: 100%;
    max-width: 100%;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
}

/* 底部分析图表布局 */
.home-task-analytics {
    margin-top: 20px;
    width: 100%;
    max-width: 100%;
}

.home-analytics-row {
    display: flex;
    gap: 20px;
    width: 100%;
}

.home-task-analytics .home-task-overview {
    flex: 1;
    min-width: 300px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
}

/* 确保图表区域宽度与上方内容保持一致 */
.home-overview-container,
.home-task-analytics {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.home-tasks-container {
    display: flex;
    gap: 20px;
    width: 100%;
}

/* 调整图表容器样式 */
#todoTimeDistribution,
#todoDailyTracking {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
    margin-top: 10px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* 确保图表容器在小屏幕上也能显示 */
@media (max-width: 1023px) {
    .home-analytics-row {
        flex-direction: column !important;
    }
    .home-task-analytics .home-task-overview {
        width: 100% !important;
    }
    #todoTimeDistribution,
    #projectProgressChart,
    #taskCompletionTrend {
        width: 100% !important;
    }
}

/* 底部图表分析区域 */
.home-chart-container {
    margin-top: 20px;
    width: 100%;
    padding: 0;
    box-sizing: border-box;
}

.home-chart-row {
    display: flex;
    gap: 20px;
    width: 100%;
}

.home-chart-box {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chart-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
}

.chart-help-icon {
    margin-left: 8px;
    color: #909399;
    font-size: 16px;
    cursor: help;
}

.chart-content {
    padding: 15px;
}

/* 确保home-dashboard内所有主容器宽度一致 */
.home-dashboard .home-tasks-container,
.home-dashboard .home-overview-container,
.home-dashboard .home-chart-container {
    width: 100%;
    box-sizing: border-box;
}

/* 确保图表容器样式适配新结构 */
#todoTimeDistribution,
#todoDailyTracking {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
    width: 100%;
    box-sizing: border-box;
}

/* 确保图表容器在小屏幕上也能显示 */
@media (max-width: 1023px) {
    .home-chart-row {
        flex-direction: column !important;
    }
    .home-chart-box {
        width: 100% !important;
    }
    #todoTimeDistribution,
    #projectProgressChart,
    #taskCompletionTrend,
    #taskAssignmentDistribution {
        width: 100% !important;
    }
}

/* 完成任务对话框样式 */
.task-complete-dialog .el-message-box__wrapper {
    max-width: 550px;
    margin: 0 auto;
}

.task-complete-dialog .el-message-box {
    width: 500px;
    max-width: 90vw;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.task-complete-dialog .el-message-box__input {
    padding: 10px 0;
}

.task-complete-dialog .el-textarea__inner {
    min-height: 120px !important;
    font-size: 14px;
    padding: 12px;
    border-radius: 6px;
    resize: vertical;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #dcdfe6;
    transition: all 0.25s ease;
}

.task-complete-dialog .el-textarea__inner:focus {
    border-color: #F05123;
    box-shadow: 0 0 0 2px rgba(240, 81, 35, 0.2);
}

.task-complete-dialog .el-message-box__title {
    font-weight: 600;
    font-size: 18px;
    color: #333;
}

.task-complete-dialog .el-message-box__message {
    font-size: 15px;
    margin-bottom: 10px;
    color: #555;
}

/* 项目成员管理表单样式 */
.demo-form-inline {
    display: flex;
    gap: 10px;
    align-items: flex-start;
    margin-bottom: 20px;
}

.demo-form-inline .el-form-item {
    margin-bottom: 0;
    flex-shrink: 0;
}

/* 调整用户选择和角色选择下拉框的宽度 */
.demo-form-inline .el-select {
    width: 280px;  /* 增加选择用户下拉框的宽度 */
}

/* 特别调整角色选择下拉框的宽度 */
.demo-form-inline .el-select:nth-child(2) {
    width: 150px;
}

/* 确保下拉选项完全显示 */
.el-select-dropdown__item {
    padding: 0 15px;
    height: 34px;
    line-height: 34px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 调整下拉框中的部门信息显示 */
.el-select-dropdown__item span:last-child {
    color: #909399;
    font-size: 13px;
    margin-left: 8px;
}

/* 调整选择框的输入框样式 */
.el-select .el-input__inner {
    font-size: 14px;
}

/* 确保下拉列表的最小宽度与选择框一致 */
.el-select-dropdown.el-popper {
    min-width: 280px !important;
}

/* 调整成员角色显示样式 */
.project-member-role {
    font-size: 13px;
    color: #F05123;
    background-color: #fff0eb;
    padding: 4px 12px;
    border-radius: 12px;
    display: inline-block;
    line-height: 1.4;
    white-space: nowrap;
}

/* 调整成员操作区域的布局 */
.project-member-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-left: auto;
    padding-left: 20px;
}

/* 调整权限下拉按钮的样式 */
.project-member-actions .el-dropdown {
    margin-right: 8px;
}

.project-member-actions .el-button {
    min-width: 90px;
}

/* 项目成员管理样式 */
.project-members-list {
    padding: 20px;
}

.member-add-form {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 8px;
}

.member-form {
    display: flex;
    align-items: center;
    gap: 10px;
}

.member-search {
    margin-bottom: 20px;
}

.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.member-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.member-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.member-card-content {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.member-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--el-color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.member-info {
    flex: 1;
    min-width: 0;
}

.member-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.member-role {
    font-size: 13px;
    color: var(--el-text-color-secondary);
}

.member-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

/* 响应式布局调整 */
@media screen and (max-width: 1023px) {
    .members-grid {
        grid-template-columns: 1fr !important;
    }
    
    .member-form {
        flex-direction: column !important;
        gap: 10px !important;
    }
    
    .member-form .el-form-item {
        margin-right: 0 !important;
        width: 100% !important;
    }
    
    .member-card-content {
        flex-direction: column !important;
        align-items: center !important;
    }
    
    .member-actions {
        margin-top: 10px !important;
        justify-content: center !important;
    }
} 

.quick-task-date {
    position: relative;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    width: fit-content;  /* 添加这个确保宽度适应内容 */
}
.el-form-item{
    margin-bottom: 0px;
}
.el-form--inline .el-form-item{
        margin-right: 12px;

}
/* 任务筛选区域样式 */
.filter-item-style {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 15px;
    /*padding: 10px 15px;*/
    /* background-color: #f9f9fa; */
    border-radius: 6px;
    /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); */
    flex-wrap: wrap;
    gap: 10px;
}

.filter-item-style span {
    white-space: nowrap;
}

.filter-item-style .el-select,
.filter-item-style .el-date-picker {
    margin-right: 15px;
}

@media (max-width: 1023px) {
    .filter-item-style {
        flex-direction: column !important;
    }
    .filter-item-style > * {
        margin-bottom: 10px !important;
        width: 100% !important;
    }
} 

/* 任务统计视图样式 */
.task-stats-container {
    /*background: #f5f7fa;*/
    display: flex;
    flex-direction: column;
    height: calc(100vh - 100px);
    padding: 16px;
    margin: 0;
}

/* 筛选区域样式 */
.stats-filter-container {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    border: none;
    flex-shrink: 0;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

/* 筛选行样式 */
.stats-filter-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

/* 统计卡片样式 */
.stats-cards-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    gap: 16px;
}

/* 统计卡片项目 */
.stats-card-item {
    flex: 1;
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-width: 120px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

/* 蓝色卡片 */
.blue-card {
    background-color: #e6f1fc;
    color: #409EFF;
}

/* 绿色卡片 */
.green-card {
    background-color: #e7f6e8;
    color: #67C23A;
}

/* 红色卡片 */
.red-card {
    background-color: #fdeaeb;
    color: #F56C6C;
}

/* 统计卡片数值 */
.stats-card-value {
    font-size: 26px;
    font-weight: bold;
    /*margin-bottom: 8px;*/
}

/* 统计卡片标签 */
.stats-card-label {
    font-size: 14px;
}

/* 主内容区域样式 */
.stats-main-content {
    flex: 1; /* 占据剩余空间 */
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 4px;
    overflow: visible; /* 或直接移除上面那行 */
    /*box-shadow: 0 1px 4px rgba(0,0,0,0.05);*/
    min-height: 0; /* 添加此项 */
}

/* 表格容器样式 */
.stats-table-container {
    flex: 1; /* 占据 stats-main-content 内的剩余空间 */
    /*overflow-y: auto; !* 添加/修改此行 *!*/
    min-height: 0; /* 添加此项 */
}

/* 表格基本样式 */
.task-stats-container .el-table {
    border: none !important; /* 确保表格本身无边框 */
    margin-top: 0;
}

/* 表头样式 */
.task-stats-container .el-table th {
    background-color: #f5f7fa !important;
    color: #606266;
    font-weight: 500;
    text-align: center;
    padding: 12px 0 !important;
    height: 48px !important;
    border-bottom: none !important; /* 移除表头底部边框 */
    border-left: none !important; /* 移除表头左边框 */
    border-right: none !important; /* 移除表头右边框 */
    border-top: none !important; /* 移除表头顶部边框 */
}

/* 表头单元格样式 */
.task-stats-container .el-table th.is-leaf {
    background-color: #f5f7fa !important;
    position: sticky;
    top: 0;
    z-index: 2;
    border-bottom: none !important; /* 再次确保移除表头底部边框 */
}

/* 固定表头 */
.task-stats-container .el-table__header-wrapper {
    position: sticky;
    top: 0;
    z-index: 2;
    border: none !important; /* 移除表头包装器的边框 */
}

/* 表格单元格样式 */
.task-stats-container .el-table td,
.task-stats-container .el-table td.el-table__cell {
    text-align: center;
    padding: 10px 0;
    height: 50px;
    border-bottom: none !important; /* 移除单元格底部边框 */
    border-left: none !important; 
    border-right: none !important;
    border-top: none !important;
}

/* 移除Element Plus表格的::before和::after伪元素边框 */
.task-stats-container .el-table::before,
.task-stats-container .el-table::after {
    display: none !important;
}

/* 确保移除el-table--border类引入的边框 */
.task-stats-container .el-table--border th,
.task-stats-container .el-table--border td,
.task-stats-container .el-table--border .el-table__cell {
    border: none !important;
}

.task-stats-container .el-table--border .el-table__header-wrapper {
    border: none !important;
}

.task-stats-container .el-table__border-left-patch {
    display: none !important; /* 移除左侧补丁边框 */
}

/* 表格行高 */
.stats-table-container .el-table .el-table__row {
    height: 50px;
}

/* 表格斑马纹效果 */
.task-stats-container .el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: #f9fafc;
}

/* 表格悬停效果 */
.task-stats-container .el-table .el-table__row:hover > td {
    background-color: #f5f7fa;
}

/* 数字标签样式 - 按照原型图设计 */
.stats-num-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 30px;
    height: 30px;
    text-align: center;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 500;
    padding: 0 8px;
}

/* 第一列样式 - 负责人姓名 */
.task-stats-container .el-table td:first-child .cell {
    color: #303133;
    font-weight: 500;
}

/* 第二列样式 - 总任务数 */
.stats-num-value {
    font-size: 15px;
    font-weight: 500;
    color: #F05123;
}

/* 各种标签颜色 - 与原型图匹配 */
.tag-primary {
    /*background-color: #e6f1fc;*/
    color: #409EFF;
}

.tag-success {
    /*background-color: #e7f6e8;*/
    color: #67C23A;
}

.tag-warning {
    /*background-color: #fdf5e9;*/
    color: #E6A23C;
}

.tag-danger {
    /*background-color: #fdeaeb;*/
    color: #F56C6C;
}

/* 分页容器样式 */
.stats-pagination-container {
    padding: 12px 16px;
    display: flex;
    border-top: 1px solid #ebeef5;
    background-color: #fff;
    margin-top: auto;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
    z-index: 10;
}

/* 分页控件样式 */
.stats-pagination-container .el-pagination {
    justify-content: flex-end;
    padding: 0;
    font-weight: normal;
}

/* 分页按钮样式 */
.stats-pagination-container .el-pagination .btn-prev,
.stats-pagination-container .el-pagination .btn-next {
    padding: 0 8px;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    margin: 0 4px;
}

/* 分页数字按钮样式 */
.stats-pagination-container .el-pagination .el-pager li {
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    margin: 0 4px;
    min-width: 28px;
}

/* 当前页码样式 */
.stats-pagination-container .el-pagination .el-pager li.active {
    background-color: #F05123;
    color: white;
    border-color: #F05123;
}

/* 确保元素使用border-box盒模型 */
.stats-table-container * {
    box-sizing: border-box;
}

/* 响应式样式 */
@media screen and (max-width: 1023px) {
    .task-stats-container {
        padding: 10px !important;
    }
    
    .stats-filter-container {
        padding: 0 !important;
    }
    
    .stats-pagination-container .el-pagination {
        flex-wrap: wrap !important;
    }
    
    .stats-table-container {
        overflow-x: auto !important;
    }
    
    .stats-cards-wrapper {
        flex-wrap: wrap !important;
        justify-content: center !important;
    }
    
    .stats-card-item {
        width: 47% !important;
        margin: 5px !important;
    }
    
    .stats-card-value {
        font-size: 20px !important;
    }
} 

/* 确保表格内容居中对齐 */
.task-stats-container .el-table .cell {
    text-align: center !important;
    white-space: nowrap;
}

.task-stats-container .el-table th > .cell {
    text-align: center !important;
}

.task-stats-container .el-table__cell {
    text-align: center !important;
} 

/* 组织架构树对话框样式 */
.org-dialog-container {
    display: flex;
    height: 500px;
    gap: 20px;
}

/* 左侧面板样式 */
.org-left-panel {
    width: 300px;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
}

.org-search-box {
    padding: 10px;
    border-bottom: 1px solid #e4e7ed;
}

.org-breadcrumb {
    padding:  10px;
    border-bottom: 1px solid #e4e7ed;
    /*background-color: #f5f7fa;*/
}

.org-breadcrumb .el-breadcrumb__item {
    cursor: pointer;
}

.org-select-all {
    padding: 0 12px;
    /*border-bottom: 1px solid #e4e7ed;*/
    /*background-color: #f5f7fa;*/
}

.org-list-container {
    flex: 1;
    overflow: auto;
    /*padding: 10px 0;*/
}

.org-list-title {
    padding: 0px 10px;
    color: #909399;
    font-size: 14px;
}

.org-search-results {
    padding: 0 10px;
}

.org-search-section {
    margin-bottom: 15px;
}

.org-search-title {
    padding: 5px;
    color: #909399;
    font-size: 14px;
}

/* 部门项样式 */
.org-dept-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.org-dept-item:hover {
    background-color: #f5f7fa;
}

.org-dept-icon {
    color: #F05123 ;
    /*margin-right: 8px;*/
}

.org-dept-name {
    flex: 1;
}

.org-dept-count {
    color: #909399;
    font-size: 12px;
    margin: 0 5px;
}

.org-dept-arrow {
    color: #c0c4cc;
}

/* 用户项样式 */
.org-user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 3px 10px;
    transition: background-color 0.2s;
}

.org-user-item:hover {
    background-color: #f5f7fa;
}

.org-user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.org-user-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #F05123 ;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.org-user-details {
    display: flex;
    flex-direction: column;
}

.org-user-name {
    /*font-weight: bold;*/
}

.org-user-title {
    font-size: 12px;
    color: #909399;
}

/* 右侧面板样式 */
.org-right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
}

.org-selected-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    /*border-bottom: 1px solid #e4e7ed;*/
    /*background-color: #f5f7fa;*/
}

.org-selected-title {
    /*font-weight: bold;*/
}

.org-selected-count {
    color: #F05123 ;
}

.org-selected-list {
    flex: 1;
    overflow: auto;
    padding: 10px;
}

.org-selected-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.org-selected-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #F05123 ;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 8px;
}

.org-selected-name {
    flex: 1;
}

.org-selected-remove {
    color: #909399;
}

/* 抄送人表单项样式 */
.cc-users-container {
    display: flex;
    width: 100%;
    align-items: flex-start;
    gap: 10px;
}

.cc-users-tags-container {
    flex: 1;
    min-height: 36px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 4px 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

.cc-user-tag {
    margin-right: 5px;
    margin-bottom: 3px;
}

.cc-users-input {
    border: none;
    outline: none;
    min-width: 80px;
    flex: 1;
    height: 28px;
    font-size: 14px;
    color: #606266;
    background: transparent;
}

.select-cc-btn {
    flex-shrink: 0;
}

/* 对话框底部样式 */
.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.selected-count {
    margin-right: auto;
    color: #606266;
    font-size: 14px;
}

/* 富文本容器样式 */
.rich-text-container {
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-word;
    line-height: 1.4;
    font-size: 14px;
}

.rich-text-container img {
    max-height: 40px;
    max-width: 100%;
    object-fit: contain;
}

.rich-text-container p {
    margin: 0;
    padding: 0;
} 