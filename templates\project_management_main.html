{% extends "base_layout.html" %}

{% block title %}任务管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ cdn_static('css/element-plus.css') }}">
<link rel="stylesheet" href="{{ cdn_static('css/project_management_main.css') }}">
<link rel="stylesheet" href="{{ cdn_static('css/project_management_taskflow_theme.css') }}">
<link rel="stylesheet" href="{{ cdn_static('css/pm_templates.css') }}">
<!-- 添加移动端项目管理样式 -->
<link rel="stylesheet" href="{{ cdn_static('css/project_management_mobile.css') }}">
<!-- 引入Wang富文本编辑器样式 -->
<link rel="stylesheet" href="{{ cdn_static('css/wangeditor.css') }}">

<!-- 添加JS哈希值到window对象 -->
<script>
window.cdn_js_hash = {{ cdn_js_hash|tojson|safe }};
</script>

<!-- 添加自定义样式 -->
<style>
.el-overlay-message-box .el-message-box {
    width: 700px !important;
    max-width: 90vw !important;
}
.el-message-box__container {
    display: block !important;
}
.quick-task-assignee {
    margin-left: 2px;
}

/* 添加已选负责人容器样式 */
/* .selected-assignees-container {
    margin-top: 8px;
    padding: 5px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.selected-assignees-title {
    font-size: 12px;
    color: #606266;
    margin-bottom: 4px;
}

.selected-assignees-list {
    display: flex;
    flex-wrap: wrap;
    max-height: 100px;
    overflow-y: auto;
} */

.task-complete-rich-dialog .el-message-box__message {
    overflow: visible;
}

{#.task-complete-rich-dialog .el-message-box__container {
    min-width: 700px;
}#}

.rich-editor-dialog {
    width: 100%;
    padding: 10px 0;
}

.rich-editor-toolbar {
    border: 1px solid #dcdfe6;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
}

.rich-editor-content {
    min-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 0 0 4px 4px;
    padding: 8px;
}

.rich-editor-container {
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
}

.w-e-bar {
    z-index: 2600 !important;
}

.w-e-text-container {
    z-index: 2500 !important;
}

.w-e-drop-panel {
    z-index: 2550 !important;
}

.w-e-modal {
    z-index: 2650 !important;
}

.w-e-text img {
    max-width: 100%;
    height: auto !important;
}

/* 自定义对话框头部样式 */
.el-dialog__header {
    padding-bottom: 5px !important;
}
.el-dialog__footer{
    padding-top: 10px !important; 
}
/* 模板任务富文本编辑器样式 */
.template-rich-editor {
    width: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
}
.template-rich-editor .editor-toolbar {
    border-bottom: 1px solid #dcdfe6;
}
.template-rich-editor .editor-content {
    min-height: 150px;
    max-height: 400px;
    overflow-y: auto;
    width: 100%;
    height: 350px;
}

/* 任务富文本编辑器样式 */
.task-rich-editor {
    width: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: visible; /* 修改为visible，避免截断下拉菜单 */
}
.task-rich-editor .editor-toolbar {
    border-bottom: 1px solid #dcdfe6;
}
.task-rich-editor .editor-content {
    min-height: 150px;
    max-height: 400px;
    overflow-y: auto;
    width: 100%;
    height: 350px;
}

/* 添加箭头旋转动画样式 */
.is-rotate {
    transform: rotate(180deg);
}

/* 任务日志中的HTML内容样式 */
.log-content img {
    max-width: 100%;
    height: auto !important;
    margin: 8px 0;
}

.log-content p {
    margin-bottom: 6px;
}

.log-content ul, .log-content ol {
    padding-left: 24px;
    margin-bottom: 6px;
}

/* 日志项样式 */
.log-item {
    position: relative;
    padding-right: 40px; /* 为删除按钮留出空间 */
}

.log-delete-btn {
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.log-delete-btn:hover {
    opacity: 1;
}

.quick-task-date {
    margin-left: 2px;
}

.quick-task-project {
    margin-left: 2px;
}

.quick-task-urgency {
    margin-left: 2px;
}

.quick-task-assignee .assignee-button {
    color: #606266;
    font-size: 13px;
    padding: 0 4px;
    height: 28px;
    display: flex;
    align-items: center;
    gap: 3px;
    transition: all 0.3s;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    background-color: transparent;
}

.quick-task-assignee .assignee-button:hover {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary);
}

.quick-task-urgency .assignee-button {
    color: #606266;
    font-size: 13px;
    padding: 0 4px;
    height: 28px;
    display: flex;
    align-items: center;
    gap: 3px;
    transition: all 0.3s;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    background-color: transparent;
}

.quick-task-urgency .assignee-button:hover {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary);
}

.quick-task-assignee-icon {
    font-size: 14px !important;
}

.quick-task-urgency-icon {
    font-size: 14px !important;
}

.quick-task-assignee-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70px;
}
.assignee-button {
    border: none;
    background: transparent;
    display: flex;
    align-items: center;
    color: #606266;
    padding: 0;
    height: 100%;
}
.assignee-popover .el-popover__title {
    color: #606266;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
}
.assignee-select-list {
    max-height: 300px;
    overflow-y: auto;
}
.assignee-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    transition: all 0.3s;
}
.assignee-item:hover {
    background-color: #f5f7fa;
}
.assignee-item.active {
    background-color: #fff0eb;
    color: #F05123;
}
.assignee-item-name {
    flex: 1;
    margin-right: 8px;
}
.assignee-item-dept {
    color: #909399;
    font-size: 12px;
}
.assignee-item-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.assignee-item-status {
    font-size: 12px;
    color: #67c23a;
    margin-top: 2px;
}
.assignee-item-status.not-member {
    color: #e6a23c;
}

.nav-badge {
    position: absolute;
    right: 10px;
    top: 60%;
    transform: translateY(-50%) scale(0.8);
}
.nav-badge .el-badge__content {
    border: none;
}

.approval-details {
    padding: 0 10px;
}

.approval-details h3 {
    margin: 20px 0 10px 0;
    font-size: 16px;
    color: #303133;
    font-weight: 500;
}

.info-section, .approval-status-section, .user-action-section {
    margin-bottom: 20px;
}

.el-table .el-button + .el-button {
    margin-left: 5px;
}

.task-approval-container {
    padding: 20px;
}

.approval-section {
    margin-bottom: 20px;
}

/* 新增样式 */
.logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    height: 160px;
    box-sizing: border-box;
}

.logo-image {
    width: 120px;
    height: 64.5px;
    margin-bottom: 10px;
    object-fit: contain;
}

.panel-name {
    color: white;
    font-size: 20px;
    font-weight: bold;
    width: 140px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* 添加侧边栏菜单项的字体样式 */
.sidebar .nav-item .nav-label {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
}

.projects-header {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.project-item {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

.task-item-meta-info {
    margin-left: 10px;
    font-size: 12px;
    color: #606266;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}

.task-creator, .task-assignees {
    margin-right: 8px;
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.task-deadline {
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.task-item-project {
    display: flex;
    align-items: center;
    margin-top: 3px;
    flex-wrap: wrap;
    gap: 5px;
}

/* Override the project link color in quick task sections to use the standard text color */
.task-list .task-item-project .el-link.el-link--primary {
    color: #606266 !important;
}

.task-list .task-item-project .el-link.el-link--primary:hover {
    color: #909399 !important;
}

/* 图片查看器样式 */
.image-viewer {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    touch-action: none;
    -webkit-user-select: none;
    user-select: none;
}

.image-viewer-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.image-viewer img {
    max-width: 100%;
    max-height: 100vh;
    object-fit: contain;
    transform-origin: center center;
    will-change: transform;
    touch-action: none;
    transition: transform 0.1s ease-out;
    cursor: grab;
}

.image-viewer img:active {
    cursor: grabbing;
}

.image-viewer.dragging img {
    cursor: grabbing;
    transition: none;
}

/* 关闭按钮样式 */
.image-viewer .close-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    line-height: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-tap-highlight-color: transparent;
}

.image-viewer .close-btn::before {
    content: "×";
    font-size: 28px;
    font-weight: 700;
    display: block;
    margin-top: -2px;
    text-align: center;
    width: 100%;
    height: 100%;
    line-height: 40px;
}

.image-viewer .close-btn:hover {
    background-color: white;
    transform: scale(1.1);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .image-viewer .close-btn {
        top: 10px;
        right: 10px;
        width: 36px;
        height: 36px;
    }

    .image-viewer .close-btn::before {
        font-size: 24px;
        line-height: 36px;
    }
}

/* 审批图标样式 */
.approval-icon-container {
    cursor: pointer;
    margin-right: 10px;
}

.approval-icon-container .el-button.is-circle {
    background-color: rgb(26, 115, 232) !important;
    border-color: rgb(26, 115, 232) !important;
}

.approval-icon-container .el-button.is-circle:hover {
    background-color: #d84010;
    border-color: #d84010;
}

/* 任务完成对话框样式 */
.task-complete-dialog .el-message-box__wrapper {
    max-width: 550px;
    margin: 0 auto;
}

/* 抄送人功能相关样式 */
.cc-users-container {
    display: flex;
    width: 100%;
    align-items: flex-start;
}

.cc-users-tags-container {
    flex: 1;
    min-height: 36px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 4px 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
    /* margin-right: 10px; */
}

.cc-user-tag {
    margin-right: 5px;
    margin-bottom: 3px;
}

.cc-users-input {
    border: none;
    outline: none;
    min-width: 80px;
    flex: 1;
    height: 28px;
    font-size: 14px;
    color: #606266;
    background: transparent;
}

.select-cc-btn {
    flex-shrink: 0;
}

/* 组织架构对话框样式 */
.org-dialog-container {
    display: flex;
    min-height: 400px;
    gap: 15px;
}

.org-dept-tree {
    width: 40%;
    border-right: 1px solid #e6e6e6;
    padding-right: 15px;
    display: flex;
    flex-direction: column;
}

.org-user-list {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.org-tree-search, .org-user-search {
    margin-bottom: 10px;
}

.org-tree-content, .org-user-content {
    flex: 1;
    overflow: auto;
}

.current-dept-name {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.user-count {
    font-size: 13px;
    color: #909399;
    font-weight: normal;
    margin-left: 5px;
}

.user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-item:hover {
    background-color: #f5f7fa;
}

.user-item.selected {
    background-color: #f0f9eb;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 14px;
    margin-bottom: 3px;
}

.user-title {
    font-size: 12px;
    color: #909399;
}

.selected-users-summary {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
}

.selected-users-count {
    margin-bottom: 10px;
    font-size: 14px;
}

.count-value {
    font-weight: bold;
    color: #F05123;
}

.selected-users-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    max-height: 100px;
    overflow-y: auto;
    padding: 5px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
}

.task-complete-dialog .el-message-box__wrapper {
    max-width: 550px;
    margin: 0 auto;
}

/* 添加已选负责人容器样式 */
.selected-assignees-container {
    margin-top: 8px;
    margin-bottom: 8px;
    padding: 5px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
}

.selected-assignees-title {
    font-size: 12px;
    color: #606266;
    margin-bottom: 4px;
}

.selected-assignees-list {
    display: flex;
    flex-wrap: wrap;
    max-height: 100px;
    overflow-y: auto;
}

/* 统计任务对话框样式 */
.stats-task-dialog .el-dialog {
    margin-top: 8vh !important;
}

.stats-task-dialog .el-dialog__body {
    padding: 20px;
}

.stats-task-dialog .el-table {
    margin-bottom: 20px;
}

.stats-task-dialog .pagination-container {
    padding: 10px 0;
}
</style>
{% endblock %}

{% block content %}
<!-- 初始化数据 -->
<script id="init-data" type="application/json">
{
    "userData": {
        "name": "{{ user_name|safe }}",
        "role": "{{ user_role|safe }}"
    },
    "projectsData": {{ projects|tojson|safe }},
    "currentProjectData": {{ current_project|tojson|safe }},
    "viewTypeData": "{{ view_type or 'stats' }}",
    "searchQueryData": "{{ search_query or '' }}",
    "tasksData": {{ tasks|tojson|safe }},
    "totalTasks": {{ totalTasks or 0 }},
    "selectedTaskId": {% if selected_task_id %}{{ selected_task_id }}{% else %}null{% endif %}
}
</script>

<div id="app" v-cloak>
{% raw %}
    <!-- Vue模板开始 -->
    <div>
        <div class="project-content-wrapper">
            <!-- 侧边栏 (Sidebar) -->
            <div class="sidebar">
                <!-- 修改: 从用户信息改为Logo和板块名称 -->
                <div class="logo-section">
                    <img src="/static/images/logo.png" alt="Logo" class="logo-image">
                    <div class="panel-name">任务管理</div>
                </div>
                
                <div class="nav-menu">
                    <div 
                        v-for="item in navItems" 
                        :key="item.value"
                        class="nav-item" 
                        :class="{ active: currentView === item.value }"
                        @click="changeView(item.value)"
                        style="position: relative;">
                        <el-icon class="nav-icon"><component :is="item.icon" /></el-icon>
                        <span class="nav-label">{{ item.label }}</span>
                        <el-badge v-if="item.badge > 0" :value="item.badge" class="nav-badge" type="danger" />
                    </div>
                </div>
                
                <!-- 可见项目列表 -->
                <div class="projects-list">
                    <div class="projects-list-fixed">
                        <div class="projects-header">
                            可见项目列表
                        </div>
                        <el-input 
                            v-model="projectSearch" 
                            placeholder="搜索项目..." 
                            class="project-search">
                        </el-input>
                    </div>
                    
                    <div class="projects-list-scrollable">
                        <template v-if="projects.length > 0">
                            <div 
                                v-for="project in filteredProjects"
                                :key="project.id"
                                class="project-item"
                                :class="{ current: project.id === currentProject.id && currentView === 'project' }"
                                @click="selectProject(project)">
                                <el-icon v-if="project.id === currentProject.id && currentView === 'project'" class="project-selected-icon"><Check /></el-icon>
                                {{ project.name }}
                                <span v-if="project.role_info && project.role_info !== '边缘人权限'" class="special">({{ project.role_info }})</span>
                                <span v-else-if="project.role_info && project.role_info === '边缘人权限'" class="special">(成员权限)</span>
                            </div>
                            
                            <!-- 无匹配项提示 -->
                            <el-empty 
                                v-if="filteredProjects.length === 0"
                                description="未找到匹配项目" 
                                :image-size="60" />
                        </template>
                        
                        <!-- 无项目提示 -->
                        <el-empty 
                            v-else 
                            description="暂无可见项目" 
                            :image-size="60">
                            <template #default>
                                <p style="font-size: 12px; color: rgba(255,255,255,0.5); margin-top: 10px;">
                                    请联系管理员为您添加项目
                                </p>
                            </template>
                        </el-empty>
                    </div>
                </div>
            </div>
            
            <!-- 主界面内容区 (Main Content Area) -->
            <div class="project-main-area" id="projectMainArea">
                <div class="action-buttons">
                    <template v-if="currentView === 'completed'">
                           <span style=" font-weight: 500;">查询：</span>
                        <el-form :inline="true" @submit.prevent="statusFilters">
                            <el-form-item>
                                <el-input  style="width: 206px"
                                    v-model="completedSearchQuery" 
                                    placeholder="请输入关键字..."
                                    clearable />
                            </el-form-item>
                            <el-form-item>
                                <el-button  style="width: 80px" type="primary" @click="statusFilters">搜索</el-button>
                            </el-form-item>
                        </el-form>
                        <!-- <div class="filter-item-style"> -->
                                <span style=" align-content: center;font-weight: 500;">任务状态：</span>
                                <el-select
                                    v-model="taskStatus"
                                    placeholder="选择任务状态"
                                    @change="statusFilters"
                                    style="width: 120px;">
                                    <el-option label="全部" value="all"></el-option>

                                    <el-option label="已逾期完成" value="overdue_completed"></el-option>
                                    <el-option label="按时完成" value="on_time_completed"></el-option>
                                </el-select>
                                
                              
                            <!-- </div> -->

                    </template>
                    <template v-if="currentView === 'received'">
                        <div class="is-desktop">
                            <div style="width: 100%;">
                                <div style="display: flex;  align-items: center;">
                                    <span style=" font-weight: 500;">查询：</span>
                                    <el-form :inline="true" @submit.prevent="statusFilters">
                                        <el-form-item>
                                            <el-input style="width: 206px"
                                                v-model="receivedSearchQuery" 
                                                placeholder="请输入关键字..."
                                                clearable
                                                @keyup.enter="statusFilters" />
                                        </el-form-item>
                                        <el-form-item>
                                            <el-button style="width: 80px" type="primary" @click="statusFilters">搜索</el-button>
                                        </el-form-item>
                                    </el-form>
                                    <span style="align-content: center;font-weight: 500;">任务状态：</span>
                                    <el-select
                                        v-model="receivedTaskStatus"
                                        placeholder="选择任务状态"
                                        @change="statusFilters"
                                        style="width: 150px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="今日到期" value="due_today"></el-option>
                                        <el-option label="进行中" value="in_progress"></el-option>
                                        <el-option label="未完成" value="incomplete"></el-option>
                                        <el-option label="已完成" value="completed"></el-option>
                                        <el-option label="已逾期未完成" value="overdue"></el-option>
                                        <el-option label="已逾期完成" value="overdue_completed"></el-option>
                                        <el-option label="按时完成" value="on_time_completed"></el-option>
                                    </el-select>
                                    <span style="margin-left: 15px; font-weight: 500; align-content: center;" >截止月份：</span>
                                    <el-date-picker
                                        v-model="receivedSelectedMonth"
                                        type="month"
                                        placeholder="按截止月份筛选"
                                        format="YYYY-MM"
                                        value-format="YYYY-MM"
                                        @change="handleReceivedMonthChange"
                                        style="width: 190px;">
                                    </el-date-picker>
                                </div>
                                <!-- 添加任务类型筛选按钮组 -->
                                <div class="filter-item-style">
                                    <span style="font-weight: 500;align-content: center;">任务类型：</span>
                                    <el-button-group >
                                        <el-button
                                            :type="receivedTaskType === 'assigned' ? 'primary' : 'default'" 
                                            @click="toggleTaskTypeFilter('assigned')"
                                
                                            :style="receivedTaskType === 'assigned' ? '' : 'border-color: rgb(240, 81, 35); color: rgb(240, 81, 35);'">
                                            我负责的
                                        </el-button>
                                        <el-button style="margin-left: 10px;"
                                            :type="receivedTaskType === 'created' ? 'primary' : 'default'" 
                                            @click="toggleTaskTypeFilter('created')"
                                            :style="receivedTaskType === 'created' ? '' : 'border-color: rgb(240, 81, 35); color: rgb(240, 81, 35);'">
                                            我创建的
                                        </el-button>
                                        <el-button style="margin-left: 10px;"
                                            :type="receivedTaskType === 'cc' ? 'primary' : 'default'" 
                                            @click="toggleTaskTypeFilter('cc')"
                                            :style="receivedTaskType === 'cc' ? '' : 'border-color: rgb(240, 81, 35); color: rgb(240, 81, 35);'">
                                            被抄送的
                                        </el-button>
                                    </el-button-group>
                                </div>
                            </div>
                        </div>
                        <div class="is-mobile">
                            {% endraw %}
                            {% include 'project_management_mobile_received.html' %}
                            {% raw %}
                        </div>
                    </template>
                    <template v-if="currentView === 'search'">
                        <span style=" font-weight: 500;">查询：</span>
                        <el-form :inline="true" @submit.prevent="searchTasks">
                            <el-form-item>
                                <el-input  style="width: 206px"
                                    v-model="searchQuery" 
                                    placeholder="请输入关键字..."
                                    clearable />
                            </el-form-item>
                            <el-form-item>
                                <el-button  style="width: 80px" type="primary" @click="searchTasks">搜索</el-button>
                            </el-form-item>
                        </el-form>
                    </template>
                    <template v-else-if="currentView === 'manage'">
                        <div class="search-box" style="display: flex; align-items: center; margin-right: 10px;">
                            <el-input
                                v-model="manageProjectSearch"
                                placeholder="搜索项目名称或描述..."
                                style="width: 250px; margin-right: 10px;"
                                clearable
                                @keyup.enter="searchProjects">
                                <template #prefix>
                                    <el-icon><Search /></el-icon>
                                </template>
                            </el-input>
                            <el-button type="primary" @click="searchProjects" style="width: 80px;">
                                <!-- <el-icon><Search /></el-icon>  -->搜索
                            </el-button>
                        </div>
                        <el-button type="success" @click="showAddProjectDialog"  style="width: 80px;">
                            <el-icon><Plus /></el-icon> 新增项目
                        </el-button>
                    </template>
                    <template v-else-if="currentView === 'templates'">
                        <el-button type="primary" @click="showAddTemplateDialog" class="add-template-button">
                            <el-icon><Plus /></el-icon> 新增模板
                        </el-button>
                    </template>
                    <template v-else-if="currentView === 'template_details'">
                        <div style="display: flex; justify-content: space-between; width: 100%; align-items: center;">
                            <div>
                                <el-button @click="showAddTemplateTaskDialog"><el-icon><Plus /></el-icon> 添加任务</el-button>
                                <el-button 
                                    @click="showAddTemplateSubTaskDialog" 
                                    :disabled="!selectedTemplateTask || selectedTemplateTask.is_child"
                                    :title="!selectedTemplateTask ? '请先选择一个主任务' : (selectedTemplateTask.is_child ? '子任务不能添加子任务' : '添加子任务')">
                                    <el-icon><Plus /></el-icon> 添加子任务
                                </el-button>
                                <el-button :disabled="!selectedTemplateTask" @click="showEditTemplateTaskDialog"><el-icon><Edit /></el-icon> 编辑任务</el-button>
                                <el-button :disabled="!selectedTemplateTask" @click="deleteTemplateTask"><el-icon><Delete /></el-icon> 删除任务</el-button>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="currentView === 'home'">
                        <div style="display: flex; justify-content: flex-end; width: 100%;">
                            <div v-if="homeApprovalBadgeCount > 0" class="approval-icon-container" @click="changeView('approval')">
                                <el-tooltip content="任务审批" placement="bottom">
                                    <el-badge :value="homeApprovalBadgeCount" :max="99" type="danger">
                                        <el-button type="primary" circle>
                                            <el-icon><Stamp /></el-icon>
                                        </el-button>
                                    </el-badge>
                                </el-tooltip>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="currentView === 'task_stats' || currentView === 'stats' || currentView === 'completed' || currentView === 'approval'|| currentView === 'received'">
                        <!-- 项目统计、已完成的任务视图和任务审批视图不显示按钮 -->
                    </template>
                    <template v-else>
                        <div style="width: 100%;">

                            <div style="display: flex; justify-content: space-between; width: 100%; align-items: center;">
                                <div>
                                    <el-button @click="showAddTaskDialog"><el-icon><Plus /></el-icon> 添加任务</el-button>
                                    <el-button 
                                        @click="showAddSubTaskDialog" 
                                        :disabled="!drawer.taskId || (drawer.task && drawer.task.is_child)"
                                        :title="!drawer.taskId ? '请先选择一个任务' : (drawer.task && drawer.task.is_child) ? '子任务不能添加子任务' : '添加子任务'">
                                        <el-icon><Plus /></el-icon> 添加子任务
                                    </el-button>
                                    <el-button :disabled="!drawer.taskId" @click="showEditTaskDialog">
                                        <el-icon><Edit /></el-icon> 编辑任务
                                    </el-button>
                                    <el-button :disabled="!drawer.taskId" @click="() => deleteTask(drawer.task)">
                                        <el-icon><Delete /></el-icon> 删除任务
                                    </el-button>
                                    <el-button @click="showAddHistoricalTaskDialog">
                                        <el-icon><Calendar /></el-icon> 登记未完成
                                    </el-button>
                                    <el-button @click="showImportTemplateDialog">
                                        <el-icon><Download /></el-icon> 导入模板
                                    </el-button>
                                    <el-button type="success" @click="exportProjectTasks" :loading="exportLoading">
                                        <el-icon><Download /></el-icon> 导出Excel
                                    </el-button>
                                </div>
                            </div>
                            
  <div class="filter-item-style">
                                 <span style=" font-weight: 500;">查询：</span>
                                 <el-form :inline="true" @submit.prevent="handleProjectTaskSearch"> <!-- 修改了submit.prevent的目标 -->
                                    <el-form-item>
                                        <el-input  style="width: 206px"
                                                v-model="projectTaskSearchQuery"
                                                placeholder="请输入关键字..."
                                                clearable
                                                @keyup.enter="handleProjectTaskSearch"/>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button  style="width: 80px" type="primary" @click="handleProjectTaskSearch">搜索</el-button>
                                    </el-form-item>
                                </el-form>
                                <span style="align-content: center; font-weight: 500;">任务状态：</span>
                                <el-select
                                    v-model="taskStatusFilter"
                                    placeholder="选择任务状态"
                                    @change="handleTaskStatusChange"
                                    style="width: 150px;">
                                    <el-option label="全部" value="all"></el-option>
                                    <el-option label="今日到期" value="due_today"></el-option>
                                    <el-option label="按时完成" value="on_time_completed"></el-option>
                                    <el-option label="进行中" value="in_progress"></el-option>
                                    <el-option label="未完成" value="incomplete"></el-option>
                                    <el-option label="已完成" value="completed"></el-option>
                                    <el-option label="已逾期未完成" value="overdue"></el-option>
                                    <el-option label="已逾期完成" value="overdue_completed"></el-option>
                                </el-select>
                                <span style=" font-weight: 500;">创建日期筛选：</span>
                                <el-date-picker
                                    v-model="selectedCreateMonth"
                                    type="month"
                                    placeholder="按创建月份筛选"
                                    format="YYYY-MM"
                                    value-format="YYYY-MM"
                                    @change="(value) => handleMonthChange(value, 'create')"
                                    style="width: 190px;">
                                </el-date-picker>
       <!--
                                <span style=" font-weight: 500;">截至日期筛选：</span>
                                <el-date-picker
                                    v-model="selectedMonth"
                                    type="month"
                                    placeholder="按月份筛选"
                                    format="YYYY-MM"
                                    value-format="YYYY-MM"
                                    @change="(value) => handleMonthChange(value, 'deadline')"
                                    style="width: 140px;">
                                </el-date-picker>-->
                            </div>
                        </div>
                    </template>
                </div>
                
                <h2 v-if="pageTitle">{{ pageTitle }}</h2>
                
                <Transition name="content-fade" mode="out-in">
                    <div :key="currentView">
                        <!-- 项目管理视图 -->
                        <template v-if="currentView === 'manage'">
                            <!-- 有项目时显示网格布局 -->
                            <div class="project-management-grid" v-if="filteredManageProjects.length > 0">
                                <div v-for="project in filteredManageProjects" :key="project.id" class="project-card" @click="openProjectManagement(project)">
                                    <div class="project-card-title">{{ project.name }}</div>
                                    <el-tooltip :content="project.description ? project.description.replace(/<[^>]*>/g, '') : '暂无项目描述'" placement="top" :show-after="500">
                                        <div class="project-card-desc rich-text-container" v-html="project.description || '暂无项目描述'"></div>
                                    </el-tooltip>
                                    <div class="project-card-footer">
                                        <div class="project-card-members">
                                            <el-icon><User /></el-icon> {{ project.member_count || 0 }} 名成员
                                        </div>
                                        <div class="project-card-actions">
                                            <el-button 
                                                type="primary" 
                                                size="small" 
                                                @click.stop="goToProject(project.id)">
                                                <el-icon><Document /></el-icon>
                                                进入项目
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 无项目时显示空状态 -->
                            <div v-else class="project-management-empty">
                                <el-empty 
                                    description="暂无可管理项目" 
                                    :image-size="140">
                                    <template #description>
                                        <p>您当前没有作为管理员的项目</p>
                                        <p style="margin-top: 8px; font-size: 14px; color: #909399;">
                                            可以点击右上角的"新增项目"按钮创建项目
                                        </p>
                                    </template>
                                </el-empty>
                            </div>
                        </template>
                        
                        <!-- 模板管理视图 -->
                        <template v-else-if="currentView === 'templates'">
                            <div v-loading="templateLoading">
                                <!-- 有模板时显示网格布局 -->
                                <div class="template-management-grid" v-if="filteredTemplates.length > 0">
                                    <div v-for="template in filteredTemplates" :key="template.id" class="template-card" @click="viewTemplate(template)">
                                        <div class="template-card-title">{{ template.name }}</div>
                                        <el-tooltip :content="template.description || '暂无模板描述'" placement="top" :show-after="500">
                                            <div class="template-card-desc">{{ template.description || '暂无模板描述' }}</div>
                                        </el-tooltip>
                                        <div class="template-card-footer">
                                            <div class="template-card-info">
                                                <el-icon><User /></el-icon> 创建人: {{ template.creator || '未知' }}
                                            </div>
                                            <div class="template-card-actions">
                                                <el-button 
                                                    type="primary" 
                                                    size="small" 
                                                    @click.stop="viewTemplate(template)">
                                                    <el-icon><Document /></el-icon>
                                                    查看
                                                </el-button>
                                                <el-button
                                                    type="primary"
                                                    size="small" 
                                                    @click.stop="showEditTemplateDialog(template)">
                                                    <el-icon><Edit /></el-icon>
                                                    编辑
                                                </el-button>
                                                <el-button 
                                                    type="primary"
                                                    size="small" 
                                                    @click.stop="deleteTemplate(template)">
                                                    <el-icon><Delete /></el-icon>
                                                    删除
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 无模板时显示空状态 -->
                                <div v-else class="template-management-empty">
                                    <el-empty 
                                        description="暂无可用模板" 
                                        :image-size="140">
                                        <template #description>
                                            <p>您当前没有任何模板</p>
                                            <p style="margin-top: 8px; font-size: 14px; color: #909399;">
                                                可以点击右上角的"新增模板"按钮创建模板
                                            </p>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </template>
                        
                        <!-- 模板详情视图 (复制自项目任务表格) -->
                        <template v-else-if="currentView === 'template_details'">
                             <div v-loading="templateTasksLoading">
                                <template v-if="templateTasks.length > 0">
                                    <el-table
                                        :data="paginatedTemplateTasks" 
                                        :row-class-name="getRowClass" 
                                        @row-click="handleTemplateTaskRowClick"
                                        highlight-current-row
                                        style="width: 100%"
                                        :empty-text="'模板中没有任务'">
                                        <el-table-column label="任务名称" prop="name" min-width="30%" fixed-width="false">
                                            <template #default="{row}">
                                                <div class="task-name" :style="row.is_child ? 'padding-left: 25px' : ''">
                                                    <!-- Remove checkbox, not applicable for templates -->
                                                    <span :class="{'task-name-text': true, 'is-milestone': row.is_milestone}">
                                                        {{ row.name }}
                                                        <el-tag v-if="row.is_milestone" size="small" type="warning" effect="light" style="margin-left: 5px;">里程碑</el-tag>
                                                    </span>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            label="紧急程度" 
                                            prop="urgency" 
                                            width="100">
                                            <template #default="{row}">
                                                <el-tag :type="row.urgency === '紧急' ? 'danger' : 'success'" effect="plain">
                                                    {{ row.urgency }}
                                                </el-tag>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            prop="assignees" 
                                            label="建议负责人/角色" 
                                            min-width="20%">
                                        </el-table-column>
                                        <el-table-column 
                                            prop="relative_deadline_days" 
                                            label="相对天数" 
                                            width="100">
                                            <template #default="{row}">
                                                <span v-if="row.relative_deadline_days !== null && row.relative_deadline_days !== undefined">
                                                    {{ row.relative_deadline_days }} 天
                                                </span>
                                                <span v-else>--</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            prop="description" 
                                            label="描述" 
                                            min-width="30%">
                                             <template #default="{row}">
                                                <el-tooltip :content="row.description ? row.description.replace(/<[^>]*>/g, '') : '无描述'" placement="top" :show-after="500">
                                                    <div class="rich-text-container" v-html="row.description || '-'"></div>
                                                </el-tooltip>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            fixed="right" 
                                            label="操作" 
                                            width="150">
                                            <template #default="{row, $index}">
                                                <!-- Placeholder buttons - functionality to be added later -->
                                                <el-button type="text" size="small" :disabled="$index === 0" @click.stop="moveTemplateTask(row, 'up')">上移</el-button>
                                                <el-button type="text" size="small" :disabled="$index === paginatedTemplateTasks.length - 1" @click.stop="moveTemplateTask(row, 'down')">下移</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    
                                    <!-- 模板任务分页组件 -->
                                    <div class="pagination-container">
                                        <el-pagination
                                            background
                                            layout="prev, pager, next, jumper, total"
                                            :page-size="templatePageSize"
                                            :current-page="currentTemplateTasksPage"
                                            :total="templateTasks.length"
                                            @current-change="handleTemplateTasksPageChange">
                                        </el-pagination>
                                    </div>
                                </template>
                                <template v-else>
                                     <el-empty description="此模板中没有定义任务" :image-size="100"></el-empty>
                                </template>
                            </div>
                        </template>
                        
                        <!-- 首页仪表盘视图 -->
                        <template v-else-if="currentView === 'home'">
                            <div class="home-dashboard" v-loading="homeLoading">
                                <!-- 头部问候与日期 -->
                                <div class="home-header">
                                    <div class="home-date">{{ formattedDate }}</div>
                                    <div class="home-greeting">{{ greeting }}，{{ userInfo.name }}</div>
                                </div>

                                <!-- 统计数据部分 -->
                                <div class="home-stats">
                                    <div class="stats-bar" :class="{ 'custom-date-active': statsTimeRange === 'custom' }">
                                        <div class="stats-period">
                                            <el-select v-model="statsTimeRange" size="small" class="period-select" @change="handleStatsTimeRangeChange">
                                                <el-option label="全部" value="all"></el-option>
                                                <el-option label="今天" value="today"></el-option>
                                                <el-option label="本周" value="week"></el-option>
                                                <el-option label="本月" value="month"></el-option>
                                                <el-option label="自定义" value="custom"></el-option>
                                            </el-select>
                                            <el-date-picker
                                                v-if="statsTimeRange === 'custom'"
                                                v-model="customDateRange"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期"
                                                format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD"
                                                size="small"
                                                style="margin-left: 10px; width: 240px; flex-shrink: 0;"
                                                @change="handleCustomDateRangeChange">
                                            </el-date-picker>
                                        </div>
                                        <div class="stats-divider"></div>
                                        <div class="stats-item" @click="handleStatsItemClick('unfinished')"style=" cursor: pointer; ">
                                            <span class="stats-value" >{{ statsData.unfinished }}</span>
                                            <span class="stats-label">未完成</span>
                                        </div>
                                        <div class="stats-item" @click="handleStatsItemClick('finished')"style=" cursor: pointer; ">
                                            <span class="stats-value">{{ statsData.finished }}</span>
                                            <span class="stats-label">已完成</span>
                                        </div>
                                        <div class="stats-item" @click="handleStatsItemClick('total')"style=" cursor: pointer; ">
                                            <span class="stats-value">{{ statsData.total }}</span>
                                            <span class="stats-label">{{ getStatsTimeRangeLabel() }}</span>
                                        </div>
                                        <div class="stats-item" @click="handleStatsItemClick('overdue')"style=" cursor: pointer; ">
                                            <span class="stats-value stats-overdue">{{ statsData.overdue }}</span>
                                            <span class="stats-label">已逾期</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 统计任务详情对话框 -->
                                <el-dialog
                                    v-model="statsTaskDialog.visible"
                                    :title="statsTaskDialog.title"
                                    width="1225px"
                                    :close-on-click-modal="false"
                                    :close-on-press-escape="false"
                                    class="task-list-dialog"
                                >
                                    <div v-loading="statsTaskDialog.loading">
                                        <el-table
                                            :data="statsTaskDialog.tasks"
                                           style="width: 100%;     height: 450px !important;    min-height: 450px !important;"
                                           :header-cell-style="{background:'#f8fafc',color:'#606266',fontWeight:'500'}"
                                           :cell-style="{padding: '8px 0'}"
                                        >
                                            <el-table-column
                                                type="index"
                                                label="序号"
                                                width="53"
                                                align="center">
                                            </el-table-column>
                          
                                            <el-table-column
                                                prop="name"
                                                label="任务名称"
                                                min-width="220"
                                                show-overflow-tooltip>
                                            </el-table-column>
                                            <el-table-column
                                                prop="project_name"
                                                label="所属项目"
                                                width="150"
                                                show-overflow-tooltip>
                                            </el-table-column>
                                            <el-table-column
                                                prop="creator_name"
                                                label="发起人"
                                                width="80">
                                            </el-table-column>
                                              <el-table-column
                                                prop="assignees"
                                                label="负责人"
                                                width="100"
                                               show-overflow-tooltip>
                                            </el-table-column>
                                            <el-table-column
                                                prop="created_at"
                                                label="创建时间"
                                                width="170">
                                            </el-table-column>
                                            <el-table-column
                                                prop="deadline"
                                                label="截止时间"
                                                width="110">
                                            </el-table-column>
                                            <el-table-column
                                                prop="completed_date"
                                                label="完成时间"
                                                width="110">
                                            </el-table-column>
                                           
                                            <el-table-column
                                            prop="urgency"
                                            label="紧急程度"
                                            width="70">
                                            <template #default="{row}">
                                                <el-tag :type="row.urgency === '紧急' ? 'danger' : 'success'" effect="plain">
                                                    {{ row.urgency }}
                                                </el-tag>
                                            </template>
                                            </el-table-column> 
                                            <el-table-column 
                                            label="任务状态" 
                                            width="125">
                                            <template #default="{row}">
                                                <el-tag :type="getTaskStatusLabel(row).type" effect="light">
                                                    {{ getTaskStatusLabel(row).label }}
                                                </el-tag>
                                            </template>
                                        </el-table-column>
                                        </el-table>
                                        
                                        <!-- 分页 -->
                                        <div class="pagination-container">
                                            <el-pagination
                                                v-model:current-page="statsTaskDialog.currentPage"
                                                v-model:page-size="statsTaskDialog.pageSize"
                                                :total="statsTaskDialog.total"
                                                layout="total,  prev, pager, next"
                                                @size-change="handleStatsTaskPageSizeChange"
                                                @current-change="handleStatsTaskPageChange"
                                            />
                                        </div>
                                    </div>
                                    <template #footer>
                                        <div style="text-align: right;">
                                            <el-button @click="statsTaskDialog.visible = false">关闭</el-button>
                                        </div>
                                    </template>
                                </el-dialog>

                                <!-- 任务区域 -->
                                <div class="home-tasks-container">
                                    <!-- 要我办 -->
                                    <div class="home-task-section">
                                        <div class="task-section-header">
                                            <h3 class="task-section-title">
                                                <div class="task-section-title-icon">
                                                    我的任务
                                                </div>
                                                <el-tooltip content="显示任务帮助信息" placement="top">
                                                    <el-icon class="task-help-icon"><InfoFilled /></el-icon>
                                                </el-tooltip>
                                            </h3>
                                        </div>
                                        <div class="task-section-tabs">
                                            <div class="task-tab" 
                                                :class="{ active: activeTodoTab === '待完成' }"
                                                @click="handleTabChange('todo', '待完成')">待完成</div>
                                            <div class="task-tab" 
                                                :class="{ active: activeTodoTab === '已逾期' }"
                                                @click="handleTabChange('todo', '已逾期')">我逾期未完成</div>
                                        </div>
                                        
                                        <!-- 添加快速创建任务输入框 -->
                                       <!--  <div class="quick-task-input">
                                            <div class="quick-task-plus">
                                                <el-icon><Plus /></el-icon>
                                            </div>
                                            <input 
                                                type="text" 
                                                class="quick-task-text" 
                                                placeholder="输入任务名，回车快速创建" 
                                                v-model="quickTaskName"
                                                @keyup.enter="createQuickTask" />
                                            <div class="quick-task-date">
                                                <el-date-picker
                                                    v-model="quickTaskDeadline"
                                                    type="date"
                                                    format="YYYY-MM-DD"
                                                    value-format="YYYY-MM-DD"
                                                    :size="'small'"
                                                    style="width: 0; padding: 0; border: none; position: absolute; opacity: 0;"
                                                    @change="handleQuickTaskDateChange">
                                                </el-date-picker>
                                                <el-button 
                                                    type="text" 
                                                    @click="focusQuickTaskDate"
                                                    class="date-button">
                                                    <el-icon class="quick-task-date-icon"><Calendar /></el-icon>
                                                    <span class="quick-task-date-text">{{ quickTaskDeadline ? quickTaskDeadline.substring(5) : '' }}</span>
                                                </el-button>
                                            </div>
                                            <div class="quick-task-project">
                                                <el-popover
                                                    placement="bottom"
                                                    :width="300"
                                                    trigger="click"
                                                    popper-class="assignee-popover">
                                                    <template #reference>
                                                        <el-button 
                                                            type="text" 
                                                            class="assignee-button">
                                                            <el-icon class="quick-task-project-icon"><Folder /></el-icon>
                                                            <span class="quick-task-assignee-text">
                                                                {{ projects.find(p => p.id === quickTaskProjectId)?.name || '选择项目' }}
                                                            </span>
                                                        </el-button>
                                                    </template>
                                                    <template #title>选择项目</template>

                                                    <div>
                                                        <el-input
                                                            v-model="projectSearchQuery"
                                                            placeholder="搜索项目..."
                                                            prefix-icon="Search"
                                                            clearable
                                                            size="small"
                                                            style="margin-bottom: 10px;">
                                                        </el-input>
                                                    </div>

                                                    <div class="assignee-select-list" style="max-height: 300px; overflow-y: auto;">
                                                        <div 
                                                            v-for="project in filteredTaskProjects" 
                                                            :key="project.id" 
                                                            class="assignee-item"
                                                            :class="{ 'active': project.id === quickTaskProjectId }"
                                                            @click="handleQuickTaskProjectChange(project.id)">
                                                            <span class="assignee-item-name">{{ project.name }}</span>
                                                        </div>
                                                        <div 
                                                            v-if="filteredTaskProjects.length === 0" 
                                                            style="padding: 8px 12px; color: #909399; font-size: 13px;">
                                                            暂无匹配项目
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                            <div class="quick-task-urgency">
                                                <el-popover
                                                    placement="bottom"
                                                    :width="200"
                                                    trigger="click"
                                                    popper-class="assignee-popover">
                                                    <template #reference>
                                                        <el-button 
                                                            type="text" 
                                                            class="assignee-button">
                                                            <el-icon class="quick-task-urgency-icon"><Warning /></el-icon>
                                                            <span class="quick-task-assignee-text">
                                                                {{ quickTaskUrgency || '一般' }}
                                                            </span>
                                                        </el-button>
                                                    </template>
                                                    <template #title>选择紧急程度</template>
                                                    <div class="assignee-select-list">
                                                        <div 
                                                            v-for="urgency in ['紧急', '一般']" 
                                                            :key="urgency" 
                                                            class="assignee-item"
                                                            :class="{ 'active': urgency === quickTaskUrgency || (urgency === '一般' && !quickTaskUrgency) }"
                                                            @click="handleQuickTaskUrgencyChange(urgency)">
                                                            <span class="assignee-item-name">{{ urgency }}</span>
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div> -->
                                        
                                        <div class="task-list">
                                            <template v-if="filteredTodoTasks.length > 0">
                                                <div class="task-item" v-for="task in filteredTodoTasks" :key="task.id" @click="handleRowClick(task)">
                                                    <div class="task-item-checkbox">
                                                        <el-checkbox
                                                            :model-value="!!task.completed_date"
                                                            :disabled="!task.can_complete"
                                                            @click.stop
                                                            @change="completeTask(task)"
                                                            :title="getCompleteTitle(task)">
                                                        </el-checkbox>
                                                    </div>
                                                    <div class="task-item-content">
                                                        <div class="task-item-name" :class="{'completed-name': !!task.completed_date}">
                                                            {{ task.name }}
                                                        </div>
                                                        <div class="task-item-project">
                                                            <el-link type="primary" size="small" @click.stop="goToProject(task.project_id)">
                                                                {{ task.project_name }}
                                                            </el-link>
                                                            <span class="task-item-meta-info">
                                                                <span class="task-creator"><el-icon><User /></el-icon> {{ task.creator_name || '未知' }}</span>
                                                                <span class="task-deadline"><el-icon><Calendar /></el-icon> {{ task.deadline || '无截止日期' }}</span>
                                                                <el-tag :type="getTaskStatusLabel(task).type" effect="light" size="small" style="margin-left: 5px;">
                                                                    {{ getTaskStatusLabel(task).label }}
                                                                </el-tag>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="task-item-meta">
                                                        <div class="task-item-badge">
                                                            <el-tag :type="task.urgency === '紧急' ? 'danger' : 'success'" effect="plain" size="small">
                                                                {{ task.urgency }}
                                                            </el-tag>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                            <div v-else class="task-empty">
                                                <el-empty description="暂无待办任务" :image-size="60"></el-empty>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 我发起 -->
                                    <div class="home-task-section">
                                        <div class="task-section-header">
                                            <h3 class="task-section-title">
                                                <div class="task-section-title-icon">
                                                    我的要求
                                                </div>
                                                <el-tooltip content="显示任务帮助信息" placement="top">
                                                    <el-icon class="task-help-icon"><InfoFilled /></el-icon>
                                                </el-tooltip>
                                            </h3>
                                        </div>
                                        <div class="task-section-tabs">
                                            <div class="task-tab" 
                                                :class="{ active: activeCreatedTab === '待完成' }"
                                                @click="handleTabChange('created', '待完成')">待完成</div>
                                            <div class="task-tab" 
                                                :class="{ active: activeCreatedTab === '已逾期' }"
                                                @click="handleTabChange('created', '已逾期')">对方逾期未完成</div>
                                        </div>
                                        
                                        <!-- 添加快速创建任务输入框 -->
                                        <div class="quick-task-input">
                                            <div class="quick-task-plus">
                                                <el-icon><Plus /></el-icon>
                                            </div>
                                            <input style="    min-width: 50px;"
                                                type="text" 
                                                class="quick-task-text" 
                                                placeholder="输入任务名，回车快速创建" 
                                                v-model="quickCreatedTaskName"
                                                @keyup.enter="createQuickCreatedTask" />
                                            <div class="quick-task-assignee">
                                                <el-popover
                                                    ref="quickCreatedTaskAssigneePopover"
                                                    placement="bottom"
                                                    :width="300"
                                                    trigger="click"
                                                    popper-class="assignee-popover">
                                                    <template #reference>
                                                        <el-button 
                                                            type="text" 
                                                            class="assignee-button">
                                                            <el-icon class="quick-task-assignee-icon"><User /></el-icon>
                                                            <span class="quick-task-assignee-text">
                                                                <!-- 显示已选择的负责人数量 -->
                                                                {{ quickCreatedTaskAssignees.length > 0 ? 
                                                                    `已选 ${quickCreatedTaskAssignees.length} 人` : 
                                                                    '选择负责人' }}
                                                            </span>
                                                        </el-button>
                                                    </template>
                                                    <template #title>选择负责人</template>
                                                    <div class="person-charge">
                                                        <!-- 添加搜索框 -->
                                                        <el-input
                                                            v-model="assigneeSearchQuery"
                                                            placeholder="搜索负责人..."
                                                            prefix-icon="Search"
                                                            clearable
                                                            size="small"
                                                            style="margin-bottom: 10px;">
                                                        </el-input>
                                                        <!-- 全选按钮放在这里 -->
                                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                                            <span style="font-size: 13px; color: #606266;">共 {{ availableAssignees.length }} 人</span>
                                                            <div>
                                                                <el-button 
                                                                    type="primary" 
                                                                    size="small" 
                                                                    @click="$refs.quickCreatedTaskAssigneePopover.hide()">
                                                                    确定
                                                                </el-button>
                                                                <el-button 
                                                                    type="primary" 
                                                                    size="small" 
                                                                    @click="handleSelectAllQuickTaskAssignees"
                                                                    style="margin-left: 10px;">
                                                                    {{ quickCreatedTaskAssignees.length === availableAssignees.length ? '取消全选' : '全选' }}
                                                                </el-button>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- 已选负责人显示区域 -->
                                                        <div v-if="quickCreatedTaskAssignees.length > 0" class="selected-assignees-container">
                                                            <div class="selected-assignees-title">已选负责人:</div>
                                                            <div class="selected-assignees-list">
                                                                <el-tag
                                                                    v-for="assigneeId in quickCreatedTaskAssignees"
                                                                    :key="assigneeId"
                                                                    size="small"
                                                                    closable
                                                                    @close="handleQuickCreatedTaskAssigneeChange(assigneeId)"
                                                                    style="margin-right: 4px; margin-bottom: 4px;">
                                                                    {{ availableAssignees.find(a => a.value === assigneeId)?.label || '未知用户' }}
                                                                </el-tag>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="assignee-select-list">
                                                        <div 
                                                            v-for="assignee in filteredAssignees" 
                                                            :key="assignee.value" 
                                                            class="assignee-item"
                                                            :class="{ 'active': quickCreatedTaskAssignees.includes(assignee.value) }"
                                                            @click="handleQuickCreatedTaskAssigneeChange(assignee.value)">
                                                            <el-checkbox 
                                                                :model-value="quickCreatedTaskAssignees.includes(assignee.value)" 
                                                                @click.stop
                                                                style="pointer-events: none; margin-right: 8px;"
                                                            ></el-checkbox>
                                                            <span class="assignee-item-name">{{ assignee.label }}</span>
                                                            <span class="assignee-item-dept">{{ assignee.department }}</span>
                                                        </div>
                                                        <div 
                                                            v-if="filteredAssignees.length === 0" 
                                                            style="padding: 8px 12px; color: #909399; font-size: 13px;">
                                                            暂无匹配负责人
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                            <div class="quick-task-date">
                                                <el-date-picker
                                                    v-model="quickCreatedTaskDeadline"
                                                    type="date"
                                                    format="YYYY-MM-DD"
                                                    value-format="YYYY-MM-DD"
                                                    :size="'small'"
                                                    style="width: 0; padding: 0; border: none; position: absolute; opacity: 0;"
                                                    @change="handleQuickCreatedTaskDateChange">
                                                </el-date-picker>
                                                <el-button 
                                                    type="text" 
                                                    @click="focusQuickCreatedTaskDate"
                                                    class="date-button">
                                                    <el-icon class="quick-task-date-icon"><Calendar /></el-icon>
                                                    <span class="quick-task-date-text">{{ quickCreatedTaskDeadline ? quickCreatedTaskDeadline.substring(5) : '' }}</span>
                                                </el-button>
                                            </div>
                                            <div class="quick-task-project">
                                                <el-popover
                                                    ref="quickCreatedTaskProjectPopover"
                                                    placement="bottom"
                                                    :width="300"
                                                    trigger="click"
                                                    popper-class="assignee-popover">
                                                    <template #reference>
                                                        <el-button 
                                                            type="text" 
                                                            class="assignee-button">
                                                            <el-icon class="quick-task-project-icon"><Folder /></el-icon>
                                                            <span class="quick-task-assignee-text">
                                                                {{ projects.find(p => p.id === quickCreatedTaskProjectId)?.name || '选择项目' }}
                                                            </span>
                                                        </el-button>
                                                    </template>
                                                    <template #title>选择项目</template>
                                                    <!-- 添加搜索框 -->
                                                    <div>
                                                        <el-input
                                                            v-model="projectSearchQuery"
                                                            placeholder="搜索项目..."
                                                            prefix-icon="Search"
                                                            clearable
                                                            size="small"
                                                            style="margin-bottom: 10px;">
                                                        </el-input>
                                                    </div>
                                                    <!-- 添加最大高度限制和滚动效果 -->
                                                    <div class="assignee-select-list" style="max-height: 300px; overflow-y: auto;">
                                                        <div 
                                                            v-for="project in filteredTaskProjects" 
                                                            :key="project.id" 
                                                            class="assignee-item"
                                                            :class="{ 'active': project.id === quickCreatedTaskProjectId }"
                                                            @click="handleQuickCreatedTaskProjectChange(project.id); $refs.quickCreatedTaskProjectPopover.hide()">
                                                            <span class="assignee-item-name">{{ project.name }}</span>
                                                        </div>
                                                        <div 
                                                            v-if="filteredTaskProjects.length === 0" 
                                                            style="padding: 8px 12px; color: #909399; font-size: 13px;">
                                                            暂无匹配项目
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                            <div class="quick-task-urgency">
                                                <el-popover
                                                    ref="quickCreatedTaskUrgencyPopover"
                                                    placement="bottom"
                                                    :width="200"
                                                    trigger="click"
                                                    popper-class="assignee-popover">
                                                    <template #reference>
                                                        <el-button 
                                                            type="text" 
                                                            class="assignee-button">
                                                            <el-icon class="quick-task-urgency-icon"><Warning /></el-icon>
                                                            <span class="quick-task-assignee-text">
                                                                {{ quickCreatedTaskUrgency || '一般' }}
                                                            </span>
                                                        </el-button>
                                                    </template>
                                                    <template #title>选择紧急程度</template>
                                                    <div class="assignee-select-list">
                                                        <div 
                                                            v-for="urgency in ['紧急', '一般']" 
                                                            :key="urgency" 
                                                            class="assignee-item"
                                                            :class="{ 'active': urgency === quickCreatedTaskUrgency || (urgency === '一般' && !quickCreatedTaskUrgency) }"
                                                            @click="handleQuickCreatedTaskUrgencyChange(urgency); $refs.quickCreatedTaskUrgencyPopover.hide()">
                                                            <span class="assignee-item-name">{{ urgency }}</span>
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                            <el-button 
                                            type="primary" 
                                            size="small" 
                                            :width="50"
                                            @click="createQuickCreatedTask"
                                            :disabled="!quickCreatedTaskName"
                                            style="margin-left: 5px; height: 28px; padding: 0 10px;">
                                            创建
                                        </el-button>
                                        </div>
                                        
                                        <div class="task-list">
                                            <template v-if="filteredCreatedTasks.length > 0">
                                                <div class="task-item" v-for="task in filteredCreatedTasks" :key="task.id" @click="handleRowClick(task)">
                                                    <div class="task-item-checkbox">
                                                        <el-checkbox
                                                            :model-value="!!task.completed_date"
                                                            :disabled="!task.can_complete"
                                                            @click.stop
                                                            @change="completeTask(task)"
                                                            :title="getCompleteTitle(task)">
                                                        </el-checkbox>
                                                    </div>
                                                    <div class="task-item-content">
                                                        <div class="task-item-name" :class="{'completed-name': !!task.completed_date}">
                                                            {{ task.name }}
                                                        </div>
                                                        <div class="task-item-project">
                                                            <el-link type="primary" size="small" @click.stop="goToProject(task.project_id)">
                                                                {{ task.project_name }}
                                                            </el-link>
                                                            <span class="task-item-meta-info">
                                                                <span class="task-assignees"><el-icon><User /></el-icon> {{ task.assignees || '无负责人' }}</span>
                                                                <span class="task-deadline"><el-icon><Calendar /></el-icon> {{ task.deadline || '无截止日期' }}</span>
                                                                <el-tag :type="getTaskStatusLabel(task).type" effect="light" size="small" style="margin-left: 5px;">
                                                                    {{ getTaskStatusLabel(task).label }}
                                                                </el-tag>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="task-item-meta">
                                                        <div class="task-item-badge">
                                                            <el-tag :type="task.urgency === '紧急' ? 'danger' : 'success'" effect="plain" size="small">
                                                                {{ task.urgency }}
                                                            </el-tag>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                            <div v-else class="task-empty">
                                                <el-empty description="暂无发起任务" :image-size="60"></el-empty>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 信息概览区域 -->
                                <div class="home-overview-container">
                                    <!-- 重点关注 -->
                                    <div class="home-task-overview">
                                        <div class="task-overview-header">
                                            <h3 class="task-overview-title">
                                                概览
                                                <el-tooltip content="显示任务概览帮助信息" placement="top">
                                                    <el-icon class="task-overview-help-icon"><InfoFilled /></el-icon>
                                                </el-tooltip>
                                            </h3>
                                        </div>
                                        
                                        <div class="task-overview-content">
                                            <!-- 顶部统计 -->
                                            <div class="task-stats-row">
                                                <div class="task-stat-item">
                                                    <div class="stat-label">总数</div>
                                                    <div class="stat-value">{{ filteredTodoStats.total }}</div>
                                                    <div class="stat-progress blue-bg"></div>
                                                </div>
                                                <div class="task-stat-item">
                                                    <div class="stat-label">已完成</div>
                                                    <div class="stat-value">{{ filteredTodoStats.completed }}</div>
                                                    <div class="stat-progress green-bg"></div>
                                                </div>
                                                <div class="task-stat-item">
                                                    <div class="stat-label">未完成</div>
                                                    <div class="stat-value">{{ filteredTodoStats.unfinished }}</div>
                                                    <div class="stat-progress orange-bg"></div>
                                                </div>
                                            </div>
                                            
                                            <!-- 底部统计 -->
                                            <div class="task-stats-row">
                                                <div class="task-stat-item">
                                                    <div class="stat-label">已逾期</div>
                                                    <div class="stat-value">{{ filteredTodoStats.overdue }}</div>
                                                    <div class="stat-progress red-bg"></div>
                                                </div>
                                                <div class="task-stat-item">
                                                    <div class="stat-label">时间未定</div>
                                                    <div class="stat-value">{{ filteredTodoStats.noDeadline }}</div>
                                                    <div class="stat-progress lime-bg"></div>
                                                </div>
                                                <div class="task-stat-item">
                                                    <div class="stat-label">今天到期</div>
                                                    <div class="stat-value">{{ filteredTodoStats.dueToday }}</div>
                                                    <div class="stat-progress beige-bg"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 我要办的-任务概览 -->
                                    <div class="home-focus-section">
                                        <div class="focus-header">
                                            <h3 class="focus-title">
                                                <span>重点关注</span>
                                                <el-tooltip content="显示重点关注帮助信息" placement="top">
                                                    <el-icon class="focus-help-icon"><InfoFilled /></el-icon>
                                                </el-tooltip>
                                            </h3>
                                        </div>
                                        
                                        <div class="focus-content">
                                            <!-- 进度更新 -->
                                            <div class="focus-item progress-update">
                                                <div class="focus-item-icon">
                                                    <el-icon><Star /></el-icon>
                                                </div>
                                                <div class="focus-item-content">
                                                    <div class="focus-item-title">进度更新</div>
                                                    <div class="focus-item-detail">
                                                        {{ focusStats.timeRangeDesc }}完成任务 <span class="highlight">{{ focusStats.completedTasks }}</span> 个，同比{{ focusStats.prevTimeRangeDesc }}{{ focusStats.completedIncrease > 0 ? '增加' : '减少' }} <span class="highlight">{{ Math.abs(focusStats.completedIncrease) }}%</span>。<br>
                                                        {{ focusStats.timeRangeDesc }}新建任务 <span class="highlight">{{ focusStats.createdTasks }}</span> 个，同比{{ focusStats.prevTimeRangeDesc }}{{ focusStats.createdIncrease > 0 ? '增加' : '减少' }} <span class="highlight">{{ Math.abs(focusStats.createdIncrease) }}%</span>。
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 指派分析 -->
                                            <div class="focus-item assignment-analysis">
                                                <div class="focus-item-icon">
                                                    <el-icon><Document /></el-icon>
                                                </div>
                                                <div class="focus-item-content">
                                                    <div class="focus-item-title">指派分析</div>
                                                    <div class="focus-item-detail">
                                                        {{ focusStats.timeRangeDesc }}指派给我的任务在明确截止时间的数量为 <span class="highlight">{{ focusStats.tasksWithDeadline }}</span>，占比 <span class="highlight">{{ focusStats.percentWithDeadline }}%</span>。请及时跟踪任务的完成时间设置。
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 图表分析区域 -->
                                <div class="home-chart-container">
                                    <!-- 我的任务-时间分布与每日追踪 -->
                                    <div class="home-chart-row">
                                        <!-- 时间分布 -->
                                        <div class="home-chart-box">
                                            <div class="chart-header">
                                                <h3 class="chart-title">
                                                    我的任务-时间分布
                                                    <el-tooltip content="显示时间分布帮助信息" placement="top">
                                                        <el-icon class="chart-help-icon"><InfoFilled /></el-icon>
                                                    </el-tooltip>
                                                </h3>
                                            </div>
                                            
                                            <div class="chart-content">
                                                <!-- 图表容器 -->
                                                <div id="todoTimeDistribution" style="width: 100%; height: 300px;"></div>
                                            </div>
                                        </div>
                                        
                                        <!-- 每日追踪 -->
                                        <div class="home-chart-box">
                                            <div class="chart-header">
                                                <h3 class="chart-title">
                                                    我的任务-每日追踪
                                                    <el-tooltip content="显示每日追踪帮助信息" placement="top">
                                                        <el-icon class="chart-help-icon"><InfoFilled /></el-icon>
                                                    </el-tooltip>
                                                </h3>
                                            </div>
                                            
                                            <div class="chart-content">
                                                <!-- 图表容器 -->
                                                <div id="todoDailyTracking" style="width: 100%; height: 300px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        
                        <!-- 项目统计视图 -->
                        <template v-else-if="currentView === 'stats'">
                            <div class="project-stats-dashboard" v-loading="tableLoading">
                                <!-- 项目选择区域 -->
                                <div class="stats-header">
                                    <div class="stats-project-selector">
                                        <span style="margin-right: 10px; font-weight: 500;">选择项目:</span>
                                        <el-select 
                                            :model-value="currentProject.id" 
                                            placeholder="选择要查看的项目" 
                                            @change="handleProjectChange"
                                            class="project-select"
                                            filterable>
                                            <!-- 全部项目选项 -->
                                            <el-option
                                                key="all"
                                                label="全部项目"
                                                value="all">
                                            </el-option>
                                            <el-option
                                                v-for="project in projects"
                                                :key="project.id"
                                                :label="project.name"
                                                :value="project.id">
                                            </el-option>
                                        </el-select>
                                        
                                        <!-- 添加时间范围筛选器 -->
                                        <div class="stats-date-filter">
                                            <span style="margin-right: 10px; font-weight: 500;">时间筛选：</span>
                                            <el-select 
                                                v-model="statsDateRange" 
                                                placeholder="选择时间范围"
                                                class="date-range-select"
                                                @change="handleStatsDateRangeChange">
                                                <el-option label="本月" value="current"></el-option>
                                                <el-option label="上月" value="last"></el-option>
                                                <el-option label="自定义" value="custom"></el-option>
                                            </el-select>
                                            <el-date-picker
                                                v-if="statsDateRange === 'custom'"
                                                v-model="statsCustomDateRange"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期"
                                                format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD"
                                                :disabled-date="disableFutureDates"
                                                style="margin-left: 10px; width: 260px;"
                                                @change="handleStatsCustomDateRangeChange">
                                            </el-date-picker>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 统计图表区域 -->
                                <div class="stats-charts-container" v-if="currentProject.id && (currentProject.id === 'all' || typeof currentProject.id === 'number')">
                                    <!-- 上方图表行 -->
                                    <div class="stats-chart-row">
                                        <!-- 任务状态分布饼图 -->
                                        <div class="stats-chart-box">
                                            <div id="taskStatusPieChart" class="chart-container"></div>
                                        </div>
                                        
                                        <!-- 本周任务完成趋势折线图 -->
                                        <div class="stats-chart-box">
                                            <div id="weeklyTaskCompletionChart" class="chart-container"></div>
                                        </div>
                                    </div>
                                    
                                    <!-- 下方图表行 -->
                                    <div class="stats-chart-row">
                                        <!-- 团队成员任务分配柱状图 -->
                                        <div class="stats-chart-box">
                                            <div id="teamMemberTaskChart" class="chart-container"></div>
                                        </div>
                                        
                                        <!-- 任务优先级分布条形图 -->
                                        <div class="stats-chart-box">
                                            <div id="taskPriorityChart" class="chart-container"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 无项目选择时的提示 -->
                                <div v-else class="stats-empty-container">
                                    <el-empty 
                                        description="请选择一个项目查看统计数据" 
                                        :image-size="200">
                                        <template #default>
                                            <p style="margin-top: 20px; color: #999;">
                                                您需要从上方下拉菜单中选择一个可见项目
                                            </p>
                                        </template>
                                    </el-empty>
                                </div>
                            </div>
                        </template>

                        <!-- 任务审批视图 -->
                        <template v-else-if="currentView === 'approval'">
                            <div class="task-approval-container" v-loading="approvalLoading">
                                <!-- 审批列表 -->
                                <div class="approval-section">
                                    <el-table
                                        :data="approvalRequests"
                                        style="width: 100%"
                                        empty-text="暂无待审批任务"
                                        @row-click="showApprovalDetails">
                                        <el-table-column
                                            prop="task_name"
                                            label="任务名称"
                                            min-width="20%">
                                            <template #default="{row}">
                                                <el-tooltip :content="row.task_name" placement="top" :show-after="500">
                                                    <span class="ellipsis-text">{{ row.task_name }}</span>
                                                </el-tooltip>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            prop="project_name"
                                            label="所属项目"
                                            width="120">
                                        </el-table-column>
                                        <el-table-column
                                            prop="requester_name"
                                            label="删除申请人"
                                            width="100">
                                        </el-table-column>
                                        <el-table-column
                                            prop="user_role"
                                            label="你的角色"
                                            width="120">
                                            <template #default="{row}">
                                                <el-tag size="small" effect="plain">{{ row.user_role }}</el-tag>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            label="审批状态"
                                            width="100">
                                            <template #default="{row}">
                                                <el-tag :type="row.approved ? 'success' : 'info'" effect="plain">
                                                    {{ row.approved ? '已审批' : '待审批' }}
                                                </el-tag>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            label="操作"
                                            width="200"
                                            fixed="right">
                                            <template #default="{row}">
                                                <el-button 
                                                    type="success" 
                                                    size="small"
                                                    :disabled="row.approved"
                                                    @click.stop="approveTaskDelete(row.id)">
                                                    审批通过
                                                </el-button>
                                                <el-button 
                                                    type="danger" 
                                                    size="small"
                                                    :disabled="row.approved"
                                                    @click.stop="rejectTaskDelete(row.id)">
                                                    拒绝
                                                </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>

                                <!-- 审批说明 -->
                                <div class="approval-info" style="margin-top: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
                                    <h4 style="margin: 0 0 10px 0; color: #303133;">审批说明：</h4>
                                    <ul style="margin: 0; padding-left: 20px; color: #606266; font-size: 14px;">
                                        <li>任务删除需要相关人员审批通过后才能执行</li>
                                        <li>审批人包括：删除请求人、任务创建人及其直属主管</li>
                                        <li>如果直属主管不存在，则自动跳过该审批环节</li>
                                        <li>所有必要的审批通过后，任务将被自动删除</li>
                                    </ul>
                                </div>
                                
                                <!-- 审批详情对话框 -->
                                <el-dialog
                                    v-model="approvalDetailDialog.visible"
                                    :title="'任务删除审批详情 - ' + (approvalDetailDialog.approval?.task_name || '')"
                                    width="600px">
                                    <div v-loading="approvalDetailDialog.loading">
                                        <div v-if="approvalDetailDialog.approval" class="approval-details">
                                            <!-- 任务基本信息 -->
                                            <div class="info-section">
                                                <h3>任务信息</h3>
                                                <el-descriptions :column="1" border>
                                                    <el-descriptions-item label="任务名称">
                                                        {{ approvalDetailDialog.approval.task_name }}
                                                    </el-descriptions-item>
                                                    <el-descriptions-item label="任务描述">
                                                        <div style="white-space: pre-wrap; word-break: break-word;">{{ approvalDetailDialog.approval.task_description || '无' }}</div>
                                                    </el-descriptions-item>
                                                    <el-descriptions-item label="所属项目">
                                                        {{ approvalDetailDialog.approval.project_name }}
                                                    </el-descriptions-item>
                                                    <el-descriptions-item label="删除申请时间">
                                                        {{ approvalDetailDialog.approval.created_at }}
                                                    </el-descriptions-item>
                                                </el-descriptions>
                                            </div>

                                            <!-- 审批人状态 -->
                                            <div class="approval-status-section">
                                                <h3>审批人状态</h3>
                                                <el-descriptions :column="2" border>
                                                    <el-descriptions-item label="删除申请人">
                                                        {{ approvalDetailDialog.approval.requester_name }}
                                                        <el-tag 
                                                            v-if="approvalDetailDialog.approval.requester_approved" 
                                                            type="success" 
                                                            size="small"
                                                            style="margin-left: 8px">
                                                            已审批
                                                        </el-tag>
                                                    </el-descriptions-item>
                                                    <el-descriptions-item label="任务创建人">
                                                        {{ approvalDetailDialog.approval.creator_name }}
                                                        <el-tag 
                                                            v-if="approvalDetailDialog.approval.creator_approved" 
                                                            type="success" 
                                                            size="small"
                                                            style="margin-left: 8px">
                                                            已审批
                                                        </el-tag>
                                                        <el-tag 
                                                            v-else
                                                            type="warning" 
                                                            size="small"
                                                            style="margin-left: 8px">
                                                            待审批
                                                        </el-tag>
                                                    </el-descriptions-item>
                                                    <!-- 申请人的直属主管 -->
                                                    <el-descriptions-item v-if="approvalDetailDialog.approval.requester_manager_name" label="申请人直属主管">
                                                        {{ approvalDetailDialog.approval.requester_manager_name }}
                                                        <el-tag 
                                                            v-if="approvalDetailDialog.approval.requester_manager_approved" 
                                                            type="success" 
                                                            size="small"
                                                            style="margin-left: 8px">
                                                            已审批
                                                        </el-tag>
                                                        <el-tag 
                                                            v-else
                                                            type="warning" 
                                                            size="small"
                                                            style="margin-left: 8px">
                                                            待审批
                                                        </el-tag>
                                                    </el-descriptions-item>
                                                    <!-- 创建人的直属主管 -->
                                                    <el-descriptions-item v-if="approvalDetailDialog.approval.creator_manager_name" label="创建人直属主管">
                                                        {{ approvalDetailDialog.approval.creator_manager_name }}
                                                        <el-tag 
                                                            v-if="approvalDetailDialog.approval.creator_manager_approved" 
                                                            type="success" 
                                                            size="small"
                                                            style="margin-left: 8px">
                                                            已审批
                                                        </el-tag>
                                                        <el-tag 
                                                            v-else
                                                            type="warning" 
                                                            size="small"
                                                            style="margin-left: 8px">
                                                            待审批
                                                        </el-tag>
                                                    </el-descriptions-item>
                                                </el-descriptions>
                                            </div>
                                            
                                            <!-- 当前用户操作 -->
                                            <div class="user-action-section" v-if="!approvalDetailDialog.approval.approved">
                                                <h3>我的操作</h3>
                                                <div class="action-buttons" style="margin-top: 15px; text-align: center;">
                                                    <el-button 
                                                        type="success" 
                                                        :disabled="approvalDetailDialog.approval.approved"
                                                        @click="approveFromDetail(approvalDetailDialog.approval.id)">
                                                        审批通过
                                                    </el-button>
                                                    <el-button 
                                                        type="danger" 
                                                        :disabled="approvalDetailDialog.approval.approved"
                                                        @click="rejectFromDetail(approvalDetailDialog.approval.id)">
                                                        拒绝删除
                                                    </el-button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-dialog>
                            </div>
                        </template>

                        <!-- 任务统计视图 -->
                        <template v-else-if="currentView === 'task_stats'">
                            <div class="task-stats-container">
                                <!-- 筛选条件 -->
                                <div class="stats-filter-container">
                                    <el-row :gutter="20" class="stats-filter-row">
                                        <el-col :xs="12" :sm="6" :md="4" :lg="3" style=" padding-right: 0px;    padding-left: 0px;">
                                            <el-input style="padding-left: 1px !important"
                                                v-model="assigneeNameFilter"
                                                placeholder="输入姓名"
                                                clearable
                                                @keyup.enter="handleAssigneeNameFilterChange">
                                                <template #prefix>
                                                    <el-icon><Search /></el-icon>
                                                </template>
                                            </el-input>
                                        </el-col>
                                        <el-col :xs="6" :sm="3" :md="2" :lg="2"   style=" padding-right: 0px;    padding-left: 0px;" >
                                            <el-select
                                                v-model="assigneeProjectFilter"
                                                placeholder="选择项目"
                                                filterable
                                                clearable
                                                style="width: 100%">
                                                <el-option
                                                    v-for="project in projectOptions"
                                                    :key="project.id"
                                                    :label="project.name"
                                                    :value="project.id">
                                                </el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :xs="6" :sm="3" :md="2" :lg="2" style=" padding-right: 0px;    padding-left: 0px;">
                                            <el-select
                                                v-model="assigneeDepartmentFilter"
                                                placeholder="选择部门"
                                                filterable
                                                clearable
                                                style="width: 100%">
                                                <el-option
                                                    v-for="dept in departments"
                                                    :key="dept.id"
                                                    :label="dept.name"
                                                    :value="dept.id">
                                                </el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :xs="6" :sm="3" :md="2" :lg="2" style=" padding-right: 0px;    padding-left: 0px;">
                                            <el-select
                                                v-model="assigneeUrgencyFilter"
                                                placeholder="紧急程度"
                                                clearable
                                                style="width: 100%">
                                                <!-- <el-option label="全部" value="all"></el-option> -->
                                                <el-option label="紧急" value="紧急"></el-option>
                                                <el-option label="一般" value="一般"></el-option>
                                            </el-select>
                                        </el-col>
                                        <!-- <el-col :xs="6" :sm="3" :md="2" :lg="2">
                                            <el-select
                                                v-model="assigneeDateFilter"
                                                placeholder="选择日期范围"
                                                style="width: 100%">
                                                <el-option
                                                    v-for="option in dateFilterOptions"
                                                    :key="option.value"
                                                    :label="option.label"
                                                    :value="option.value">
                                                </el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :xs="20" :sm="10" :md="6" :lg="5" v-if="assigneeDateFilter === 'custom'">
                                            <el-date-picker
                                                v-model="assigneeCustomDateRange"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期"
                                                format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD"
                                                style="width: 100%">
                                            </el-date-picker>
                                        </el-col> -->
                                        <el-col :xs="6" :sm="3" :md="2" :lg="2" style=" padding-right: 0px;    padding-left: 0px;">
                                            <el-select
                                                v-model="assigneeDeadlineFilter"
                                                placeholder="截至日期"
                                                clearable
                                                style="width: 100%">
                                                <el-option label="全部" value="all"></el-option>
                                                <el-option label="本月" value="current_month_deadline"></el-option>
                                                <el-option label="上月" value="last_month_deadline"></el-option>
                                                <el-option label="本年" value="current_year_deadline"></el-option>
                                                <el-option label="自定义" value="custom_deadline"></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :xs="20" :sm="10" :md="6" :lg="5" v-if="assigneeDeadlineFilter === 'custom_deadline'">
                                            <el-date-picker
                                                v-model="assigneeCustomDeadlineRange"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期"
                                                format="YYYY-MM-DD"
                                                value-format="YYYY-MM-DD"
                                                style="width: 100%">
                                            </el-date-picker>
                                        </el-col>
                                        <el-col :xs="24" :sm="12" :md="4" :lg="1" style="padding-left: 10px;margin-right: 45px;">
                                            <el-button 
                                                type="primary" 
                                                @click="handleAssigneeStatsSearch" 
                                                :loading="tableLoading"
                                                style="width: 90px; background-color: #F05123; border-color: #F05123">
                                                搜索
                                            </el-button>
                                        </el-col>
                                        <el-col :xs="24" :sm="12" :md="4" :lg="1">
                                            <el-button 
                                                type="success" 
                                                @click="exportAssigneeStats" 
                                                :loading="exportLoading"
                                                style="width: 90px;">
                                                <el-icon><Download /></el-icon> 导出Excel
                                            </el-button>
                                        </el-col>
                                    </el-row>
                                </div>

                                <!-- 统计卡片区域
                                <div class="stats-cards-wrapper">
                                    <div class="stats-card-item blue-card">
                                        <div class="stats-card-value">{{ assigneeStatsTotal }}</div>
                                        <div class="stats-card-label">负责人总数</div>
                                    </div>
                                    <div class="stats-card-item green-card">
                                        <div class="stats-card-value">{{ getTotalActiveTasks() }}</div>
                                        <div class="stats-card-label">进行中任务</div>
                                    </div>
                                    <div class="stats-card-item red-card">
                                        <div class="stats-card-value">{{ getTotalOverdueTasks() }}</div>
                                        <div class="stats-card-label">逾期任务</div>
                                    </div>
                                </div>-->

                                <!-- 主内容区域 -->
                                <div class="stats-main-content">
                                    <!-- 表格容器 -->
                                    <div class="stats-table-container">
                                        <el-table
                                            :data="assigneeStats"
                                            style="width: 100%; height: 100% !important;min-height: 100% !important;"
                                            v-loading="tableLoading"
                                            element-loading-text="加载任务统计数据中..."
                                            
                                            :height="'100%'"
                                            :empty-text="tableLoading ? '' : '暂无数据'"
                                            :header-cell-style="{background:'#f8fafc',color:'#606266',fontWeight:'500'}"
                                            :cell-style="{padding: '8px 0'}">
                                            <el-table-column
                                                type="index"
                                                label="序号"
                                                width="60"
                                                align="center"
                                                fixed="left">
                                            </el-table-column>
                                            <el-table-column
                                                prop="user_name"
                                                label="负责人姓名"
                                                min-width="120"
                                                align="center"
                                                fixed="left">
                                            </el-table-column>
                                            <!-- 添加部门列 -->
                                            <el-table-column
                                                prop="department"
                                                label="部门"
                                                min-width="150"
                                                align="center">
                                            </el-table-column>
                                            <!-- 添加职位列 -->
                                            <el-table-column
                                                prop="position"
                                                label="职位"
                                                min-width="120"
                                                align="center">
                                            </el-table-column>
                                            <el-table-column
                                                prop="total_tasks"
                                                label="总任务数"
                                                min-width="100"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-value"
                                                          @click="showTaskDetailDialog(row, 'total_tasks')"
                                                          style="cursor: pointer;">
                                                        {{ row.total_tasks }}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column
                                                prop="in_progress_tasks"
                                                label="进行中任务"
                                                min-width="110"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-tag tag-primary" 
                                                          @click="showTaskDetailDialog(row, 'in_progress_tasks')"
                                                          style="cursor: pointer;">
                                                        {{ row.in_progress_tasks }}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <!-- <el-table-column
                                                prop="overdue_completed_tasks"
                                                label="逾期完成任务"
                                                min-width="110"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-tag tag-warning"
                                                          @click="showTaskDetailDialog(row, 'overdue_completed_tasks')"
                                                          style="cursor: pointer;">
                                                        {{ row.overdue_completed_tasks }}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column
                                                prop="overdue_incomplete_tasks"
                                                label="逾期未完成任务"
                                                min-width="120"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-tag tag-danger"
                                                          @click="showTaskDetailDialog(row, 'overdue_incomplete_tasks')"
                                                          style="cursor: pointer;">
                                                        {{ row.overdue_incomplete_tasks }}
                                                    </span>
                                                </template>
                                            </el-table-column> -->
                                            <el-table-column
                                                prop="on_time_completed_tasks"
                                                label="按时完成任务"
                                                min-width="110"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-tag tag-success"
                                                          @click="showTaskDetailDialog(row, 'on_time_completed_tasks')"
                                                          style="cursor: pointer;">
                                                        {{ row.on_time_completed_tasks }}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column
                                                prop="overdue_tasks"
                                                label="逾期任务"
                                                min-width="100"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-tag tag-danger"
                                                          @click="showTaskDetailDialog(row, 'overdue_tasks')"
                                                          style="cursor: pointer; font-weight: bold;">
                                                        {{ row.overdue_tasks }}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column
                                                prop="overdue_periods"
                                                label="超期三天次数"
                                                min-width="100"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-tag tag-warning"
                                                          @click="showTaskDetailDialog(row, 'overdue_tasks')"
                                                          style=" cursor: pointer; font-weight: bold;">
                                                        {{ row.overdue_periods }}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column
                                                prop="completion_rate"
                                                label="完成率"
                                                min-width="100"
                                                align="center"
                                                sortable>
                                                <template #default="{row}">
                                                    <span class="stats-num-tag" 
                                                          :class="row.completion_rate >= 90 ? 'tag-success' :  'tag-danger'">
                                                        {{ row.completion_rate }}%
                                                    </span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </div>
                                
                                    <!-- 分页组件 -->
                                    <div class="stats-pagination-container">
                                        <el-pagination
                                            v-model:current-page="assigneeStatsCurrentPage"
                                            v-model:page-size="assigneeStatsPageSize"
                                            :page-sizes="[20, 50, 100]"
                                            :total="assigneeStatsTotal"
                                            layout="total, sizes, prev, pager, next, jumper"
                                            @size-change="handleAssigneeStatsPageSizeChange"
                                            @current-change="handleAssigneeStatsPageChange"
                                            background
                                            style="width: 100%; justify-content: flex-end;"
                                        />
                                    </div>
                                </div>
                            </div>
                        </template>

                        <!-- 其他视图的表格 -->
                        <template v-else>
                            <el-table
                                ref="tableRef"
                                :data="paginatedTasks"
                                :row-class-name="getRowClass"
                                @row-click="handleRowClick"
                                highlight-current-row
                                v-loading="tableLoading"
                                style="width: 100%"
                                :empty-text="getEmptyText()"
                                class="is-desktop">
                                <el-table-column label="任务名称" prop="name" min-width="20%" fixed-width="false">
                                    <template #default="{row}">
                                        <div class="task-name" :style="row.is_child ? 'padding-left: 25px' : ''">
                                            <el-checkbox
                                                :model-value="!!row.completed_date"
                                                :disabled="!row.can_complete"
                                                @click.stop
                                                @change="completeTask(row)"
                                                :title="getCompleteTitle(row)"
                                                class="task-checkbox">
                                            </el-checkbox>
                                            <span :class="{
                                                'task-name-text': true, 
                                                'completed-name': !!row.completed_date,
                                                'overdue-name': !row.completed_date && row.deadline && row.deadline < new Date().toISOString().slice(0, 10)
                                            }">
                                                {{ row.name }}
                                            </span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                 v-if="currentView !== 'received'"
                                    label="紧急程度" 
                                    prop="urgency" 
                                    width="100"
                                    :filters="[{text: '紧急', value: '紧急'}, {text: '一般', value: '一般'}]"
                                    :filter-method="filterUrgency"
                                    filter-placement="bottom-end">
                                    <template #default="{row}">
                                        <el-tag :type="row.urgency === '紧急' ? 'danger' : 'success'" effect="plain">
                                            {{ row.urgency }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                v-if="currentView === 'received'"
                                   prop="task_relation_type"
                                   label="任务类型"
                                   width="100">
                                   <template #default="scope">
                                       <el-tag
                                           :type="scope.row.task_relation_type === '我创建的' ? 'success' : 
                                                 scope.row.task_relation_type === '我负责的' ? 'primary' : 
                                                 'warning'"
                                           size="small"
                                           effect="light">
                                           {{ scope.row.task_relation_type }}
                                       </el-tag>
                                   </template>
                               </el-table-column>
                                <el-table-column 
                                    prop="assignees" 
                                    label="负责人" 
                                    width="100">
                                </el-table-column>
                                <el-table-column
                                 v-if="currentView === 'received'"
                                    prop="cc_users_names"
                                    label="抄送人"
                                    width="100">
                                </el-table-column>
                                <el-table-column 
                                    prop="creator_name" 
                                    label="发起人" 
                                    width="90">
                                </el-table-column>
                                <el-table-column 
                                    prop="created_at" 
                                    label="创建时间" 
                                    width="180"
                                    sortable>
                                </el-table-column>
                                <el-table-column 

                                    prop="completed_date" 
                                    label="完成时间" 
                                    width="120"
                                    sortable>
                                </el-table-column>
                                <el-table-column 
                                    prop="deadline" 
                                    label="截止时间" 
                                    width="120"
                                    sortable>
                                </el-table-column>
                                <el-table-column 
                                    label="任务状态" 
                                    width="135">
                                    <template #default="{row}">
                                        <el-tag :type="getTaskStatusLabel(row).type" effect="light">
                                            {{ getTaskStatusLabel(row).label }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column 
                                    v-if="['search', 'completed', 'created'].includes(currentView)" 
                                    prop="project_name" 
                                    label="所属项目" 
                                    width="140"
                                    column-key="project_name"
                                    :filters="projectFilters"
                                    :filter-method="filterProject">
                                    <template #default="{row}">
                                        <el-link type="primary" @click.stop="goToProject(row.project_id)">
                                            {{ row.project_name }}
                                        </el-link>
                                    </template>
                                </el-table-column>
                            </el-table>
                            
                            <!-- 添加清除筛选按钮 -->
                            <div class="filter-actions" v-if="hasActiveFilters">
                                <el-button size="small" @click="clearAllFilters">清除全部筛选</el-button>
                            </div>
                            
                            <!-- 分页组件 -->
                            <div class="pagination-container">
                                <el-pagination
                                    background
                                    layout="prev, pager, next, jumper, total"
                                    :page-size="pageSize"
                                    :current-page="currentPage"
                                    :total="totalTasks"
                                    @current-change="handlePageChange">
                                </el-pagination>
                            </div>
                        </template>
                    </div>
                </Transition>
            </div>
            
            <!-- 任务详情区（作为主界面同层的一部分，而不是叠层） -->
            <div class="task-drawer" :class="{ open: drawer.visible }">
                <template v-if="drawer.loading">
                    <div class="drawer-loading">
                        <el-skeleton :rows="6" animated />
                    </div>
                </template>
                <template v-else-if="drawer.error">
                    <div class="drawer-error">
                        <el-alert :title="drawer.error" type="error" />
                    </div>
                </template>
                <template v-else-if="drawer.task">
                    <div class="drawer-header">
                        <div class="drawer-title">{{ drawer.title }}</div>
                        <div class="drawer-close" @click="closeDrawer">&times;</div>
                    </div>
                    
                    <!-- 添加可滚动的内容区域 -->
                    <div class="drawer-content">
                        <div class="drawer-info">
                            <el-descriptions :column="1" border>
                                <el-descriptions-item label="项目名称">{{ drawer.task.project_name }}</el-descriptions-item>
                                <el-descriptions-item label="项目描述">
                                    <div v-if="drawer.task.description" class="log-content" v-html="drawer.task.description"></div>
                                    <span v-else>无</span>
                                </el-descriptions-item>
                                <el-descriptions-item label="发起人">{{ drawer.task.creator_name || '未知' }}</el-descriptions-item>
                                <el-descriptions-item label="创建时间">{{ drawer.task.created_at || '未知' }}</el-descriptions-item>
                                <el-descriptions-item label="截止日期">{{ drawer.task.deadline || '无' }}</el-descriptions-item>
                                <el-descriptions-item label="完成日期">{{ drawer.task.completed_date || '---' }}</el-descriptions-item>
                                <el-descriptions-item label="负责人">{{ drawer.task.assignees }}</el-descriptions-item>
                                <el-descriptions-item label="抄送人">{{ drawer.task.cc_users }}</el-descriptions-item>
                                <el-descriptions-item label="紧急程度">
                                    <el-tag :type="drawer.task.urgency === '紧急' ? 'danger' : 'success'">
                                        {{ drawer.task.urgency }}
                                    </el-tag>
                                </el-descriptions-item>
                                <el-descriptions-item label="主任务">{{ drawer.task.parentTaskName || '无' }}</el-descriptions-item>
                            </el-descriptions>
                        </div>
                        
                        <!-- 沟通记录部分 -->
                        <div class="communication-log">
                            <el-divider content-position="left">沟通记录 & 日志</el-divider>
                            
                            <!-- 沟通记录时间线 -->
                            <div v-if="drawer.task.logs && drawer.task.logs.length > 0">
                                <el-timeline>
                                    <el-timeline-item
                                        v-for="log in drawer.task.logs"
                                        :key="log.id"
                                        :timestamp="log.time"
                                        :type="log.type === 'reply' ? 'primary' : 'info'">
                                        <div class="log-item">
                                            <strong>{{ log.sender }}:</strong> 
                                            <el-tag v-if="log.type === 'reply'" size="small" type="primary" effect="plain" class="log-tag">回复</el-tag>
                                            <el-tag v-else size="small" type="info" effect="plain" class="log-tag">日志</el-tag>
                                            <div class="log-content" v-html="log.message"></div>
                                            <!-- 添加删除按钮，仅在日志创建3分钟内显示 -->
                                            <el-button 
                                                v-if="isWithinDeleteTimeLimit(log)"
                                                type="danger" 
                                                size="small" 
                                                circle
                                                class="log-delete-btn"
                                                @click.stop="deleteLog(log.id, log.type)"
                                                title="删除此日志">
                                                <el-icon><Delete /></el-icon>
                                            </el-button>
                                            <!-- <div class="log-content" v-if="log.is_html" v-html="log.message"></div>
                                            <div class="log-content" v-else>{{ log.message }}</div> -->
                                        </div>
                                    </el-timeline-item>
                                </el-timeline>
                            </div>
                            
                            <!-- 无沟通记录时显示 -->
                            <el-empty v-else description="暂无沟通记录" :image-size="100" />
                        </div>
                    </div>
                    
                    <!-- 固定在底部的回复框部分 -->
                    <div class="reply-section">
                        <el-form @submit.native.prevent="sendReply">
                            <el-form-item>
                                <!-- 富文本编辑器容器 -->
                                <div class="rich-editor-container">
                                    <div id="replyEditorToolbar" class="rich-editor-toolbar"></div>
                                    <div id="replyEditorContent" class="rich-editor-content" style="height: 250px;"></div>
                                </div>
                            </el-form-item>
                            <el-form-item>
                                <!-- <el-button type="primary" @click="sendReply" :disabled="!canSendReply">
                                    <el-icon><Message /></el-icon> 发送回复
                                </el-button> -->
                                
                                <!-- 新增钉钉回复按钮 -->
                                <el-button type="success" @click="sendDingReply" :disabled="!canSendReply" style="margin-left: 10px;">
                                    <el-icon><Message /></el-icon> 发送
                                </el-button>
                                 <el-button type="warning" :disabled="!drawer.taskId" @click="showEditTaskDialog" style="margin-left: 10px;">
                                    <el-icon><Message /></el-icon> 编辑
                                </el-button>
                                <!-- 添加删除任务按钮 -->
                                <el-button 
                                    type="danger" 
                                    @click="deleteTask(drawer.task)"
                                    style="margin-left: 10px;">
                                    <el-icon><Delete /></el-icon> 删除
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </template>
            </div>
        </div>
        
        <!-- 项目管理弹窗 -->
        <el-dialog
            v-model="projectDialog.visible"
            :title="projectDialog.title"
            width="1000px"
            :close-on-click-modal="false"
            @closed="handleProjectDialogClosed">
            <template v-if="projectDialog.loading">
                <el-skeleton :rows="10" animated />
            </template>
            <template v-else>
                <div class="project-modal-content">
                    <el-tabs tab-position="left" class="project-management-tabs" style="min-height: 350px;">
                        <el-tab-pane>
                            <template #label>
                                <div class="tab-label">
                                    <el-icon><Setting /></el-icon>
                                    <span>参数管理</span>
                                </div>
                            </template>
                            
                            <div class="project-modal-section">
                                <div class="project-modal-section-title">
                                    <el-icon><Setting /></el-icon> 项目参数配置
                                </div>
                                
                                <el-form label-position="top">
                                    <el-form-item label="项目名称">
                                        <el-input v-model="projectDialog.project.editName" placeholder="请输入项目名称" />
                                    </el-form-item>
                                    
                                    <el-form-item label="项目描述">
                                        <el-input 
                                            v-model="projectDialog.project.editDescription" 
                                            type="textarea" 
                                            :rows="3" 
                                            placeholder="请输入项目描述" />
                                    </el-form-item>

                                    <el-form-item label="任务删除审核">
                                        <el-switch
                                            v-model="projectDeleteSettings.requireApproval"
                                            :loading="projectDeleteSettings.loading"
                                            @change="(val) => updateProjectDeleteSettings(projectDialog.project.id, val)"
                                            active-text="启用"
                                            inactive-text="禁用">
                                        </el-switch>
                                        <div class="el-form-item-tip" style="margin-top: 5px; font-size: 12px; color: #909399;">
                                            启用后，删除任务需要经过审核流程才能执行。审核人包括：删除请求人、任务创建人及其直属主管。
                                        </div>
                                    </el-form-item>
                                    
                                    <el-form-item>
                                        <el-button type="primary" @click="saveProjectSettings">保存项目设置</el-button>
                                    </el-form-item>
                                </el-form>
                            </div>
                        </el-tab-pane>
                        
                        <el-tab-pane>
                            <template #label>
                                <div class="tab-label">
                                    <el-icon><User /></el-icon>
                                    <span>成员管理</span>
                                </div>
                            </template>
                            
                            <div class="project-modal-section">
                                <div class="project-modal-section-title">
                                    <el-icon><User /></el-icon> 项目成员管理
                                </div>
                                
                                <div class="project-members-list">
                                    <!-- 添加成员表单 -->
                                    <div class="member-add-form">
                                        <el-form :inline="true" class="member-form">
                                            <el-form-item style="margin-right: 10px; width: 200px;">
                                                <el-select 
                                                    v-model="projectDialog.newMember.userId" 
                                                    placeholder="选择用户" 
                                                    filterable
                                                    style="width: 100%;">
                                                    <el-option-group
                                                        v-for="group in projectDialog.availableUsers.reduce((groups, user) => {
                                                            const dept = user.department || '未分组';
                                                            (groups[dept] = groups[dept] || []).push(user);
                                                            return groups;
                                                        }, {})"
                                                        :key="$index"
                                                        :label="$index">
                                                        <el-option
                                                            v-for="user in group"
                                                            :key="user.id"
                                                            :label="user.name"
                                                            :value="user.id">
                                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                                <span>{{ user.name }}</span>
                                                                <span style="color: #909399; font-size: 12px;">
                                                                    {{ user.department || '未知部门' }}
                                                                </span>
                                                            </div>
                                                        </el-option>
                                                    </el-option-group>
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item style="margin-right: 10px; width: 150px;">
                                                <el-select 
                                                    v-model="projectDialog.newMember.role" 
                                                    placeholder="选择角色"
                                                    style="width: 100%;">
                                                    <el-option label="管理员" value="admin"></el-option>
                                                    <el-option label="普通成员" value="member"></el-option>
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item>
                                                <el-button type="primary" @click="addProjectMember">添加成员</el-button>
                                            </el-form-item>
                                        </el-form>
                                    </div>

                                    <!-- 成员搜索框 -->
                                    <div class="member-search">
                                        <el-input
                                            v-model="projectDialog.memberSearch"
                                            placeholder="搜索成员..."
                                            prefix-icon="Search"
                                            clearable>
                                        </el-input>
                                    </div>

                                    <!-- 成员列表 -->
                                    <div class="members-grid">
                                        <template v-if="projectDialog.members && projectDialog.members.length > 0">
                                            <div class="member-card" 
                                                v-for="member in projectDialog.members.filter(member => 
                                                    !projectDialog.memberSearch || 
                                                    member.name.toLowerCase().includes(projectDialog.memberSearch.toLowerCase()) ||
                                                    (member.role && member.role.toLowerCase().includes(projectDialog.memberSearch.toLowerCase()))
                                                )" 
                                                :key="member.id">
                                                <div class="member-card-content">
                                                    <div class="member-avatar">
                                                        {{ member.name.substring(0, 1) }}
                                                    </div>
                                                    <div class="member-info">
                                                        <div class="member-name">{{ member.name }}</div>
                                                        <div class="member-role">
                                                            {{ member.display_role || 
                                                               (member.role === 'superadmin' ? '超级管理员' : 
                                                                member.role === 'admin' ? '管理员' : 
                                                                member.role === 'member' ? '普通成员' : 
                                                                member.role === '管理员' ? '管理员' :
                                                                member.role === '普通人' ? '普通成员' : 
                                                                member.role === '超级管理员' ? '超级管理员' : 
                                                                member.role === 'marginal' ? '成员' : '未知角色') }}
                                                        </div>
                                                    </div>
                                                    <div class="member-actions">
                                                        <el-dropdown 
                                                            v-if="projectDialog.canManageMembers && member.role !== 'superadmin' && member.role !== '超级管理员'" 
                                                            @command="handleRoleChange($event, member)" 
                                                            trigger="click">
                                                            <el-button type="primary" size="small">
                                                                权限 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                                                            </el-button>
                                                            <template #dropdown>
                                                                <el-dropdown-menu>
                                                                    <el-dropdown-item :command="'admin'" :disabled="member.role === 'admin'">
                                                                        设为管理员
                                                                    </el-dropdown-item>
                                                                    <el-dropdown-item :command="'member'" :disabled="member.role === 'member'">
                                                                        设为普通成员
                                                                    </el-dropdown-item>
                                                                </el-dropdown-menu>
                                                            </template>
                                                        </el-dropdown>
                                                        
                                                        <el-button 
                                                            type="danger" 
                                                            size="small" 
                                                            @click.stop="removeProjectMember(member)"
                                                            :disabled="!projectDialog.canManageMembers || member.role === 'superadmin' || member.role === '超级管理员'">
                                                            <el-icon><Delete /></el-icon>
                                                            移除
                                                        </el-button>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <el-empty v-else description="暂无项目成员" />
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        
                        <el-tab-pane>
                            <template #label>
                                <div class="tab-label">
                                    <el-icon><Delete /></el-icon>
                                    <span>删除项目</span>
                                </div>
                            </template>
                            
                            <div class="project-modal-section">
                                <div class="project-modal-section-title">
                                    <el-icon><Delete /></el-icon> 项目删除
                                </div>
                                
                                <el-alert
                                    title="警告：此操作不可逆"
                                    type="warning"
                                    description="删除项目将会永久删除所有相关数据，包括任务、成员关系等信息，请谨慎操作。"
                                    show-icon
                                    :closable="false"
                                    style="margin-bottom: 20px;">
                                </el-alert>
                                
                                <div class="delete-project-confirm">
                                    <p>如需删除项目，请在下方输入框中输入项目名称 <strong>{{ projectDialog.project.name }}</strong> 以确认</p>
                                    
                                    <el-input 
                                        v-model="projectDialog.deleteConfirmName" 
                                        placeholder="请输入项目名称以确认删除"
                                        style="margin: 15px 0;">
                                    </el-input>
                                    
                                    <el-button 
                                        type="danger" 
                                        @click="deleteProject" 
                                        :disabled="projectDialog.deleteConfirmName !== projectDialog.project.name"
                                        style="width: 100%;">
                                        删除项目
                                    </el-button>
                                </div>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </template>
            <template #footer>
                <el-button @click="projectDialog.visible = false">关闭</el-button>
            </template>
        </el-dialog>
        
        <!-- 任务处理对话框 -->
        <el-dialog
            v-model="taskDialog.visible"
            :title="taskDialog.title"
            width="600px"
            style="padding-top: 6px;"
            :close-on-click-modal="false">
            <template #header v-if="!taskDialog.isEditMode">
                <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                    <span>{{ taskDialog.title }}</span>
                    <el-button
                        type="text"
                        @click="taskDialog.showAdvanced = !taskDialog.showAdvanced"
                        style="padding: 0; ">
                        <el-icon :class="{'is-rotate': taskDialog.showAdvanced}" style="transition: transform 0.3s;">
                            <ArrowDown />
                        </el-icon>
                        <span style="margin-left: 5px; font-size: 12px;">{{ taskDialog.showAdvanced ? '▲' : '▼' }}</span>
                    </el-button>
                </div>
            </template>
            <el-form 
                ref="taskFormRef"
                :model="taskDialog.form"
                :rules="taskDialog.rules"
                label-position="top"
                v-loading="taskDialog.loading">
                
                <el-form-item label="任务名称" prop="name" style="margin-bottom: 15px;">
                    <el-input 
                        v-model="taskDialog.form.name" 
                        placeholder="请输入任务名称"
                        maxlength="50"
                        show-word-limit />
                </el-form-item>
                
                <el-form-item label="任务描述" prop="description" style="margin-bottom: 15px;">
                    <!-- 富文本编辑器 -->
                    <div class="task-rich-editor">
                        <div id="taskEditorToolbar" class="editor-toolbar"></div>
                        <div id="taskEditorContent" class="editor-content" style="height: 180px;"></div>
                        <!-- 隐藏的输入框，用于表单验证 -->
                        <input type="hidden" v-model="taskDialog.form.description">
                    </div>
                </el-form-item>
                
                <el-row :gutter="20" style="margin-bottom: 15px;">
                    <el-col :span="12">
                        <el-form-item label="截止日期" prop="deadline">
                            <el-date-picker
                                v-model="taskDialog.form.deadline"
                                type="date"
                                placeholder="选择截止日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                :disabled-date="disablePastDates"
                                style="width: 100%;"
                                :time-zone="'Asia/Shanghai'" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="紧急程度" prop="urgency">
                            <el-select 
                                v-model="taskDialog.form.urgency" 
                                placeholder="选择紧急程度"
                                style="width: 100%;">
                                <el-option
                                    v-for="option in taskDialog.urgencyOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <!-- 无论是否显示，都保持数据绑定 -->
                <input type="hidden" v-model="taskDialog.form.isImmediate">
                
                <el-form-item v-if="taskDialog.showAdvanced" label="单人单任务（每个负责人必须独立完成）" prop="isImmediate">
                    <el-switch
                        v-model="taskDialog.form.isImmediate"
                        active-text="是"
                        inactive-text="否"
                        :active-value="true"
                        :inactive-value="false" />
                    <div class="el-form-item-tip" style="margin-top: 5px; font-size: 12px; color: #909399;">
                        勾选此项表示此任务需要立即处理，将获得最高优先级。
                    </div>
                </el-form-item>
                
                <el-form-item label="负责人" prop="assignees" style="margin-bottom: 15px;">
                    <!-- <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <el-button 
                            type="primary" 
                            size="small" 
                            @click="handleSelectAllAssignees"
                            style="margin-right: 10px;">
                            {{ selectAllAssignees ? '取消全选' : '全选' }}
                        </el-button>
                        <span style="color: #909399; font-size: 12px;">点击按钮可一键全选/取消全选负责人</span>
                    </div> -->
                    
                    <div class="assignee-select-container" style="display: flex; align-items: center; width: 100%;">
                        <el-select 
                            v-model="taskDialog.form.assignees" 
                            multiple
                            placeholder="选择任务负责人"
                            style="width: 100%;"
                            filterable>
                            <el-option
                                v-for="assignee in taskDialog.availableAssignees"
                                :key="assignee.value"
                                :label="assignee.label"
                                :value="assignee.value">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>{{ assignee.label }}</span>
                                    <span style="color: #909399; font-size: 12px;">
                                        {{ assignee.department || '未知部门' }}
                                    </span>
                                </div>
                            </el-option>
                        </el-select>
                        <!-- 添加部门选择按钮 -->
                        <el-button 
                            type="primary" 
                            icon="Plus"
                            style="margin-left: 8px; width: 88px;"
                            @click="showAssigneeOrgTreeDialog">
                            部门选择
                        </el-button>
                    </div>
                    <!-- <div class="el-form-item-tip" style="margin-top: 5px; font-size: 12px; color: #909399;">
                        可选择多个负责人，未选择时默认为创建者负责。
                    </div> -->
                </el-form-item>
                
                <!-- 添加抄送人字段 -->
                <el-form-item label="任务抄送人" prop="ccUsers">
                    <div class="cc-users-container">
                        <!-- 已选择的抄送人标签区域 -->
                        <div class="cc-users-tags-container">
                            <el-tag
                                v-for="user in taskDialog.form.ccUsers" 
                                :key="user.userid"
                                closable
                                @close="removeCcUser(user)"
                                class="cc-user-tag">
                                {{ user.name }}
                            </el-tag>
                            <!-- 输入框，仅用于占位和触发组织架构选择 -->
                            <input 
                                class="cc-users-input" 
                                placeholder="点击选择抄送人" 
                                readonly
                                @click="showOrgTreeDialog" />
                        </div>
                        
                        <!-- 选择抄送人按钮 -->
                        <!-- <el-button 
                            type="primary" 
                            icon="Plus"
                            class="select-cc-btn"
                            @click="showOrgTreeDialog">
                            选择
                        </el-button> -->
                    </div>
                    <div class="el-form-item-tip" style="font-size: 12px; color: #909399;">
                        选择需要抄送的人员，可多选，默认抄送给负责人相关领导。已选择 {{ taskDialog.form.ccUsers.length }} 位抄送人
                    </div>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeTaskDialog">取消</el-button>
                    <el-button type="primary" @click="submitAddTask(taskFormRef)">
                        {{ taskDialog.isEditMode ? '保存修改' : (taskDialog.isHistoricalMode ? '登记任务' : '创建任务') }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- Template Task Dialog (New) -->
        <el-dialog
            v-model="templateTaskDialog.visible"
            :title="templateTaskDialog.title"
            width="600px"
            :close-on-click-modal="false"
            @closed="closeTemplateTaskDialog"
            destroy-on-close>
            <el-form 
                ref="templateTaskFormRef" 
                :model="templateTaskDialog.form"
                :rules="templateTaskDialog.rules" 
                label-position="top"
                v-loading="templateTaskDialog.loading">
                
                <el-form-item label="任务名称" prop="name">
                    <el-input 
                        v-model="templateTaskDialog.form.name" 
                        placeholder="请输入任务名称"
                        maxlength="50"
                        show-word-limit />
                </el-form-item>
                
                <el-form-item label="任务描述" prop="description">
                    <!-- 富文本编辑器 -->
                    <div class="template-rich-editor">
                        <div id="templateTaskEditorToolbar" class="editor-toolbar"></div>
                        <div id="templateTaskEditorContent" class="editor-content" style="height: 200px;"></div>
                        <!-- 隐藏的输入框，用于表单验证 -->
                        <input type="hidden" v-model="templateTaskDialog.form.description">
                    </div>
                </el-form-item>
                
                <el-row :gutter="20"     style="margin-bottom: 10px;">
                    <el-col :span="12">
                        <el-form-item label="相对截止天数" prop="relative_deadline_days">
                             <el-input-number 
                                v-model="templateTaskDialog.form.relative_deadline_days" 
                                :min="0" 
                                controls-position="right" 
                                placeholder="留空表示无期限" 
                                style="width: 100%"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="紧急程度" prop="urgency">
                            <el-select 
                                v-model="templateTaskDialog.form.urgency" 
                                placeholder="选择紧急程度"
                                style="width: 100%;">
                                <el-option
                                    v-for="option in templateTaskDialog.urgencyOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-form-item label="负责人类型" prop="assignees_type">
                    <el-radio-group v-model="templateTaskDialog.form.assignees_type" @change="handleAssigneesTypeChange">
                        <el-radio label="role">角色</el-radio>
                        <el-radio label="person">具体负责人</el-radio>
                    </el-radio-group>
                </el-form-item>
                
                <el-form-item :label="templateTaskDialog.form.assignees_type === 'role' ? '请输入角色' : '请选择具体负责人'" prop="assignees"
                style="margin-bottom: 35px;"
                >
                    <!-- 当选择角色类型时显示输入框 -->
                    <template v-if="templateTaskDialog.form.assignees_type === 'role'">
                        <el-input 
                            v-model="templateTaskDialog.form.assignees" 
                            placeholder="例如：宗师, HRBP"
                            maxlength="100"
                            show-word-limit />

                    </template>
                    
                    <!-- 当选择具体负责人时显示下拉框 -->
                    <template v-else>
                        <el-select 
                            v-model="templateTaskDialog.form.assignees" 

                            filterable
                            placeholder="请输入负责人或选择具体负责人"
                            style="width: 100%;">
                            <el-option
                                v-for="employee in templateTaskDialog.employeeOptions"
                                :key="employee.id"
                                :label="employee.name"
                                :value="employee.id">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>{{ employee.name }}</span>
                                    <span style="color: #909399; font-size: 12px;">
                                        {{ employee.department || '未知部门' }}
                                    </span>
                                </div>
                            </el-option>
                        </el-select>

                    </template>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeTemplateTaskDialog">取消</el-button>
                    <el-button type="primary" @click="submitTemplateTask">
                        {{ templateTaskDialog.isEditMode ? '保存修改' : '创建任务' }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 编辑模板对话框 -->
        <el-dialog
            v-model="templateEditDialog.visible"
            :title="'编辑模板信息'"
            width="500px"
            :close-on-click-modal="false"
            @closed="closeTemplateEditDialog"
            destroy-on-close>
            <el-form 
                ref="templateEditFormRef" 
                :model="templateEditDialog.form"
                :rules="templateEditDialog.rules" 
                label-position="top"
                v-loading="templateEditDialog.loading">
                
                <el-form-item label="模板名称" prop="name">
                    <el-input 
                        v-model="templateEditDialog.form.name" 
                        placeholder="请输入模板名称"
                        maxlength="100"
                        show-word-limit />
                </el-form-item>
                
                <el-form-item label="模板描述" prop="description">
                    <el-input 
                        v-model="templateEditDialog.form.description" 
                        type="textarea" 
                        :rows="3" 
                        placeholder="请输入模板描述"
                        maxlength="500"
                        show-word-limit />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeTemplateEditDialog">取消</el-button>
                    <el-button type="primary" @click="submitTemplateEdit">保存修改</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 新增模板对话框 -->
        <el-dialog
            v-model="addTemplateDialog.visible"
            title="新增模板"
            width="500px"
            :close-on-click-modal="false"
            @closed="closeAddTemplateDialog"
            destroy-on-close>
            <el-form 
                ref="addTemplateFormRef" 
                :model="addTemplateDialog.form"
                :rules="addTemplateDialog.rules" 
                label-position="top"
                v-loading="addTemplateDialog.loading">
                
                <el-form-item label="模板名称" prop="name">
                    <el-input 
                        v-model="addTemplateDialog.form.name" 
                        placeholder="请输入模板名称"
                        maxlength="100"
                        show-word-limit />
                </el-form-item>
                
                <el-form-item label="模板描述:" prop="description">
                    <el-input 
                        v-model="addTemplateDialog.form.description" 
                        type="textarea" 
                        :rows="3" 
                        placeholder="请输入模板描述"
                        maxlength="500"
                        show-word-limit />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeAddTemplateDialog">取消</el-button>
                    <el-button type="primary" @click="submitAddTemplate">创建模板</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入模板对话框 -->
        <el-dialog
            v-model="importTemplateDialog.visible"
            title="导入模板"
            width="800px"
            style="  min-height:200px;"
            :close-on-click-modal="false"
            @closed="closeImportTemplateDialog"
            destroy-on-close>
            <div v-loading="importTemplateDialog.loading">
                <!-- 替换为下拉框形式 -->
                <el-form label-position="top" v-if="importTemplateDialog.templates.length > 0">
                    <el-form-item label="选择要导入的模板:">
                        <el-select 
                            v-model="importTemplateDialog.selectedTemplateId" 
                            placeholder="请选择模板"
                            style="width: 100%;margin-bottom: 8px;"
                            filterable
                            @change="handleTemplateChange">
                            <el-option
                                v-for="template in importTemplateDialog.templates"
                                :key="template.id"
                                :label="template.name"
                                :value="template.id">
                                <div class="template-option">
                                    <div class="template-option-name">{{ template.name }}</div>
                                    <div class="template-option-desc">{{ template.description || '无描述' }}</div>
                                </div>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    
                    <!-- 添加模板描述显示区域 -->
                    <el-form-item label="模板描述：" v-if="importTemplateDialog.selectedTemplateId" style="margin-bottom: 60px;">
                        <div class="selected-template-desc">
                            {{ getSelectedTemplateDescription() }}
                        </div>

                    </el-form-item>

                    <!-- 动态负责人输入框区域 -->
                    <el-form-item 
                        v-for="(role, index) in importTemplateDialog.roles" 
                        :key="index" 
                        :label="role +':'"
                    style="margin-bottom: 8px;"
                    >
                        <el-select 
                            v-model="importTemplateDialog.roleAssignees[role]" 
                            placeholder="请选择负责人"
                            filterable
                            style="width: 100%;">
                            <el-option
                                v-for="user in importTemplateDialog.availableUsers"
                                :key="user.id"
                                :label="user.name"
                                :value="user.id">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>{{ user.name }}</span>
                                    <span style="color: #909399; font-size: 12px;">
                                        {{ user.department || '未知部门' }}
                                    </span>
                                </div>
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                
                <!-- 无模板时显示 -->
                <el-empty 
                    v-if="!importTemplateDialog.loading && !importTemplateDialog.templates.length" 
                    description="暂无可用模板" />
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeImportTemplateDialog">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="importTemplate"
                        :disabled="!importTemplateDialog.selectedTemplateId || importTemplateDialog.loading">
                        导入
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 添加新的样式 -->
        <style>
        .template-option {
            padding: 8px 0;
        }
        
        .template-option-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
        }
        
        .template-option-desc {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            white-space: normal;
            line-height: 1.4;
        }
        
        .selected-template-desc {
            padding: 12px;
            background-color: var(--el-fill-color-lighter);
            border-radius: 4px;
            font-size: 13px;
            color: var(--el-text-color-regular);
            line-height: 1.6;
        }
        </style>

        <!-- 添加图片查看器组件 -->
        <div id="image-viewer" class="image-viewer" style="display: none;">
            <button class="close-btn" onclick="closeImageViewer()"></button>
            <div class="image-viewer-container">
                <img id="viewer-image" src="" alt="预览图片">
            </div>
        </div>

        <!-- 任务详情对话框 -->
        <el-dialog
            v-model="taskDetailDialog.visible"
            :title="taskDetailDialog.title"
            width="1165px"

            destroy-on-close>
            <template #header>
                <div style="display: flex; align-items: center;  width: 100%;">
                    <span>{{ taskDetailDialog.title }}</span>
                    <el-button 
                    style="margin-left: 10px;"
                        type="success" 
                        size="small" 
                        @click="exportTaskDetailData"
                        :loading="taskDetailDialog.exportLoading">
                        <el-icon><Download /></el-icon> 导出Excel
                    </el-button>
                </div>
            </template>
            <div v-loading="taskDetailDialog.loading">
                <el-table
                    :data="taskDetailDialog.tasks"
                    style="width: 100%;     height: 450px !important;    min-height: 450px !important;"
                    :empty-text="taskDetailDialog.loading ? '' : '暂无数据'"
                    :header-cell-style="{background:'#f8fafc',color:'#606266',fontWeight:'500'}"
                    :cell-style="{padding: '8px 0'}">
                    <el-table-column
                        type="index"
                        label="序号"
                        width="53"
                        align="center">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="任务名称"
                        min-width="220"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column
                        prop="project_name"
                        label="项目名称"
                        min-width="120"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column
                        prop="created_at"
                        label="创建时间"
                        width="160">
                    </el-table-column>
                    <el-table-column
                    prop="creator_name"
                    label="创建人"
                    width="80">
                    </el-table-column>
                    <el-table-column
                        prop="completed_date"
                        label="完成时间"
                        width="120">
                        <template #default="{row}">
                            {{ row.completed_date || '未完成' }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="deadline"
                        label="截止时间"
                        width="120">
                        <template #default="{row}">
                            {{ row.deadline || '无截止时间' }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="urgency"
                        label="紧急程度"
                        width="80">
                        <template #default="{row}">
                            <el-tag :type="row.urgency === '紧急' ? 'danger' : 'success'" effect="plain">
                                {{ row.urgency }}
                            </el-tag>
                        </template>
                    </el-table-column> 
                    <el-table-column 
                    label="任务状态" 
                    width="125">
                    <template #default="{row}">
                        <el-tag :type="getTaskStatusLabel(row).type" effect="light">
                            {{ getTaskStatusLabel(row).label }}
                        </el-tag>
                    </template>
                </el-table-column>
                </el-table>
                
                <!-- 分页组件 -->
                <div class="pagination-container" style="margin-top: 5px;">
                    <el-pagination
                        background
                        layout="prev, pager, next, jumper, total"
                        :page-size="taskDetailDialog.pagination.pageSize"
                        :current-page="taskDetailDialog.pagination.currentPage"
                        :total="taskDetailDialog.pagination.total"
                        @current-change="handleTaskDetailPageChange">
                    </el-pagination>
                </div>
            </div>
            <template #footer style="padding-top: 1px;">
                <div class="dialog-footer">
                    <el-button @click="closeTaskDetailDialog">关闭</el-button>
                </div>
            </template>
        </el-dialog>

    <script>
        let scale = 1;
        let currentX = 0;
        let currentY = 0;
        let initialDistance = 0;
        let initialScale = 1;
        const SCALE_STEP = 0.1;
        const MIN_SCALE = 0.1;
        const MAX_SCALE = 5;
        const DRAG_SENSITIVITY = 0.7; // 拖拽灵敏度系数
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let lastX = 0;
        let lastY = 0;

        // 显示图片查看器
        function showImageViewer(src) {
            const imageViewer = document.getElementById('image-viewer');
            const viewerImage = document.getElementById('viewer-image');
            
            if (!imageViewer || !viewerImage) return;
            
            // 重置所有变量
            scale = 1;
            currentX = 0;
            currentY = 0;
            viewerImage.style.transform = `scale(${scale}) translate(${currentX}px, ${currentY}px)`;
            viewerImage.src = src;
            imageViewer.style.display = 'flex';
        }

        // 关闭图片查看器
        function closeImageViewer() {
            const imageViewer = document.getElementById('image-viewer');
            if (imageViewer) {
                imageViewer.style.display = 'none';
            }
        }

        // 处理图片缩放
        function handleImageZoom(event) {
            event.preventDefault();
            const delta = Math.sign(event.deltaY) * -1;
            const newScale = scale + delta * SCALE_STEP;
            
            if (newScale >= MIN_SCALE && newScale <= MAX_SCALE) {
                scale = newScale;
                updateImageTransform();
            }
        }

        // 处理触摸开始
        function handleTouchStart(event) {
            // 如果点击到关闭按钮，不处理触摸事件
            if (event.target.classList.contains('close-btn')) {
                return;
            }

            const touches = event.touches;
            const viewerImage = document.getElementById('viewer-image');
            
            // 双指触摸 - 准备缩放
            if (touches.length === 2) {
                initialDistance = getDistance(touches[0], touches[1]);
                initialScale = scale;
            }
            // 单指触摸 - 准备拖动
            else if (touches.length === 1 && viewerImage) {
                isDragging = true;
                lastX = touches[0].clientX;
                lastY = touches[0].clientY;
            }
        }

        // 处理触摸移动
        function handleTouchMove(event) {
            event.preventDefault();
            const touches = event.touches;
            const viewerImage = document.getElementById('viewer-image');
            
            if (!viewerImage) return;
            
            // 双指缩放
            if (touches.length === 2) {
                const currentDistance = getDistance(touches[0], touches[1]);
                const newScale = (initialScale * currentDistance) / initialDistance;
                
                if (newScale >= MIN_SCALE && newScale <= MAX_SCALE) {
                    scale = newScale;
                    updateImageTransform();
                }
            }
            // 单指拖动
            else if (touches.length === 1 && isDragging) {
                const bounds = calculateDragBounds();
                
                // 计算触摸移动的距离
                const deltaX = (touches[0].clientX - lastX) * DRAG_SENSITIVITY;
                const deltaY = (touches[0].clientY - lastY) * DRAG_SENSITIVITY;
                
                // 更新最后位置
                lastX = touches[0].clientX;
                lastY = touches[0].clientY;

                // 计算新位置
                const newX = currentX + deltaX;
                const newY = currentY + deltaY;
                
                // 限制拖动范围
                currentX = Math.min(Math.max(newX, -bounds.maxX), bounds.maxX);
                currentY = Math.min(Math.max(newY, -bounds.maxY), bounds.maxY);
                
                updateImageTransform();
            }
        }

        // 处理触摸结束
        function handleTouchEnd() {
            isDragging = false;
            initialDistance = 0;
            
            // 如果缩放比例接近1，则重置位置
            if (Math.abs(scale - 1) < 0.1) {
                resetImagePosition();
            }
        }

        // 获取两个触摸点之间的距离
        function getDistance(touch1, touch2) {
            const dx = touch1.clientX - touch2.clientX;
            const dy = touch1.clientY - touch2.clientY;
            return Math.sqrt(dx * dx + dy * dy);
        }

        // 更新图片变换
        function updateImageTransform() {
            const viewerImage = document.getElementById('viewer-image');
            if (viewerImage) {
                viewerImage.style.transform = `scale(${scale}) translate(${currentX}px, ${currentY}px)`;
            }
        }

        // 重置图片位置
        function resetImagePosition() {
            scale = 1;
            currentX = 0;
            currentY = 0;
            updateImageTransform();
        }

        // 双击处理
        function handleDoubleClick(event) {
            if (scale === 1) {
                // 放大到2倍，以双击位置为中心
                scale = 2;
                const rect = event.target.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                currentX = (rect.width / 2 - x) * 0.5;
                currentY = (rect.height / 2 - y) * 0.5;
            } else {
                // 重置到原始大小
                resetImagePosition();
            }
            updateImageTransform();
        }

        // 为日志内容中的图片添加点击事件
        function initializeLogImagePreview() {
            const logContents = document.querySelectorAll('.log-content');
            logContents.forEach(content => {
                const images = content.querySelectorAll('img');
                images.forEach(img => {
                    img.style.cursor = 'pointer';
                    img.onclick = function() {
                        showImageViewer(this.src);
                    };
                });
            });
        }

        // 监听DOM变化，为新添加的日志内容中的图片添加点击事件
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length) {
                    initializeLogImagePreview();
                }
            });
        });

        // 开始观察
        document.addEventListener('DOMContentLoaded', () => {
            const app = document.getElementById('app');
            const imageViewer = document.getElementById('image-viewer');
            const viewerImage = document.getElementById('viewer-image');
            
            if (app) {
                observer.observe(app, { childList: true, subtree: true });
                initializeLogImagePreview();
            }

            if (imageViewer && viewerImage) {
                // 添加滚轮缩放事件
                imageViewer.addEventListener('wheel', handleImageZoom);
                // 添加移动端触摸事件 - 绑定到整个查看器容器
                imageViewer.addEventListener('touchstart', handleTouchStart, { passive: false });
                imageViewer.addEventListener('touchmove', handleTouchMove, { passive: false });
                imageViewer.addEventListener('touchend', handleTouchEnd);
                // 添加双击事件 - 保持在图片元素上
                viewerImage.addEventListener('dblclick', handleDoubleClick);
                
                // 优化的鼠标拖动事件
                viewerImage.addEventListener('mousedown', handleMouseDown);
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                // 防止拖动时选中图片
                viewerImage.addEventListener('dragstart', (e) => e.preventDefault());
            }
        });

        // 按ESC键关闭图片查看器
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageViewer();
            }
        });

        // 计算可拖动范围
        function calculateDragBounds() {
            const viewerImage = document.getElementById('viewer-image');
            if (!viewerImage) return { maxX: 0, maxY: 0 };

            const rect = viewerImage.getBoundingClientRect();
            const viewerWidth = window.innerWidth;
            const viewerHeight = window.innerHeight;

            // 计算图片实际尺寸（考虑缩放）
            const scaledWidth = rect.width * scale;
            const scaledHeight = rect.height * scale;

            // 计算最大可拖动距离
            const maxX = Math.max(0, (scaledWidth - viewerWidth) / 2);
            const maxY = Math.max(0, (scaledHeight - viewerHeight) / 2);

            return { maxX, maxY };
        }

        // 处理鼠标拖动开始
        function handleMouseDown(e) {
            if (e.button !== 0) return; // 只响应左键
            isDragging = true;
            lastX = e.clientX;
            lastY = e.clientY;
            
            const imageViewer = document.getElementById('image-viewer');
            if (imageViewer) {
                imageViewer.classList.add('dragging');
            }
        }

        // 处理鼠标拖动
        function handleMouseMove(e) {
            if (!isDragging) return;

            const bounds = calculateDragBounds();
            
            // 计算鼠标移动的距离
            const deltaX = (e.clientX - lastX) * DRAG_SENSITIVITY;
            const deltaY = (e.clientY - lastY) * DRAG_SENSITIVITY;
            
            // 更新最后位置
            lastX = e.clientX;
            lastY = e.clientY;

            // 计算新位置
            const newX = currentX + deltaX;
            const newY = currentY + deltaY;

            // 限制拖动范围
            currentX = Math.min(Math.max(newX, -bounds.maxX), bounds.maxX);
            currentY = Math.min(Math.max(newY, -bounds.maxY), bounds.maxY);

            updateImageTransform();
        }

        // 处理鼠标拖动结束
        function handleMouseUp() {
            isDragging = false;
            const imageViewer = document.getElementById('image-viewer');
            if (imageViewer) {
                imageViewer.classList.remove('dragging');
            }
        }

        // 更新图片变换
        function updateImageTransform() {
            const viewerImage = document.getElementById('viewer-image');
            if (viewerImage) {
                viewerImage.style.transform = `scale(${scale}) translate(${currentX}px, ${currentY}px)`;
            }
        }

        // 开始观察
        document.addEventListener('DOMContentLoaded', () => {
            const app = document.getElementById('app');
            const imageViewer = document.getElementById('image-viewer');
            const viewerImage = document.getElementById('viewer-image');
            
            if (app) {
                observer.observe(app, { childList: true, subtree: true });
                initializeLogImagePreview();
            }

            if (imageViewer && viewerImage) {
                // 添加滚轮缩放事件
                imageViewer.addEventListener('wheel', handleImageZoom);
                // 添加移动端触摸事件 - 绑定到整个查看器容器
                imageViewer.addEventListener('touchstart', handleTouchStart, { passive: false });
                imageViewer.addEventListener('touchmove', handleTouchMove, { passive: false });
                imageViewer.addEventListener('touchend', handleTouchEnd);
                // 添加双击事件 - 保持在图片元素上
                viewerImage.addEventListener('dblclick', handleDoubleClick);
                
                // 优化的鼠标拖动事件
                viewerImage.addEventListener('mousedown', handleMouseDown);
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                // 防止拖动时选中图片
                viewerImage.addEventListener('dragstart', (e) => e.preventDefault());
            }
        });
    </script>

        <!-- 组织架构树对话框 -->
        <el-dialog
            v-model="orgTreeDialog.visible"
            :title="orgTreeDialog.dialogMode === 'cc' ? '选择抄送人' : '选择负责人'"
            width="650px"
            :close-on-click-modal="false"
            destroy-on-close>
            <div class="org-dialog-container">
                <!-- 左侧部门树和用户列表 -->
                <div class="org-left-panel">
                    <!-- 搜索框 -->
                    <div class="org-search-box">
                        <el-input
                            v-model="orgTreeDialog.searchKeyword"
                            placeholder="搜索部门或成员..."
                            prefix-icon="Search"
                            clearable
                            @input="searchOrgMembers">
                        </el-input>
                    </div>
                    
                    <!-- 部门导航路径 -->
                    <div class="org-breadcrumb" v-if="orgTreeDialog.currentPath.length > 0">
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item 
                                v-for="(item, index) in orgTreeDialog.currentPath" 
                                :key="item.dept_id"
                                @click="navigateToDept(item, index)">
                                {{ item.name }}
                            </el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    
                    <!-- 全选当前部门 - 仅在非搜索模式下显示 -->
                    <div class="org-select-all" v-if="orgTreeDialog.currentDept && !orgTreeDialog.searchKeyword">
                        <el-checkbox 
                            v-model="orgTreeDialog.selectAllChecked"
                            @change="handleSelectAllDept">
                            全选
                             <!-- ({{ orgTreeDialog.deptUsers.length }}人) -->
                        </el-checkbox>
                    </div>
                    
                    <!-- 部门和用户列表 -->
                    <div class="org-list-container">
                        <!-- 搜索结果模式 -->
                        <template v-if="orgTreeDialog.searchKeyword">
                            <div class="org-search-results">
                                <!-- 部门搜索结果 -->
                                <div class="org-search-section" v-if="orgTreeDialog.searchDepts.length > 0">
                                    <div class="org-search-title">部门</div>
                                    <div 
                                        v-for="dept in orgTreeDialog.searchDepts" 
                                        :key="'dept-'+dept.dept_id"
                                        class="org-dept-item">
                                        <div class="org-dept-checkbox">
                                            <el-checkbox 
                                                :model-value="isDeptSelected(dept)" 
                                                @change="(val) => handleSelectDept(dept, val)"
                                                @click.stop>
                                            </el-checkbox>
                                        </div>
                                        <div class="org-dept-content" @click="handleDeptClick(dept)">
                                            <span class="org-dept-icon"><el-icon><Folder /></el-icon></span>
                                            <span class="org-dept-count">({{ dept.total_users || 0 }}人)</span>
                                            <span class="org-dept-name">{{ dept.name }}</span>
                                            
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 用户搜索结果 -->
                                <div class="org-search-section" v-if="orgTreeDialog.searchUsers.length > 0">
                                    <div class="org-search-title">成员</div>
                                    <div 
                                        v-for="user in orgTreeDialog.searchUsers" 
                                        :key="'user-'+user.userid"
                                        class="org-user-item"
                                        :class="{'disabled-user': user.disabled}">
                                        <div class="org-user-info">
                                            <div class="org-user-avatar">{{ user.name.substring(0, 1) }}</div>
                                            <div class="org-user-details">
                                                <div class="org-user-name">
                                                    {{ user.name }}
                                                    <span v-if="user.isCurrentUser && orgTreeDialog.dialogMode !== 'cc'" class="current-user-tag">(自己)</span>
                                                </div>
                                                <div class="org-user-title" v-if="user.dept_name">{{ user.dept_name }}</div>
                                            </div>
                                        </div>
                                        <el-checkbox 
                                            :model-value="isUserSelected(user)" 
                                            :disabled="user.disabled"
                                            @change="() => toggleCcUser(user)">
                                        </el-checkbox>
                                    </div>
                                </div>
                                
                                <!-- 无搜索结果 -->
                                <el-empty 
                                    v-if="orgTreeDialog.searchDepts.length === 0 && orgTreeDialog.searchUsers.length === 0"
                                    description="无搜索结果">
                                </el-empty>
                            </div>
                        </template>
                        
                        <!-- 正常浏览模式 -->
                        <template v-else>
                            <!-- 子部门列表 -->
                            <div class="org-dept-list" v-if="orgTreeDialog.childDepts.length > 0">
                                <div class="org-list-title">部门</div>
                                <div 
                                    v-for="dept in orgTreeDialog.childDepts" 
                                    :key="'dept-'+dept.dept_id"
                                    class="org-dept-item">
                                    <div class="org-dept-checkbox">
                                        <el-checkbox 
                                            :model-value="isDeptSelected(dept)" 
                                            @change="(val) => handleSelectDept(dept, val)"
                                            @click.stop>
                                        </el-checkbox>
                                    </div>
                                    <div class="org-dept-content" @click="handleDeptClick(dept)">
                                        <span class="org-dept-icon"><el-icon><Folder /></el-icon></span>
                                        <span class="org-dept-count">({{ dept.total_users || 0 }}人)</span>
                                        <span class="org-dept-name">{{ dept.name }}</span>
                                     
                                        <span class="org-dept-arrow"><el-icon><ArrowRight /></el-icon></span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 部门用户列表 -->
                            <div class="org-user-list" v-if="orgTreeDialog.deptUsers.length > 0">
                                <div class="org-list-title">成员</div>
                                <div 
                                    v-for="user in orgTreeDialog.deptUsers" 
                                    :key="'user-'+user.userid"
                                    class="org-user-item"
                                    :class="{'disabled-user': user.disabled}">
                                    <div class="org-user-info">
                                        <div class="org-user-avatar">{{ user.name.substring(0, 1) }}</div>
                                        <div class="org-user-name">
                                            {{ user.name }}
                                            <span v-if="user.isCurrentUser && orgTreeDialog.dialogMode !== 'cc'" class="current-user-tag">(自己)</span>
                                        </div>
                                    </div>
                                    <el-checkbox 
                                        :model-value="isUserSelected(user)" 
                                        :disabled="user.disabled"
                                        @change="() => toggleCcUser(user)">
                                    </el-checkbox>
                                </div>
                            </div>
                            
                            <!-- 空部门 -->
                            <el-empty 
                                v-if="orgTreeDialog.childDepts.length === 0 && orgTreeDialog.deptUsers.length === 0"
                                description="该部门下没有子部门和成员">
                            </el-empty>
                        </template>
                    </div>
                </div>
                
                <!-- 右侧已选人员 -->
                <div class="org-right-panel">
                    <div class="org-selected-header">
                        <div class="org-selected-title">已选择 <span class="org-selected-count">{{ orgTreeDialog.dialogMode === 'cc' ? taskDialog.form.ccUsers.length : taskDialog.form.assignees.length }}</span>/1000</div>
                        <el-button 
                            type="text" 
                            @click="clearSelectedUsers"
                            :disabled="orgTreeDialog.dialogMode === 'cc' ? taskDialog.form.ccUsers.length === 0 : taskDialog.form.assignees.length === 0">
                            清空
                        </el-button>
                    </div>
                    <div class="org-selected-list">
                        <!-- 抄送人模式 -->
                        <template v-if="orgTreeDialog.dialogMode === 'cc'">
                            <div 
                                v-for="user in taskDialog.form.ccUsers" 
                                :key="user.userid"
                                class="org-selected-item">
                                <div class="org-selected-avatar">{{ user.name.substring(0, 1) }}</div>
                                <div class="org-selected-name">{{ user.name }}</div>
                                <el-button 
                                    type="text" 
                                    class="org-selected-remove" 
                                    @click="removeCcUser(user)">
                                    <el-icon><Close /></el-icon>
                                </el-button>
                            </div>
                        </template>
                        
                        <!-- 负责人模式 -->
                        <template v-else>
                            <div 
                                v-for="assigneeId in taskDialog.form.assignees" 
                                :key="assigneeId"
                                class="org-selected-item">
                                <div class="org-selected-avatar">
                                    {{ (taskDialog.availableAssignees.find(a => a.value === assigneeId)?.label || '').substring(0, 1) }}
                                </div>
                                <div class="org-selected-name">
                                    {{ taskDialog.availableAssignees.find(a => a.value === assigneeId)?.label || '' }}
                                </div>
                                <el-button 
                                    type="text" 
                                    class="org-selected-remove" 
                                    @click="removeCcUser({userid: assigneeId})">
                                    <el-icon><Close /></el-icon>
                                </el-button>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelCcUsers">取消</el-button>
                    <el-button type="primary" @click="confirmCcUsers">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
    <!-- Vue模板结束 -->
{% endraw %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ cdn_static('js/vue.global.prod.js') }}"></script>
<script src="{{ cdn_static('js/element-plus.js') }}"></script>
<script src="{{ cdn_static('js/element-plus-icons-vue.js') }}"></script>
<script src="{{ cdn_static('js/element-plus-locale-zh-cn.min.js') }}"></script>
<script src="{{ cdn_static('js/echarts.min.js') }}"></script>
<!-- 引入富文本编辑器 -->
<script src="{{ cdn_static('js/wangeditor.js') }}"></script>

<!-- 添加JS哈希值到window对象 -->
<script>
window.cdn_js_hash = {{ cdn_js_hash|tojson|safe }};
</script>

<!-- 使用type="importmap"定义导入映射 -->
<script type="importmap">
{
  "imports": {
    "/cdn/js/pm_utils.js": "/cdn/js/pm_utils.js?v={{ cdn_js_hash.get('js/pm_utils.js', '') }}",
    "/cdn/js/pm_members.js": "/cdn/js/pm_members.js?v={{ cdn_js_hash.get('js/pm_members.js', '') }}",
    "/cdn/js/pm_tasks.js": "/cdn/js/pm_tasks.js?v={{ cdn_js_hash.get('js/pm_tasks.js', '') }}",
    "/cdn/js/pm_home.js": "/cdn/js/pm_home.js?v={{ cdn_js_hash.get('js/pm_home.js', '') }}",
    "/cdn/js/pm_charts.js": "/cdn/js/pm_charts.js?v={{ cdn_js_hash.get('js/pm_charts.js', '') }}",
    "/cdn/js/pm_task_handler.js": "/cdn/js/pm_task_handler.js?v={{ cdn_js_hash.get('js/pm_task_handler.js', '') }}",
    "/cdn/js/pm_templates.js": "/cdn/js/pm_templates.js?v={{ cdn_js_hash.get('js/pm_templates.js', '') }}",
    "/cdn/js/pm_task_delete_approval.js": "/cdn/js/pm_task_delete_approval.js?v={{ cdn_js_hash.get('js/pm_task_delete_approval.js', '') }}"
  }
}
</script>

<script type="module" src="{{ cdn_static('js/project_management_app.js') }}"></script>
<script src="{{ cdn_static('js/project_management_mobile.js') }}"></script>

<style>
/* 组织架构树样式 */
.org-dept-item {
    display: flex;
    align-items: center;
    padding: 0px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.org-dept-item:hover {
    background-color: #f5f7fa;
}

.org-dept-checkbox {
    margin-right: 8px;
    display: flex;
    align-items: center;
}

.org-dept-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.org-dept-icon {
    /* margin-right: 8px; */
    /* color: #909399; */
}

.org-dept-name {
    flex: 1;
}

.org-dept-count {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
}

.org-dept-arrow {
    margin-left: 8px;
    color: #909399;
}

/* 当前用户样式 */
.disabled-user {
    opacity: 0.6;
    cursor: not-allowed;
}

.current-user-tag {
    font-size: 12px;
    color: #909399;
    margin-left: 5px;
}
</style>
{% endblock %}

<!-- 添加移动端适配JS -->
<script src="/static/js/project_management_mobile.js?v={{ cdn_js_hash }}"></script>
<!-- 添加移动端"我的关注"视图适配JS -->
<script src="/static/js/project_management_mobile_received.js?v={{ cdn_js_hash }}"></script>

</body>
</html>