/* 考虑侧边栏的主内容区域样式 */
.main-content {
    background-color: #f5f7fa;
    min-height: 100vh;
    width: 100%;
    padding: 20px !important;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    /* 侧边栏宽度大约是75px，为了安全起见我们留出80px */
    margin-left: 130px !important;
    width: calc(100% - 80px) !important;
    box-sizing: border-box;
}

/* 顶部信息栏 - 居中显示 */
.top-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    text-align: center;
}

.current-date {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.greeting {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

/* 新增包装器样式，用于定位统计栏和表情包 */
.task-stats-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 修改任务统计栏样式 */
.task-stats {
    display: flex;
    border-radius: 30px;
    background-color: white;
    padding: 5px 7px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    flex: 1;
}

.stat-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 5px;
    position: relative;
}

.stat-item:not(:last-child)::after {
    content: "";
    position: absolute;
    right: 0;
    height: 70%;
    width: 1px;
    background-color: #eee;
}

.stat-number {
    font-size: 17px;
    font-weight: bold;
    color: #1a73e8;
    margin-right: 5px;
}

.stat-text {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

/* 任务面板 */
.panels-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 9px;
    width: 100%;
    margin-top: 9px;
}

@media (max-width: 1280px) {
    .panels-container {
        grid-template-columns: 1fr;
    }
}

.info-panel {
    background-color: white;
    border-radius: 16px;
    padding: 14px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    min-height: 320px;
    position: relative;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 7px;
    border-bottom: 2px solid #f5f5f5;
    padding-bottom: 3px;
}

.panel-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
}

.panel-title i {
    margin-right: 8px;
    color: #1a73e8;
}

.panel-content {
    height: calc(100% - 35px);
    overflow-y: auto;
    min-height: 240px;
}

/* 公告项样式 */
.announcement-item {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
    cursor: pointer;
    min-height: 70px;
}

.announcement-item:hover {
    background-color: #f8f9fa;
}

.announcement-item:last-child {
    border-bottom: none;
}

.announcement-icon {
    color: #1a73e8;
    margin-right: 12px;
    font-size: 16px;
    flex-shrink: 0;
}

.announcement-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 28px;
    width: 100%;
    min-width: 0;
}

.announcement-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.8;
    flex: 1;
}

.announcement-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
    margin-top: 6px;
    white-space: nowrap;
}

.announcement-meta span:first-child {
    color: #888;
    font-size: 13px;
    flex-shrink: 0;
}

.announcement-meta .tag {
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    padding: 0 10px;
    font-size: 13px;
    border-radius: 12px;
    font-weight: 500;
}

/* 面板操作按钮容器 */
.panel-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 新增反馈按钮样式 */
.add-feedback-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.add-feedback-btn:hover {
    background-color: #1557b0;
}

.add-feedback-btn i {
    font-size: 12px;
}

/* 优化待办事项布局 */
.todo-item {
    display: flex;
    align-items: flex-start;
    padding: 7px 11px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: background-color 0.2s;
    outline: none;
    -webkit-tap-highlight-color: transparent;
    min-height: 60px;
    height: 70px;
}

.todo-status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    margin-top: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* 修改完成状态的样式 */
.todo-status.completed {
    background-color: #1a73e8;
    border-color: #1a73e8;
}

.todo-status.completed i {
    font-size: 12px;
    color: white;
    display: block;
}

.todo-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    min-width: 0;
}

.todo-main {
    flex: 1;
    min-width: 0;
    padding-right: 16px;
}

.todo-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    line-height: 1.4;
}

.todo-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.todo-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    /*min-width: 180px;*/
}

.todo-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: flex-end;
}

.todo-info {
    display: flex;
    justify-content: flex-end;
    /*margin-top: 8px;*/
    font-size: 12px;
    color: #666;
}

.todo-source, .todo-time {
    color: #888;
    white-space: nowrap;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
    .todo-content {
        flex-direction: column;
    }
    
    .todo-main {
        padding-right: 0;
        margin-bottom: 8px;
    }
    
    .todo-meta {
        width: 100%;
        align-items: flex-start;
    }
    
    .todo-info {
        align-items: flex-start;
    }
}

/* 优先级标签 */
.priority-normal {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.priority-important {
    background-color: #fff3cd;
    color: #856404;
}

.priority-urgent {
    background-color: #f8d7da;
    color: #721c24;
}

/* 统计数字强调 */
.has-overdue .stat-number {
    color: #d32f2f;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 标签样式 */
.tag {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.tag-info {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.tag-success {
    background-color: #e6f4ea;
    color: #1e8e3e;
}

.tag-warning {
    background-color: #fef7e0;
    color: #f9ab00;
}

.tag-primary {
    background-color: #e8f0fe;
    color: #1a73e8;
}

/* 新增标签样式 */
.tag-danger {
    background-color: #ffebee;
    color: #d32f2f;
}

/* 水印图标 */
.mascot-image {
    position: absolute;
    right: 20px;
    bottom: 10px;
    width: 100px;
    opacity: 0.9;
}

/* 表情包相关样式 */
.emoticon-container {
    width: 120px;
    height: 120px;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-left: 15px;
}

.emoticon-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.task-stats-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto 9px auto;
}

.overdue-item {
    position: relative;
}

/* 修改表情包容器的定位样式 */
.emoticon-container-absolute {
    position: absolute;
    top: -38px;
    right: -120px;
    width: 100px;
    height: 100px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1; /* 确保表情包在正确的层级 */
}

.emoticon-container-absolute img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .emoticon-container-absolute {
        position: static;
        margin: 15px auto 0 auto;
        transform: none;
    }
}

/* 错误和无数据状态样式 */
.no-data {
    padding: 20px;
    text-align: center;
    color: #666;
    font-size: 14px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.error-message {
    color: #dc3545;
}

/* 刷新按钮样式 */
.refresh-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    transition: transform 0.2s;
}

.refresh-btn:hover {
    color: #1a73e8;
}

.refresh-btn:active {
    transform: rotate(180deg);
}

/* 查看更多按钮样式 */
.view-more-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 13px;
    padding: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.view-more-btn:hover {
    color: #1a73e8;
}

.view-more-btn i {
    font-size: 10px;
}

/* 公告详情弹窗样式 */
.announcement-detail-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.announcement-detail-container {
    background-color: white;
    border-radius: 12px;
    width: 80%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
}

.announcement-detail-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
}

.announcement-detail-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 0 0 10px 0;
}

.announcement-detail-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
    color: #666;
}

.announcement-detail-body {
    padding: 20px 40px;
    overflow-y: auto;
    flex: 1;
    line-height: 1.6;
}

.announcement-detail-body img {
    max-width: 100%;
    cursor: zoom-in;
}

/* 更新关闭按钮样式，使其更加明显 */
.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #eee;
    border: none;
    color: #555;
    font-size: 16px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.close-btn::before {
    content: "×"; /* 使用乘号作为X */
    font-size: 22px;
    font-weight: 600;
    line-height: 1;
}

.close-btn:hover {
    background-color: #e0e0e0;
    color: #333;
    transform: scale(1.05);
}

/* 针对图片查看器的特殊关闭按钮样式 */
.image-viewer .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 1110;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;  /* 移除内边距 */
    line-height: 0;  /* 重置行高 */
}

.image-viewer .close-btn::before {
    content: "×";
    font-size: 28px;
    font-weight: 700;
    display: block;
    margin-top: -2px;  /* 微调垂直位置 */
    text-align: center;
    width: 100%;
    height: 100%;
    line-height: 40px;  /* 与按钮高度相同 */
}

.image-viewer .close-btn:hover {
    background-color: white;
    transform: scale(1.1);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4);
}

/* 图片查看器样式 */
.image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    touch-action: none;
    cursor: grab;
}

.image-viewer-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

#viewer-image {
    max-width: 90vw;
    max-height: 90vh;
    object-fit: contain;
    transition: transform 0.2s ease;
    transform-origin: center center;
    will-change: transform;
    touch-action: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    cursor: grab;
}

#viewer-image:active {
    cursor: grabbing;
}

/* 右上角搜索按钮样式优化 */
.search-toggle-btn {
    background-color: #e8f0fe;
    border: none;
    color: #1a73e8;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.search-toggle-btn::after {
    content: "搜索";
    font-size: 13px;
}

/* 当搜索面板打开时，变更按钮样式 */
.search-toggle-btn.active {
    background-color: #1a73e8;
    color: white;
}

.search-toggle-btn.active::after {
    content: "关闭";
}

/* 搜索和过滤相关样式 */
.search-filters-container {
    padding: 12px;
    background-color: #f8fafd;
    border-radius: 8px;
    margin-bottom: 12px;
    border: 1px solid #e1e8f5;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 紧凑型搜索布局 */
.compact-search-layout {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 搜索顶部行 - 搜索框和分类选择 */
.search-top-row {
    display: flex;
    gap: 8px;
    width: 100%;
}

.search-input-group {
    flex: 3; /* 搜索框占更多空间 */
    position: relative;
}

.search-input-group::before {
    content: "\f002"; /* Font Awesome 搜索图标 */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
    font-size: 12px;
}

.filter-select-group {
    flex: 1; /* 分类选择占较少空间 */
}

/* 搜索底部行 - 日期范围和按钮 */
.search-bottom-row {
    display: flex;
    gap: 8px;
    width: 100%;
    align-items: center;
}

.date-range-compact {
    flex: 3;
    display: flex;
    align-items: center;
    gap: 4px;
}

.date-separator {
    color: #666;
    font-size: 12px;
    padding: 0 2px;
}

.filter-buttons-group {
    flex: 1;
    display: flex;
    gap: 6px;
}

/* 输入框样式微调 */
.search-input {
    width: 100%;
    padding: 7px 10px 7px 30px; /* 左侧留出图标空间 */
    border: 1px solid #dbe4f5;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.2s;
}

.filter-select {
    width: 100%;
    padding: 7px 8px;
    border: 1px solid #dbe4f5;
    border-radius: 4px;
    font-size: 13px;
    background-color: white;
}

.date-input {
    padding: 6px 8px;
    border: 1px solid #dbe4f5;
    border-radius: 4px;
    font-size: 13px;
    min-width: 0; /* 允许日期输入框缩小 */
    flex: 1;
}

/* 优化按钮尺寸和外观 */
.apply-filter-btn, .clear-filter-btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    gap: 4px;
}

.apply-filter-btn {
    background-color: #1a73e8;
    color: white;
    flex: 1.5;
}

.clear-filter-btn {
    background-color: #f1f3f4;
    color: #5f6368;
    flex: 1;
}

.apply-filter-btn i, .clear-filter-btn i {
    font-size: 10px;
}

/* 搜索结果状态提示精简化 */
.search-result-status {
    padding: 6px 10px;
    margin-bottom: 8px;
    text-align: left;
    color: #666;
    font-size: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #1a73e8;
}

/* 响应式调整 */
@media (max-width: 600px) {
    .search-top-row, .search-bottom-row {
        flex-direction: column;
        gap: 6px;
    }
    
    .filter-buttons-group {
        width: 100%;
    }
}

/* 搜索面板标题和关闭按钮 */
.search-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #eee;
}

.search-panel-title {
    font-size: 13px;
    font-weight: 500;
    color: #666;
}

.search-panel-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 2px 5px;
    font-size: 14px;
    transition: color 0.2s;
}

.search-panel-close:hover {
    color: #555;
}

/* 分页控件样式 */
.pagination-controls {
    margin-top: 15px;
    padding: 10px 0;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid #eee;
    margin-top: auto;
    /* 添加以下属性确保水平布局 */
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.page-btn {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #1a73e8;
    padding: 5px 12px;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s;
}

.page-btn:hover {
    background-color: #e9ecef;
    border-color: #1a73e8;
}

.page-info {
    color: #666;
    font-size: 13px;
}

/* 当没有数据时也显示分页控件 */
.panel-content:empty + .pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

/* 项目更新项样式 */
.project-update-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
    min-width: 0;
}

.project-update-title {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin-bottom: 1px;
}

.project-update-description {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 2px;
}

/* 反馈列表样式 */
.feedback-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feedback-item {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.feedback-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.feedback-title {
    font-weight: 600;
    color: #333;
}

.feedback-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #cce5ff;
    color: #004085;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.status-closed {
    background-color: #e2e3e5;
    color: #383d41;
}

.status-default {
    background-color: #f8f9fa;
    color: #666;
}

.feedback-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
}

.feedback-response {
    background-color: #e8f4f8;
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
}

.feedback-meta {
    color: #999;
    font-size: 12px;
}

/* 反馈弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
}

.submit-btn {
    background-color: #1a73e8;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.cancel-btn {
    background-color: #f1f3f4;
    color: #5f6368;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

/* 回复切换按钮样式 */
.toggle-response-btn {
    background: none;
    border: none;
    color: #1a73e8;
    font-size: 12px;
    cursor: pointer;
    padding: 3px 6px;
    margin-left: 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.toggle-response-btn:hover {
    background-color: #e8f0fe;
}

/* 反馈回复样式 */
.feedback-response {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-top: 10px;
    margin-bottom: 10px;
    border-left: 3px solid #1a73e8;
    transition: opacity 0.3s, max-height 0.3s;
    overflow: hidden;
}

.response-header {
    margin-bottom: 5px;
    color: #333;
    font-size: 13px;
}

.response-content {
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}

/* 反馈元数据样式优化 */
.feedback-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #999;
    font-size: 12px;
    margin-top: 8px;
}

/* 添加以下CSS样式，与项目动态保持一致的反馈列表样式 */

/* 反馈卡片样式优化 */
.feedback-card {
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.feedback-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.08);
}

/* 有回复的反馈卡片样式 */
.feedback-card.has-reply {
    border-left: 3px solid #1a73e8;
}

/* 让回复状态更显眼 */
.feedback-card.has-reply .announcement-icon {
    color: #1a73e8;
}

/* 移除不需要的样式 */
.feedback-card.has-reply::after {
    content: none; /* 移除右上角小蓝点 */
}

.reply-indicator {
    display: none; /* 移除回复指示器 */
}

/* 细微的悬停效果增强 */
.feedback-card.has-reply:hover {
    background-color: #f8f9ff;
    border-left-width: 4px;
}

/* 反馈详情页样式优化 */
.feedback-detail-dialog .announcement-detail-container {
    max-width: 650px;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.feedback-detail-dialog .announcement-detail-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.feedback-detail-dialog .announcement-detail-title {
    font-size: 1.3rem;
    color: #212529;
    font-weight: 600;
}

.feedback-detail-section {
    margin-bottom: 24px;
    padding-bottom: 0;
}

.feedback-detail-heading {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 8px;
}

.feedback-detail-heading i {
    color: #1a73e8;
    font-size: 14px;
}

.feedback-detail-content {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    white-space: pre-line;
    padding: 0 2px;
}

/* 反馈回复区域样式提升 */
.feedback-reply-section {
    background-color: #f0f7ff;
    padding: 18px;
    border-radius: 12px;
    border-left: 4px solid #1a73e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.feedback-reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(26, 115, 232, 0.2);
}

.feedback-reply-section .feedback-detail-heading {
    color: #1a73e8;
    border-bottom: none;
    padding-bottom: 0;
    margin: 0;
}

.feedback-responder {
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: rgba(255, 255, 255, 0.5);
    padding: 4px 10px;
    border-radius: 30px;
}

.feedback-responder i {
    color: #1a73e8;
    font-size: 12px;
}

/* 状态标签改进 */
.feedback-detail-dialog .tag {
    padding: 4px 10px;
    font-weight: 500;
    border-radius: 30px;
    font-size: 12px;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.status-closed {
    background-color: #e2e3e5;
    color: #383d41;
}

/* 反馈元数据布局优化 */
.announcement-item .announcement-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
    margin-top: 4px;
    white-space: nowrap;
}

/* 当容器变窄时，优先保证标题和元数据显示，允许描述被截断 */
@media (max-width: 480px) {
    .project-update-description {
        -webkit-line-clamp: 1;
    }
    
    .announcement-meta {
        margin-top: 4px;
    }
}

/* 统一所有公告项的布局 */
.announcement-item {
    padding: 8px 12px;
    transition: all 0.2s;
}

.announcement-icon {
    margin-right: 8px;
    font-size: 14px;
    flex-shrink: 0;
}

/* 对于小屏幕设备进一步压缩元素 */
@media (max-height: 800px) {
    .info-panel {
        min-height: 220px;
        padding: 12px;
    }
    
    .panel-header {
        margin-bottom: 8px;
        padding-bottom: 5px;
    }
    
    .panel-title {
        font-size: 16px;
    }
    
    .project-update-description {
        -webkit-line-clamp: 1;
        font-size: 12px;
    }
}

/* 调整表情包尺寸 */
.emoticon-container-absolute {
    width: 100px;
    height: 100px;
    right: -120px;
}

/* 优化统计栏内部间距 */
.task-stats {
    padding: 5px 7px;
}

.stat-number {
    font-size: 17px;
}

.stat-text {
    font-size: 12px;
}

/* 添加创建时间的样式 */
.todo-time {
    color: #666;
    font-size: 12px;
    /*margin-right: 8px;*/
    margin-left: 8px;
}

/* 调整 todo-meta 的样式以适应更多内容 */
.todo-meta {
    display: flex;
    /*align-items: center;*/
    flex-wrap: wrap;
    gap: 8px;
    font-size: 12px;
    color: #666;
    /*margin-top: 4px;*/
}

/* 在小屏幕上优化显示 */
@media (max-width: 480px) {
    .todo-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .todo-time, .todo-due-date {
        margin-left: 0;
    }
}

/* 添加来源标签样式 */
.tag-source {
    background-color: #f0f4f8;
    color: #607d8b;
}

/* 优化标签间距和显示 */
.todo-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: flex-end;
}

/* 调整信息区域的对齐方式 */
.todo-info {
    display: flex;
    justify-content: flex-end;
    /*margin-top: 8px;*/
    font-size: 12px;
    color: #666;
}

/* 如果空间不足，确保标签换行美观 */
@media (max-width: 480px) {
    .todo-tags {
        justify-content: flex-start;
    }
    
    .todo-info {
        justify-content: flex-start;
    }
}

/* 在分页控件样式部分添加以下内容 */
.page-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

.page-btn[disabled]:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* 任务详情弹窗样式 */
.task-detail-dialog .announcement-detail-container {
    max-width: 800px;
    width: 90%;
}

/*.task-detail-container {*/
/*    padding: 20px;*/
/*}*/

.task-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
    /*background: #f8f9fa;*/
    padding-bottom: 20px;
    padding-top: 20px;
    border-radius: 8px;
}

.task-info-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-label {
    color: #666;
    font-size: 14px;
    min-width: 100px;
}

.info-label i {
    margin-right: 5px;
    color: #1890ff;
}

.info-value {
    color: #333;
    font-weight: 500;
}

.task-description {
    /*background: #fff;*/
    border-radius: 8px;
    padding: 20px;
    /*margin-top: 20px;*/
    background: #f8f9fa;
}

.description-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.description-title i {
    color: #1890ff;
}

.description-content {
    color: #666;
    line-height: 1.6;
    text-align: center;
    /*white-space: pre-wrap;*/
}

/* 紧急程度标签样式 */
.tag-urgent {
    background-color: #ff4d4f !important;
    color: white !important;
}

.tag-important {
    background-color: #faad14 !important;
    color: white !important;
}

.tag-normal {
    background-color: #52c41a !important;
    color: white !important;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    .task-info-grid {
        grid-template-columns: 1fr;
    }

    .task-detail-dialog .announcement-detail-container {
        width: 95%;
        margin: 10px;
    }

    .task-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .info-label {
        min-width: auto;
    }
}

/* 任务完成部分样式 */
.task-completion-section {
    margin-top: 15px;
    background-color: #fff;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    border: 1px solid #eaeaea;
}

.completion-input-group {
    margin-bottom: 15px;
}

.completion-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s;
    background-color: white;
}

.completion-input:focus {
    border-color: #1a73e8;
    outline: none;
    box-shadow: 0 0 0 3px rgba(26,115,232,0.1);
}

.completion-input.error {
    border-color: #ff4d4f;
}

.completion-error {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
    display: none;
    padding-left: 4px;
}

.completion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
}

.completion-actions button {
    min-width: 100px;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    /*gap: 4px;*/
    transition: all 0.3s;
}

.complete-btn {
    border: none;
    color: white;
    background-color: #1890ff;
}

.complete-btn:hover {
    background-color: #40a9ff;
}

.complete-btn:active {
    background-color: #096dd9;
}

.complete-btn i {
    font-size: 12px;
}

.cancel-task-btn {
    border: 1px solid #d9d9d9;
    background-color: white;
    color: rgba(0, 0, 0, 0.65);
}

.cancel-task-btn:hover {
    color: #40a9ff;
    border-color: #40a9ff;
}

.cancel-task-btn:active {
    color: #096dd9;
    border-color: #096dd9;
}

.cancel-task-btn i {
    font-size: 12px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .task-completion-section {
        padding: 15px;
    }
    
    .completion-input {
        padding: 10px;
    }
    
    .complete-btn {
        width: 100%;
        justify-content: center;
    }
}

.project-name,
.project-description {
    margin: 20px 25px;
    line-height: 1.6;
    color: #333;
}

.project-name strong,
.project-description strong {
    color: #666;
    margin-right: 8px;
}

.description-title + .task-description {
    margin-bottom: 20px;
}

/* 基础样式 */
.status-filter {
    padding: 2px 16px;
    border: 1px solid rgb(26, 115, 232);
    border-radius: 6px;
    background-color: rgb(26, 115, 232);
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
    height: 33px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 30px;
}

/* 鼠标悬停样式 */
.status-filter:hover {
    border-color: rgb(26, 115, 232);
    background-color: rgb(26, 115, 232);
}

/* 聚焦样式 */
.status-filter:focus {
    border-color: rgb(26, 115, 232);
    background-color: rgb(26, 115, 232);
    color: #ffffff;
}

/* 下拉选项样式 */
.status-filter option {
    background-color: #ffffff;
    color: #333;
    padding: 8px;
}

/* 下拉框打开时的样式 */
.status-filter:focus option {
    background-color: #ffffff;
    color: #333;
}

/* 下拉框打开时的选择框样式 */
.status-filter:focus {
    border-color: rgb(26, 115, 232);
    background-color: rgb(26, 115, 232);
    color: #ffffff;
}

/* 添加任务详情美化样式 */
.task-info-card {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    border: 1px solid #eaeaea;
}

.task-info-item {
    margin-bottom: 12px;
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: flex-start;
}

.task-info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.task-info-item strong {
    min-width: 100px;
    color: #555;
    margin-right: 8px;
    display: inline-flex;
    align-items: center;
}

.task-info-item strong i {
    margin-right: 5px;
    width: 16px;
    text-align: center;
    color: #1a73e8;
}

.task-info-item .task-desc-text {
    flex: 1;
    line-height: 1.5;
}

.task-info-item .project-name-text {
    font-weight: 500;
    color: #333;
}

.task-info-item .project-desc-text {
    color: #666;
    font-style: italic;
}

.task-info-item .creator-name-text {
    color: #1a73e8;
    font-weight: 500;
}

.task-info-item .deadline-text {
    color: #f56c6c;
    font-weight: 500;
}

.task-completion-section {
    margin-top: 15px;
    background-color: #fff;
    border-radius: 10px;
    padding: 15px 0;
    box-shadow: 0 1px 3px #fff;
    border: 1px solid #fff;
}

/* 图片预览关闭按钮样式 */
.image-viewer-close-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.image-viewer-close-btn::before,
.image-viewer-close-btn::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: #fff;
    transform-origin: center;
}

.image-viewer-close-btn::before {
    transform: rotate(45deg);
}

.image-viewer-close-btn::after {
    transform: rotate(-45deg);
}

.image-viewer-close-btn:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

/* 图片查看器中的图片样式 */
#viewer-image {
    max-width: 90vw;
    max-height: 90vh;
    object-fit: contain;
    transition: transform 0.2s ease;
    transform-origin: center center;
}