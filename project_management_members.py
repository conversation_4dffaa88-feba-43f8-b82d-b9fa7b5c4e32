from flask import jsonify, session, request
import datetime
import pymysql
from logger_config import setup_logger

logger = setup_logger()

# 用户角色常量
ROLE_SUPER_ADMIN = '超级管理员'
ROLE_ADMIN = '管理员'
ROLE_NORMAL = '普通人'
ROLE_MARGINAL = '边缘人'

def init_project_management_members(app, mysql=None, employee_db=None, redis_manager=None):
    """初始化项目成员管理模块"""
    
    # 如果提供了redis_manager，将其设置到app.config中
    if redis_manager:
        app.config['redis_manager'] = redis_manager
    
    # 初始化员工数据库连接
    if employee_db is None:
        # 创建到员工数据库的连接
        employee_db_config = {
            'host': '************',
            'port': 3306,
            'user': 'root',
            'password': 'Yx123456.',
            'database': 'personnel_administration',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        # 创建连接函数，以便需要时创建新连接
        def get_employee_db_connection():
            return pymysql.connect(**employee_db_config)
    
    # 获取用户ID的接口
    @app.route('/api/user_id')
    def get_user_id():
        """获取当前登录用户的ID"""
        if 'user_id' in session:
            return jsonify({'user_id': session['user_id']})
        else:
            return jsonify({'error': 'User not logged in'}), 401
    
    # 辅助函数：从员工数据库获取在职员工
    def get_active_employees():
        """从员工数据库获取所有在职员工"""
        try:
            # 创建新连接
            conn = get_employee_db_connection()
            with conn.cursor() as cursor:
                # 获取在职员工信息
                cursor.execute("SELECT userid, sys00_name, sys00_position, Level_2_department_name FROM employee_roster_cut")
                employees = cursor.fetchall()
            conn.close()
            return employees
        except Exception as e:
            print(f"获取员工数据错误: {str(e)}")
            return []
    
    # 辅助函数：根据employee_id查询员工信息
    def get_employee_by_id(employee_id):
        """从员工数据库获取特定员工信息"""
        try:
            # 创建新连接
            conn = get_employee_db_connection()
            with conn.cursor() as cursor:
                # 获取特定员工信息
                cursor.execute("SELECT * FROM employee_roster_cut WHERE userid = %s", (employee_id,))
                employee = cursor.fetchone()
            conn.close()
            return employee
        except Exception as e:
            print(f"获取员工数据错误: {str(e)}")
            return None
    
    # 辅助函数：获取指定部门的所有员工ID列表
    def get_department_employee_ids(department_filter):
        """获取指定部门的所有员工ID列表"""
        department_employee_ids = []
        try:
            # 创建到员工数据库的连接
            conn = get_employee_db_connection()
            with conn.cursor() as cursor:
                # 查询指定部门的所有员工ID
                cursor.execute("SELECT userid FROM employee_roster_cut WHERE Level_2_department_name = %s", (department_filter,))
                employees = cursor.fetchall()
                department_employee_ids = [str(emp['userid']) for emp in employees if emp and 'userid' in emp]
            conn.close()
            return department_employee_ids
        except Exception as e:
            logger.info(f"获取部门员工列表失败: {str(e)}")
            return []
            
    def get_user_by_id(user_id):
        cur = mysql.connection.cursor()
        cur.execute("SELECT * FROM project_management_t_users WHERE id = %s", [user_id])
        user = cur.fetchone()
        cur.close()
        return user

    def get_user_role(user_id):
        """获取用户的全局角色"""
        user = get_user_by_id(user_id)
        return user['role'] if user else None

    def get_user_project_role(user_id, project_id):
        """获取用户在特定项目中的角色"""
        cur = mysql.connection.cursor()
        cur.execute("""
            SELECT role FROM project_management_t_user_projects 
            WHERE user_id = %s AND project_id = %s
        """, [user_id, project_id])
        result = cur.fetchone()
        cur.close()
        
        if result:
            return result['role']
        elif get_user_role(user_id) == ROLE_SUPER_ADMIN:
            return ROLE_SUPER_ADMIN
        else:
            return None

    # 暴露给其他模块使用的函数
    app.get_department_employee_ids = get_department_employee_ids

    @app.route('/projectmanagement/api/project_members')
    def project_api_members():
        """API endpoint for getting project members"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        # 获取项目ID
        project_id = request.args.get('project_id')
        if not project_id:
            return jsonify(success=False, message="缺少项目ID参数")
        
        try:
            project_id = int(project_id)
        except ValueError:
            return jsonify(success=False, message="项目ID参数无效")
        
        # 获取用户在项目中的角色
        user_role = get_user_project_role(session['user_id'], project_id)
        
        # 检查用户是否有权查看项目成员
        # 对于ID为1的主项目，所有用户都可以查看成员
        if int(project_id) != 1 and not user_role and get_user_role(session['user_id']) != ROLE_SUPER_ADMIN:
            return jsonify(success=False, message="无权查看项目成员")
        
        # 获取项目成员
        cur = mysql.connection.cursor()
        
        # 获取项目信息
        cur.execute("""
            SELECT id, name, description FROM project_management_t_projects
            WHERE id = %s
        """, [project_id])
        
        project = cur.fetchone()
        if not project:
            cur.close()
            return jsonify(success=False, message="项目不存在")
        
        # 获取所有超级管理员
        cur.execute("""
            SELECT id, name, role
            FROM project_management_t_users
            WHERE role = '超级管理员'
        """)
        
        super_admins = cur.fetchall()
        
        # 获取项目所有成员
        cur.execute("""
            SELECT u.id, u.name, u.role as user_global_role, up.role as project_role
            FROM project_management_t_user_projects up
            JOIN project_management_t_users u ON up.user_id = u.id
            WHERE up.project_id = %s
        """, [project_id])
        
        members = cur.fetchall()
        
        # 将数据库角色转换为前端角色，并添加超级管理员
        formatted_members = []
        existing_member_ids = set()
        
        # 首先处理项目成员
        for member in members:
            existing_member_ids.add(member['id'])
            
            # 检查全局角色和项目角色
            if member['user_global_role'] == ROLE_SUPER_ADMIN:
                frontend_role = 'superadmin'
                display_role = '超级管理员'
            elif member['project_role'] == ROLE_ADMIN:
                frontend_role = 'admin'
                display_role = '管理员'
            elif member['project_role'] == ROLE_NORMAL:
                frontend_role = 'member'
                display_role = '普通成员'
            elif member['project_role'] == ROLE_MARGINAL:
                frontend_role = 'marginal'
                display_role = '边缘人'
            else:
                frontend_role = 'unknown'
                display_role = member['project_role'] or '未知角色'
                
            formatted_members.append({
                'id': member['id'],
                'name': member['name'],
                'role': frontend_role,
                'display_role': display_role,
                'original_role': member['project_role'],
                'global_role': member['user_global_role']
            })
        
        # 然后添加不在项目中的超级管理员
        for admin in super_admins:
            if admin['id'] not in existing_member_ids:
                formatted_members.append({
                    'id': admin['id'],
                    'name': admin['name'],
                    'role': 'superadmin',
                    'display_role': '超级管理员',
                    'original_role': '超级管理员',
                    'global_role': '超级管理员',
                    'not_project_member': True  # 标记不是项目成员
                })
        
        cur.close()
        
        # 判断当前用户是否可以管理成员
        # 对于ID为1的主项目，所有用户都可以管理成员
        can_manage = int(project_id) == 1 or user_role == ROLE_ADMIN or get_user_role(session['user_id']) == ROLE_SUPER_ADMIN
        
        # 计算项目成员数量
        member_count = len(formatted_members) if formatted_members else 0
        
        # 添加项目信息到返回值中
        project_info = {
            'id': project['id'],
            'name': project['name'],
            'description': project['description'],
            'member_count': member_count
        }
        
        return jsonify(success=True, project=project_info, members=formatted_members, can_manage=can_manage)
    
    @app.route('/projectmanagement/api/available_users')
    def project_api_available_users():
        """API endpoint for getting available users to add to projects"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        # 获取项目ID，用于筛选已添加的成员
        project_id = request.args.get('project_id')
        if not project_id:
            return jsonify(success=False, message="缺少项目ID")
        
        try:
            # 获取当前项目已有成员的userid列表
            cur = mysql.connection.cursor()
            cur.execute("""
                    SELECT u.id
                    FROM project_management_t_user_projects up
                    JOIN project_management_t_users u ON up.user_id = u.id
                    WHERE up.project_id = %s
                """, [project_id])
            
            existing_members = cur.fetchall()
            existing_member_ids = [str(m['id']) for m in existing_members]
            
            # 从员工数据库获取员工信息
            employees = get_active_employees()
            
            # 过滤掉已经是项目成员的员工
            available_employees = []
            for emp in employees:
                # 如果员工已经是项目成员，则跳过
                if str(emp.get('userid')) in existing_member_ids:
                    continue
                
                # 转换为前端所需格式
                available_employees.append({
                    'id': emp.get('userid'),
                    'name': emp.get('sys00_name'),
                    'department': emp.get('Level_2_department_name'),
                    'position': emp.get('sys00_position'),
                    'source': 'employee_db'  # 标记数据来源为员工数据库
                })
            
            # 获取部门列表（用于前端过滤）
            departments = sorted(list(set([emp.get('Level_2_department_name') for emp in employees if emp.get('Level_2_department_name')])))
            
            cur.close()
        
            return jsonify({
                'success': True, 
                'users': available_employees,
                'departments': departments
            })
            
        except Exception as e:
            return jsonify(success=False, message=f"获取可用员工失败: {str(e)}")
    
    @app.route('/projectmanagement/api/add_project_member', methods=['POST'])
    def project_api_add_member():
        """API endpoint for adding a member to a project"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        # 获取请求数据
        data = request.json
        project_id = data.get('project_id')
        employee_id = data.get('employee_id')  # 使用员工ID
        role = data.get('role', 'member')
        
        if not project_id or not employee_id:
            return jsonify(success=False, message="缺少必要参数")
        
        # 检查调用API的用户是否是项目的创建者或管理员
        # 如果是创建者或已经是项目管理员，才允许添加成员
        user_id = session['user_id']
        cur = mysql.connection.cursor()
        
        # 先检查用户是否是项目创建者
        cur.execute("""
            SELECT role FROM project_management_t_user_projects
            WHERE user_id = %s AND project_id = %s
        """, [user_id, project_id])
        user_project = cur.fetchone()
        
        # 对于ID为1的主项目，所有用户都能添加成员
        if int(project_id) != 1 and (not user_project or user_project['role'] != '管理员'):
            user_role = get_user_role(user_id)
            if user_role != ROLE_SUPER_ADMIN:
                cur.close()
                return jsonify(success=False, message="只有项目创建者或管理员才能添加成员")
        
        try:
            # 检查员工是否已经是项目成员
            cur.execute("""
                SELECT id FROM project_management_t_user_projects
                WHERE user_id = %s AND project_id = %s
            """, [employee_id, project_id])
            
            if cur.fetchone():
                cur.close()
                return jsonify(success=False, message="该成员已在项目中")
            
            # 检查员工是否存在于用户表
            cur.execute("""
                SELECT id FROM project_management_t_users
                WHERE id = %s
            """, [employee_id])
            
            user_exists = cur.fetchone()
            
            if not user_exists:
                # 通过API获取员工信息
                employee = get_employee_by_id(employee_id)
                
                if not employee:
                    cur.close()
                    return jsonify(success=False, message="找不到指定员工")
                
                # 将员工信息添加到用户表
                cur.execute("""
                    INSERT INTO project_management_t_users (id, name, role)
                    VALUES (%s, %s, %s)
                """, [employee_id, employee.get('sys00_name'), '普通人'])
            
            # 根据传入的角色映射到数据库角色
            db_role = ROLE_ADMIN if role == 'admin' else ROLE_NORMAL if role == 'member' else ROLE_NORMAL
            
            # 添加项目成员关系
            cur.execute("""
                INSERT INTO project_management_t_user_projects (user_id, project_id, role)
                VALUES (%s, %s, %s)
            """, [employee_id, project_id, db_role])
            
            mysql.connection.commit()
            
            # 获取添加的成员信息
            employee = get_employee_by_id(employee_id)
            member_name = employee.get('sys00_name') if employee else '未知'
            
            cur.close()
            
            return jsonify({
                'success': True, 
                'message': '成员添加成功',
                'member': {
                    'id': employee_id,
                    'name': member_name,
                    'role': role,
                    'display_role': '管理员' if role == 'admin' else '普通成员'
                }
            })
            
        except Exception as e:
            return jsonify(success=False, message=f"添加成员失败: {str(e)}")
        
    @app.route('/projectmanagement/api/remove_project_member', methods=['POST'])
    def project_api_remove_member():
        """API endpoint for removing a member from a project"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        # 获取请求数据
        data = request.json
        project_id = data.get('project_id')
        employee_id = data.get('employee_id')
        
        if not project_id or not employee_id:
            return jsonify(success=False, message="缺少必要参数")
        
        # 检查用户是否有权限移除成员
        # 对于ID为1的主项目，所有用户都能移除成员
        if int(project_id) != 1:
            user_role = get_user_project_role(session['user_id'], project_id)
            if user_role != ROLE_ADMIN and get_user_role(session['user_id']) != ROLE_SUPER_ADMIN:
                return jsonify(success=False, message="无权移除项目成员")
        
        try:
            # 从员工数据库获取员工信息
            employee = get_employee_by_id(employee_id)
            if not employee:
                return jsonify(success=False, message="员工不存在或已离职")
        
            # 检查要移除的用户是否为超级管理员
            cur = mysql.connection.cursor()
            
            # 先检查用户角色
            cur.execute("""
                SELECT role FROM project_management_t_users
                WHERE id = %s
                """, [employee_id])
            
            user = cur.fetchone()
            if not user:
                cur.close()
                return jsonify(success=False, message="用户不存在")
            
            # 如果用户是超级管理员，不允许移除
            if user['role'] == '超级管理员':
                cur.close()
                return jsonify(success=False, message="不能移除超级管理员")
            
            # 检查要移除的成员是否为管理员
            cur.execute("""
                SELECT role FROM project_management_t_user_projects
                WHERE project_id = %s AND user_id = %s
                """, [project_id, employee_id])
            member = cur.fetchone()
            
            if not member:
                cur.close()
                return jsonify(success=False, message="该用户不是项目成员")
            
            # 如果当前用户不是超级管理员，且要移除的是管理员，则不允许移除
            if get_user_role(session['user_id']) != ROLE_SUPER_ADMIN and member['role'] == ROLE_ADMIN:
                cur.close()
                return jsonify(success=False, message="无权移除项目管理员")
            
            # 移除成员
            cur.execute("""
                DELETE FROM project_management_t_user_projects
                WHERE project_id = %s AND user_id = %s
                """, [project_id, employee_id])
            
            mysql.connection.commit()
            cur.close()
            
            return jsonify(success=True, message="成员移除成功")
        except Exception as e:
            return jsonify(success=False, message=f"移除成员失败: {str(e)}")
    
    @app.route('/projectmanagement/api/update_member_role', methods=['POST'])
    def project_api_update_member_role():
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        try:
            # 获取请求数据
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '无效的请求数据'}), 400
            
            project_id = data.get('project_id')
            user_id = data.get('user_id')
            new_role = data.get('role')
            
            if not all([project_id, user_id, new_role]):
                return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
            # 检查当前用户权限
            current_user_id = session['user_id']
            current_user_role = get_user_role(current_user_id)
            
            # 只有超级管理员和项目管理员可以修改角色
            # 对于ID为1的主项目，所有用户都能修改角色
            if int(project_id) != 1 and current_user_role != ROLE_SUPER_ADMIN:
                project_role = get_user_project_role(current_user_id, project_id)
                if project_role != ROLE_ADMIN:
                    return jsonify({'success': False, 'message': '没有权限修改成员角色'}), 403
            
            # 更新用户角色
            cur = mysql.connection.cursor()
            cur.execute("""
                UPDATE project_management_t_user_projects
                SET role = %s
                WHERE project_id = %s AND user_id = %s
            """, [new_role, project_id, user_id])
            
            mysql.connection.commit()
            cur.close()
            
            return jsonify({'success': True, 'message': '角色更新成功'})
            
        except Exception as e:
            if 'cur' in locals():
                cur.close()
            return jsonify({'success': False, 'message': f"更新角色失败: {str(e)}"})
    
    @app.route('/projectmanagement/api/employees')
    def project_api_employees():
        """从员工数据库获取员工信息的API接口"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        # 获取当前用户ID
        current_user_id = session.get('user_id')
        
        # 获取过滤参数
        department = request.args.get('department')
        position = request.args.get('position')
        keyword = request.args.get('keyword')
        
        try:
            # 获取员工数据
            employees = get_active_employees()
            
            # 如果没有数据，返回空列表
            if not employees:
                return jsonify(success=True, employees=[])
            
            # 处理过滤条件
            filtered_employees = []
            for emp in employees:
                # 过滤掉当前登录用户
                if str(emp.get('userid')) == str(current_user_id):
                    continue
                    
                # 部门过滤
                if department and emp.get('Level_2_department_name') != department:
                    continue
                
                # 职位过滤
                if position and emp.get('sys00_position') != position:
                    continue
                
                # 关键词过滤（姓名或ID）
                if keyword:
                    keyword = keyword.lower()
                    name_match = emp.get('sys00_name', '').lower().find(keyword) >= 0
                    id_match = str(emp.get('userid', '')).lower().find(keyword) >= 0
                    if not (name_match or id_match):
                        continue
                
                # 转换为前端所需格式
                filtered_employees.append({
                    'id': emp.get('userid'),
                    'name': emp.get('sys00_name'),
                    'department': emp.get('Level_2_department_name'),
                    'position': emp.get('sys00_position')
                })
            
            # 获取部门列表（用于前端过滤）
            departments = sorted(list(set([emp.get('Level_2_department_name') for emp in employees if emp.get('Level_2_department_name')])))
            
            # 获取职位列表（用于前端过滤）
            positions = sorted(list(set([emp.get('sys00_position') for emp in employees if emp.get('sys00_position')])))
            
            return jsonify({
                'success': True,
                'employees': filtered_employees,
                'departments': departments,
                'positions': positions,
                'total_count': len(filtered_employees)
            })
            
        except Exception as e:
            return jsonify(success=False, message=f"获取员工数据失败: {str(e)}")

    @app.route('/projectmanagement/api/departments')
    def project_api_departments():
        """获取所有部门列表的API接口"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")

        try:
            # 获取员工数据
            employees = get_active_employees()

            # 如果没有数据，返回空列表
            if not employees:
                return jsonify(success=True, departments=[])

            # 获取部门列表
            departments = []
            for emp in employees:
                dept_name = emp.get('Level_2_department_name')
                if dept_name and dept_name not in [d.get('name') for d in departments]:
                    departments.append({
                        'id': dept_name,
                        'name': dept_name
                    })

            # 按部门名称排序
            departments.sort(key=lambda x: x.get('name', ''))

            return jsonify(success=True, departments=departments)

        except Exception as e:
            app.logger.error(f"获取部门列表失败: {str(e)}")
            return jsonify(success=False, message=f"获取部门列表失败: {str(e)}")

    @app.route('/api/organization/departments')
    def api_organization_departments():
        """获取组织架构部门树的API接口"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(code=401, message="用户未登录")

        import json

        try:
            # 获取父部门ID参数
            parent_id = request.args.get('parentId')
            
            # 构建缓存键
            cache_key = f"org_departments:{parent_id if parent_id else '0'}"
            
            # 尝试从Redis缓存获取数据
            redis_client = app.config.get('redis_manager')
            if redis_client:
                cached_data = redis_client.get(cache_key)
                if cached_data:
                    logger.info(f"使用Redis缓存数据: {cache_key}")
                    result_departments = json.loads(cached_data)
                    return jsonify(code=0, message="success", data=result_departments)
            
            # 缓存未命中，从数据库获取数据
            # 创建到员工数据库的连接
            conn = get_employee_db_connection()
            with conn.cursor() as cursor:
                if parent_id is None or parent_id == '0':
                    # 查询顶级部门（没有父部门的部门）
                    cursor.execute("""
                        SELECT dept_id, name, parent_id 
                        FROM departments 
                        WHERE parent_id IS NULL OR parent_id = 0
                        ORDER BY name
                    """)
                else:
                    # 查询特定父部门下的子部门
                    cursor.execute("""
                        SELECT dept_id, name, parent_id 
                        FROM departments 
                        WHERE parent_id = %s
                        ORDER BY name
                    """, (parent_id,))
                
                departments = cursor.fetchall()
                
                # 对于每个部门，检查是否有子部门并获取总人数
                result_departments = []
                for dept in departments:
                    # 检查是否有子部门
                    cursor.execute("""
                        SELECT COUNT(*) as count
                        FROM departments
                        WHERE parent_id = %s
                    """, (dept['dept_id'],))
                    
                    has_children = cursor.fetchone()['count'] > 0
                    
                    # 获取该部门及其所有子部门的用户总数
                    # 首先获取该部门及其所有子部门的ID列表
                    dept_ids = [dept['dept_id']]
                    
                    # 递归获取所有子部门ID
                    def get_all_child_dept_ids(parent_id):
                        cursor.execute("""
                            SELECT dept_id
                            FROM departments
                            WHERE parent_id = %s
                        """, (parent_id,))
                        
                        children = cursor.fetchall()
                        for child in children:
                            child_id = child['dept_id']
                            dept_ids.append(child_id)
                            get_all_child_dept_ids(child_id)
                    
                    # 获取所有子部门ID
                    get_all_child_dept_ids(dept['dept_id'])
                    
                    # 获取这些部门下的用户总数（根据userid去重）
                    if dept_ids:
                        placeholders = ', '.join(['%s'] * len(dept_ids))
                        cursor.execute(f"""
                            SELECT COUNT(DISTINCT userid) as total_users
                            FROM dept_users
                            WHERE dept_id IN ({placeholders})
                        """, dept_ids)
                        
                        total_users = cursor.fetchone()['total_users']
                    else:
                        total_users = 0
                    
                    result_departments.append({
                        'dept_id': dept['dept_id'],
                        'name': dept['name'],
                        'parent_id': dept['parent_id'],
                        'has_children': has_children,
                        'total_users': total_users
                    })
            
            conn.close()
            
            # 将结果缓存到Redis，设置过期时间为1小时（3600秒）
            if redis_client and result_departments:
                try:
                    redis_client.set(cache_key, json.dumps(result_departments), ex=3600)
                    logger.info(f"数据已缓存到Redis: {cache_key}")
                except Exception as redis_err:
                    logger.error(f"Redis缓存错误: {str(redis_err)}")
            


            return jsonify(code=0, message="success", data=result_departments)
            
        except Exception as e:
            logger.error(f"获取部门树失败: {str(e)}")
            return jsonify(code=500, message=f"获取部门树失败: {str(e)}")
    
    @app.route('/api/organization/users')
    def api_organization_users():
        """获取部门下用户列表的API接口"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(code=401, message="用户未登录")
        
        try:
            # 获取部门ID参数
            dept_id = request.args.get('deptId')
            page = int(request.args.get('page', 1))
            size = int(request.args.get('size', 20))
            
            if not dept_id:
                return jsonify(code=400, message="缺少部门ID参数")
            
            # 计算分页偏移量
            offset = (page - 1) * size
            
            # 创建到员工数据库的连接
            conn = get_employee_db_connection()
            with conn.cursor() as cursor:
                # 查询部门下的用户总数
                cursor.execute("""
                    SELECT COUNT(*) as total
                    FROM dept_users
                    WHERE dept_id = %s
                """, (dept_id,))
                
                total = cursor.fetchone()['total']
                
                # 查询特定部门下的用户，带分页
                cursor.execute("""
                    SELECT userid, name, title, dept_id
                    FROM dept_users
                    WHERE dept_id = %s
                    ORDER BY name
                    LIMIT %s OFFSET %s
                """, (dept_id, size, offset))
                
                users = cursor.fetchall()
            
            conn.close()
            
            # 是否还有更多数据
            has_more = (offset + len(users)) < total
            
            return jsonify(code=0, message="success", data=users, hasMore=has_more, total=total)
            
        except Exception as e:
            logger.error(f"获取部门用户列表失败: {str(e)}")
            return jsonify(code=500, message=f"获取部门用户列表失败: {str(e)}")
    
    @app.route('/api/organization/search')
    def api_organization_search():
        """搜索组织架构中的用户或部门"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(code=401, message="用户未登录")
        
        try:
            # 获取搜索关键词
            keyword = request.args.get('keyword', '')
            search_type = request.args.get('type', 'all')  # 可选值: all, user, department
            
            if not keyword:
                return jsonify(code=400, message="搜索关键词不能为空")
            
            result = {
                'departments': [],
                'users': []
            }
            
            # 创建到员工数据库的连接
            conn = get_employee_db_connection()
            
            # 搜索部门
            if search_type in ['all', 'department']:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT dept_id, name, parent_id 
                        FROM departments 
                        WHERE name LIKE %s
                        ORDER BY name
                        LIMIT 20
                    """, (f"%{keyword}%",))
                    
                    departments = cursor.fetchall()
                    for dept in departments:
                        # 获取该部门及其所有子部门的用户总数
                        dept_ids = [dept['dept_id']]
                        
                        # 递归获取所有子部门ID
                        def get_all_child_dept_ids(parent_id):
                            cursor.execute("""
                                SELECT dept_id
                                FROM departments
                                WHERE parent_id = %s
                            """, (parent_id,))
                            
                            children = cursor.fetchall()
                            for child in children:
                                child_id = child['dept_id']
                                dept_ids.append(child_id)
                                get_all_child_dept_ids(child_id)
                        
                        # 获取所有子部门ID
                        get_all_child_dept_ids(dept['dept_id'])
                        
                        # 获取这些部门下的用户总数（根据userid去重）
                        if dept_ids:
                            placeholders = ', '.join(['%s'] * len(dept_ids))
                            cursor.execute(f"""
                                SELECT COUNT(DISTINCT userid) as total_users
                                FROM dept_users
                                WHERE dept_id IN ({placeholders})
                            """, dept_ids)
                            
                            total_users = cursor.fetchone()['total_users']
                        else:
                            total_users = 0
                            
                        result['departments'].append({
                            'dept_id': dept['dept_id'],
                            'name': dept['name'],
                            'parent_id': dept['parent_id'],
                            'total_users': total_users
                        })
            
            # 搜索用户
            if search_type in ['all', 'user']:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT userid, name, title, dept_id
                        FROM dept_users
                        WHERE name LIKE %s
                        ORDER BY name
                        LIMIT 20
                    """, (f"%{keyword}%",))
                    
                    users = cursor.fetchall()
                    
                    # 获取部门名称
                    if users:
                        dept_ids = list(set(user['dept_id'] for user in users))
                        if dept_ids:
                            placeholders = ', '.join(['%s'] * len(dept_ids))
                            cursor.execute(f"""
                                SELECT dept_id, name
                                FROM departments
                                WHERE dept_id IN ({placeholders})
                            """, dept_ids)
                            
                            dept_map = {dept['dept_id']: dept['name'] for dept in cursor.fetchall()}
                            
                            for user in users:
                                user_data = {
                                    'userid': user['userid'],
                                    'name': user['name'],
                                    'title': user['title'],
                                    'dept_id': user['dept_id']
                                }
                                
                                # 添加部门名称
                                if user['dept_id'] in dept_map:
                                    user_data['dept_name'] = dept_map[user['dept_id']]
                                
                                result['users'].append(user_data)
            
            conn.close()
            
            return jsonify(code=0, message="success", data=result)
            
        except Exception as e:
            logger.error(f"搜索组织架构失败: {str(e)}")
            return jsonify(code=500, message=f"搜索组织架构失败: {str(e)}")

    @app.route('/api/organization/dept_all_users')
    def api_organization_dept_all_users():
        """获取部门及其所有子部门的用户"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(code=401, message="用户未登录")
        
        try:
            # 获取部门ID参数
            dept_id = request.args.get('deptId')
            
            if not dept_id:
                return jsonify(code=400, message="缺少部门ID参数")
            
            # 创建到员工数据库的连接
            conn = get_employee_db_connection()
            
            # 获取该部门及其所有子部门的ID列表
            dept_ids = []
            
            # 递归函数获取所有子部门ID
            def get_child_depts(parent_id):
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT dept_id
                        FROM departments
                        WHERE parent_id = %s
                    """, (parent_id,))
                    
                    children = cursor.fetchall()
                    for child in children:
                        child_id = child['dept_id']
                        dept_ids.append(child_id)
                        get_child_depts(child_id)
            
            # 添加当前部门ID
            dept_ids.append(int(dept_id))
            
            # 获取所有子部门ID
            get_child_depts(int(dept_id))
            
            # 获取这些部门下的所有用户
            users = []
            if dept_ids:
                with conn.cursor() as cursor:
                    placeholders = ', '.join(['%s'] * len(dept_ids))
                    cursor.execute(f"""
                        SELECT DISTINCT userid, name, title, dept_id
                        FROM dept_users
                        WHERE dept_id IN ({placeholders})
                        ORDER BY name
                    """, dept_ids)
                    
                    users = cursor.fetchall()
            
            conn.close()
            
            # 格式化用户数据，确保用户不重复
            user_map = {}  # 使用字典确保用户不重复
            for user in users:
                user_id = user['userid']
                if user_id not in user_map:
                    user_map[user_id] = {
                        'userid': user_id,
                        'name': user['name'],
                        'title': user.get('title'),
                        'dept_id': user['dept_id']
                    }
            
            formatted_users = list(user_map.values())
            
            return jsonify(code=0, message="success", data=formatted_users)
            
        except Exception as e:
            logger.error(f"获取部门用户失败: {str(e)}")
            return jsonify(code=500, message=f"获取部门用户失败: {str(e)}")

    @app.route('/api/organization/dept_path')
    def api_organization_dept_path():
        """获取部门的完整路径（从根部门到指定部门）"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(code=401, message="用户未登录")
        
        try:
            # 获取部门ID参数
            dept_id = request.args.get('deptId')
            
            if not dept_id:
                return jsonify(code=400, message="缺少部门ID参数")
            
            # 创建到员工数据库的连接
            conn = get_employee_db_connection()
            
            # 获取部门路径
            path = []
            current_id = int(dept_id)
            
            # 递归获取父部门，直到根部门
            while current_id:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT dept_id, name, parent_id
                        FROM departments
                        WHERE dept_id = %s
                    """, (current_id,))
                    
                    dept = cursor.fetchone()
                    if not dept:
                        break
                    
                    # 添加到路径（添加到开头，因为我们是从子部门向上查找）
                    path.insert(0, {
                        'dept_id': dept['dept_id'],
                        'name': dept['name'],
                        'parent_id': dept['parent_id']
                    })
                    
                    # 更新为父部门ID
                    current_id = dept['parent_id'] if dept['parent_id'] else None
            
            conn.close()
            
            return jsonify(code=0, message="success", data=path)
            
        except Exception as e:
            logger.error(f"获取部门路径失败: {str(e)}")
            return jsonify(code=500, message=f"获取部门路径失败: {str(e)}")

    @app.route('/api/assignee_leaders')
    def api_assignee_leaders():
        """获取负责人的领导"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        try:
            # 获取负责人ID参数（可能有多个）
            assignee_ids = request.args.getlist('assignee_id')
            
            if not assignee_ids:
                return jsonify(success=False, message="缺少负责人ID参数")
            
            logger.info(f"获取负责人领导: 负责人IDs = {assignee_ids}")
            
            # 创建到员工数据库的连接
            conn = get_employee_db_connection()
            
            # 存储领导信息的集合，使用集合避免重复
            leaders_set = set()
            # 记录无法找到领导的负责人
            missing_managers = []
            # 记录同名领导情况
            duplicate_managers = []
            
            # 查询每个负责人的领导信息
            with conn.cursor() as cursor:
                for assignee_id in assignee_ids:
                    # 获取负责人姓名，用于日志记录
                    cursor.execute("SELECT name FROM dept_users WHERE userid = %s", (assignee_id,))
                    assignee_info = cursor.fetchone()
                    assignee_name = assignee_info['name'] if assignee_info else f"ID:{assignee_id}"
                    
                    # 从employee_roster_cut表获取负责人的领导名字
                    cursor.execute("""
                        SELECT er.sys00_reportManager
                        FROM employee_roster_cut er
                        WHERE er.userid = %s AND er.sys00_reportManager IS NOT NULL AND er.sys00_reportManager != ''
                    """, (assignee_id,))
                    
                    manager_result = cursor.fetchone()
                    if not manager_result or not manager_result['sys00_reportManager']:
                        missing_managers.append(assignee_name)
                        logger.info(f"负责人 {assignee_name} 在employee_roster_cut表中没有领导信息")
                        continue
                    
                    manager_name = manager_result['sys00_reportManager']
                    logger.info(f"负责人 {assignee_name} 的领导名字: {manager_name}")
                    
                    # 根据领导名字查询领导的userid
                    cursor.execute("""
                        SELECT userid, name
                        FROM dept_users
                        WHERE name = %s
                    """, (manager_name,))
                    
                    manager_results = cursor.fetchall()
                    
                    # 处理查询结果
                    if not manager_results:
                        missing_managers.append(f"{assignee_name}的领导({manager_name})")
                        logger.info(f"无法在dept_users表中找到领导 {manager_name}")
                    elif len(manager_results) > 1:
                        # 同名领导情况，选择第一个并记录
                        duplicate_managers.append(manager_name)
                        logger.warning(f"发现多个同名领导 {manager_name}，选择第一个")
                        leaders_set.add((manager_results[0]['userid'], manager_results[0]['name']))
                    else:
                        # 正常情况，只有一个匹配的领导
                        leaders_set.add((manager_results[0]['userid'], manager_results[0]['name']))
            
            conn.close()
            
            # 转换为列表格式返回
            leaders = [{'userid': userid, 'name': name} for userid, name in leaders_set]
            
            # 记录结果
            logger.info(f"找到 {len(leaders)} 个领导: {[leader['name'] for leader in leaders]}")
            if missing_managers:
                logger.warning(f"以下负责人没有找到领导: {missing_managers}")
            if duplicate_managers:
                logger.warning(f"以下领导存在同名情况: {duplicate_managers}")
            
            return jsonify(success=True, leaders=leaders)
            
        except Exception as e:
            logger.error(f"获取负责人领导失败: {str(e)}")
            return jsonify(success=False, message=f"获取负责人领导失败: {str(e)}")

    @app.route('/api/task_cc_users/<int:task_id>', methods=['POST'])
    def api_save_task_cc_users(task_id):
        """保存任务的抄送人列表"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        try:
            # 获取请求数据
            data = request.json
            cc_users = data.get('cc_users', [])
            
            # 开始数据库事务
            cur = mysql.connection.cursor()
            
            # 先删除该任务的所有抄送人
            cur.execute("""
                DELETE FROM project_management_t_task_cc_users
                WHERE task_id = %s
            """, [task_id])
            
            # 添加新的抄送人
            if cc_users:
                for user in cc_users:
                    user_id = user.get('userid')
                    if user_id:
                        cur.execute("""
                            INSERT INTO project_management_t_task_cc_users (task_id, user_id)
                            VALUES (%s, %s)
                        """, [task_id, user_id])
            
            # 提交事务
            mysql.connection.commit()
            cur.close()
            
            return jsonify(success=True, message="抄送人保存成功")
            
        except Exception as e:
            logger.error(f"保存任务抄送人失败: {str(e)}")
            return jsonify(success=False, message=f"保存任务抄送人失败: {str(e)}")

    @app.route('/projectmanagement/api/task_cc_users/<int:task_id>')
    def api_task_cc_users(task_id):
        """获取任务的抄送人列表"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify(success=False, message="用户未登录")
        
        try:
            # 查询任务抄送人
            cur = mysql.connection.cursor()
            cur.execute("""
                SELECT cc.user_id, u.name
                FROM project_management_t_task_cc_users cc
                JOIN project_management_t_users u ON cc.user_id = u.id
                WHERE cc.task_id = %s
            """, [task_id])
            
            cc_users = cur.fetchall()
            cur.close()
            
            # 格式化返回数据
            formatted_cc_users = []
            for user in cc_users:
                formatted_cc_users.append({
                    'userid': user['user_id'],
                    'name': user['name']
                })
            
            return jsonify(success=True, cc_users=formatted_cc_users)
            
        except Exception as e:
            logger.error(f"获取任务抄送人失败: {str(e)}")
            return jsonify(success=False, message=f"获取任务抄送人失败: {str(e)}")

    return app 