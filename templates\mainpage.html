{% extends "base_layout.html" %}

{% block title %}万象中台{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ cdn_static('css/mainpage_base.css') }}">
<link rel="stylesheet" href="{{ cdn_static('css/dingtalk_detail.css') }}">
<link rel="stylesheet" href="{{ cdn_static('css/page_animations.css') }}">
<!-- 引入Wang富文本编辑器样式 -->
<link rel="stylesheet" href="{{ cdn_static('css/wangeditor.css') }}">
<!-- 引入移动端适配样式 -->
<link rel="stylesheet" href="{{ cdn_static('css/mainpage_mobile.css') }}">
<style>
/* 富文本编辑器样式 */
.rich-editor-toolbar {
    border: 1px solid #dcdfe6;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
}

.rich-editor-content {
    min-height: 150px;
    border: 1px solid #dcdfe6;
    border-radius: 0 0 4px 4px;
    padding: 8px;
}

.rich-editor-container {
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.w-e-bar {
    z-index: 2600 !important;
}

.w-e-text-container {
    z-index: 2500 !important;
}

.w-e-drop-panel {
    z-index: 2550 !important;
}

.w-e-modal {
    z-index: 2650 !important;
}

.w-e-text img {
    max-width: 100%;
    height: auto !important;
}

/* 错误样式 */
.rich-editor-content.error {
    border-color: #f56c6c;
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

.completion-error {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}
</style>
{#{% if enable_difys %}
<style>
  #dify-chatbot-bubble-button {
    background-color: #1C64F2 !important;
  }
  #dify-chatbot-bubble-window {
    width: 24rem !important;
    height: 40rem !important;
  }
</style>
{% endif %}#}
{% endblock %}

{% block content %}
<div class="top-info animate-in">
    <div class="current-date" id="current-date"></div>
    <div class="greeting" id="greeting"></div>
</div>

<div class="task-stats-container animate-in">
    <div class="task-stats">
        <div class="stat-item">
            <div class="stat-number animate" id="unfinished-count">1</div>
            <div class="stat-text">未完成</div>
        </div>
        <div class="stat-item">
            <div class="stat-number animate" id="finished-count">0</div>
            <div class="stat-text">已完成</div>
        </div>
        <div class="stat-item">
            <div class="stat-number animate" id="due-this-month-count">0</div>
            <div class="stat-text">本月到期</div>
        </div>
        <div class="stat-item overdue-item">
            <div class="stat-number animate" id="overdue-count">1</div>
            <div class="stat-text">已逾期</div>
        </div>
    </div>
    
    <!-- 表情包容器 -->
    <div class="emoticon-container-absolute animate-in" id="random-emoticon"></div>
</div>

<div class="panels-container">
    <!-- 个人待办面板 -->
    <div class="info-panel animate-in">
        <div class="panel-header animate-in">
            <div class="panel-title">
                <i class="fas fa-tasks"></i>
                个人待办事项
            </div>
            <div class="panel-actions">
                <select class="status-filter" onchange="handleStatusChange(event)">
                    <option value="unfinished">未完成</option>
                    <option value="已完成">已完成</option>
                    <option value="all">全部</option>
{#                    <option value="待处理">待处理</option>#}
{#                    <option value="处理中">处理中</option>#}

                </select>
            </div>
        </div>
        <div class="panel-content">
            <!-- 待办内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 中台公告面板 -->
    <div class="info-panel animate-in">
        <div class="panel-header animate-in">
            <div class="panel-title">
                <i class="fas fa-bullhorn"></i>
                中台系统公告
            </div>
            <div class="panel-actions">
                <button class="refresh-btn" onclick="loadAnnouncements()">
                    <i class="fas fa-sync"></i>
                </button>
            </div>
        </div>
        <div class="panel-content" id="announcements-container">
            <!-- 公告内容将通过 JavaScript 动态加载 -->
        </div>
    </div>

    <!-- 项目开发公告面板 -->
    <div class="info-panel animate-in">
        <div class="panel-header animate-in">
            <div class="panel-title">
                <i class="fas fa-code-branch"></i>
                项目开发动态
            </div>
            <div class="panel-actions">
                <button class="search-toggle-btn" id="project-search-toggle" onclick="toggleProjectSearch()">
                    <i class="fas fa-search"></i>
                </button>
                <button class="refresh-btn" onclick="loadProjectUpdates()">
                    <i class="fas fa-sync"></i>
                </button>
            </div>
        </div>
        
        <!-- 搜索和过滤区域 - 更紧凑的布局 -->
        <div class="search-filters-container" id="project-search-container" style="display: none;">
            <div class="search-panel-header">
                <span class="search-panel-title">项目筛选</span>
                <button class="search-panel-close" onclick="toggleProjectSearch()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="compact-search-layout">
                <!-- 第一行：搜索和分类 -->
                <div class="search-top-row">
                    <div class="search-input-group">
                        <input type="text" id="project-search-input" placeholder="搜索项目名称或描述..." class="search-input">
                    </div>
                    <div class="filter-select-group">
                        <select id="project-category-filter" class="filter-select">
                            <option value="">所有分类</option>
                            <!-- 分类选项将通过JavaScript动态填充 -->
                        </select>
                    </div>
                </div>
                
                <!-- 第二行：日期范围和按钮 -->
                <div class="search-bottom-row">
                    <div class="date-range-compact">
                        <input type="date" id="project-start-date" class="date-input" placeholder="开始日期">
                        <span class="date-separator">至</span>
                        <input type="date" id="project-end-date" class="date-input" placeholder="结束日期">
                    </div>
                    <div class="filter-buttons-group">
                        <button class="clear-filter-btn" onclick="clearProjectFilters()">
                            <i class="fas fa-times"></i> 清除
                        </button>
                        <button class="apply-filter-btn" onclick="applyProjectFilters()">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="panel-content" id="project-updates-container">
            <!-- 项目开发动态内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 信息反馈面板 -->
    <div class="info-panel animate-in">
        <div class="panel-header animate-in">
            <div class="panel-title">
                <i class="fas fa-comment-alt"></i>
                我的反馈
            </div>
            <div class="panel-actions">
                <button class="add-feedback-btn" onclick="showFeedbackModal()">
                    <i class="fas fa-plus"></i>
                    提交新反馈
                </button>
            </div>
        </div>
        <div class="panel-content">
            <div class="feedback-list" id="feedback-list">
                <!-- 反馈列表将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 公告详情弹窗 -->
<div class="announcement-detail-dialog" id="announcement-detail">
    <div class="announcement-detail-container">
        <div class="announcement-detail-header">
            <h3 class="announcement-detail-title" id="detail-title"></h3>
            <div class="announcement-detail-meta">
                <span id="detail-time"></span>
                <span class="tag" id="detail-tag"></span>
            </div>
            <button class="close-btn" onclick="closeAnnouncementDetail()"></button>
        </div>
        <div class="announcement-detail-body" id="detail-content"></div>
    </div>
</div>

<!-- 图片查看器 -->
<div class="image-viewer" id="image-viewer">
    <div class="image-viewer-container">
        <img id="viewer-image" src="" alt="预览图片">
        <button class="image-viewer-close-btn" onclick="closeImageViewer()"></button>
    </div>
</div>

<!-- 添加反馈弹窗 -->
<div class="modal" id="feedback-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>提交新反馈</h3>
            <button class="close-btn" onclick="closeFeedbackModal()"></button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="request-name">请求名称</label>
                <input type="text" id="request-name" placeholder="请输入请求名称" maxlength="100">
            </div>
            <div class="form-group">
                <label for="description">详细描述</label>
                <textarea id="description" placeholder="请详细描述您的需求或问题" rows="4" maxlength="1000"></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button class="cancel-btn" onclick="closeFeedbackModal()">取消</button>
            <button class="submit-btn" onclick="submitFeedback()">提交</button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 从服务器获取的表情包列表
        const emoticons = [{% for emoticon in emoticons %}'{{ emoticon }}'{% if not loop.last %}, {% endif %}{% endfor %}];
        
        // 随机显示表情包
        function loadRandomEmoticon() {
            if (emoticons && emoticons.length > 0) {
                // 随机选择一个表情包
                const randomIndex = Math.floor(Math.random() * emoticons.length);
                const randomEmoticon = emoticons[randomIndex];
                
                // 显示表情包
                const emoticonContainer = document.getElementById('random-emoticon');
                emoticonContainer.innerHTML = `<img src="/cdn/images/emoticon/${randomEmoticon}" alt="表情包">`;
            }
        }
        
        // 初始化表情包
        loadRandomEmoticon();

        // 添加数字动画效果
        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(number => {
                const targetValue = parseInt(number.textContent);
                const duration = 1500; // 动画持续时间（毫秒）
                const steps = 20; // 动画步数
                const stepDuration = duration / steps;
                
                // 如果值为 0，无需动画
                if (targetValue === 0) {
                    number.classList.add('animate');
                    return;
                }
                
                let currentValue = 0;
                const increment = targetValue / steps;
                
                // 开始动画
                let counter = 0;
                number.textContent = '0';
                
                const interval = setInterval(() => {
                    counter++;
                    currentValue += increment;
                    
                    // 确保最后一步显示正确的目标值
                    if (counter >= steps) {
                        number.textContent = targetValue;
                        clearInterval(interval);
                    } else {
                        number.textContent = Math.round(currentValue);
                    }
                }, stepDuration);
                
                number.classList.add('animate');
            });
        }
        
        // 在页面加载后执行数字动画
        setTimeout(animateNumbers, 500);

        // 为动态加载的内容添加动画
        function addAnimationToLoadedContent() {
            // 监听 AJAX 内容加载
            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    if (mutation.addedNodes.length) {
                        // 为新添加的元素添加动画类
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === 1) { // 确保是元素节点
                                if (node.classList.contains('announcement-item') ||
                                    node.classList.contains('todo-item') ||
                                    node.classList.contains('project-update-item') ||
                                    node.classList.contains('feedback-item')) {
                                    
                                    // 添加淡入动画
                                    node.style.opacity = '0';
                                    node.style.transform = 'translateY(20px)';
                                    
                                    setTimeout(() => {
                                        node.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                                        node.style.opacity = '1';
                                        node.style.transform = 'translateY(0)';
                                    }, 50);
                                }
                            }
                        });
                    }
                });
            });
            
            // 开始观察容器中的变化
            const containers = [
                document.getElementById('announcements-container'),
                document.querySelector('.panel-content'), // 待办内容容器
                document.getElementById('project-updates-container'),
                document.getElementById('feedback-list')
            ];
            
            containers.forEach(container => {
                if (container) {
                    observer.observe(container, { childList: true });
                }
            });
        }
        
        // 在页面加载后设置监听器
        setTimeout(addAnimationToLoadedContent, 1000);

        // 更新反馈按钮事件绑定
        document.querySelector('.add-feedback-btn').addEventListener('click', function() {
            // TODO: 实现打开反馈表单的逻辑
        });

        // 加载公告
        loadAnnouncements();

        // 更新日期和问候语函数
        function updateDateTime() {
            const now = new Date();
            
            // 更新日期显示
            const dateOptions = { year: 'numeric', month: 'long', day: 'numeric' };
            const dateStr = now.toLocaleDateString('zh-CN', dateOptions);
            document.getElementById('current-date').textContent = dateStr;
            
            // 获取小时，用于确定问候语
            const hour = now.getHours();
            let greeting = '';
            if (hour >= 5 && hour < 12) {
                greeting = '上午好';
            } else if (hour >= 12 && hour < 14) {
                greeting = '中午好';
            } else if (hour >= 14 && hour < 18) {
                greeting = '下午好';
            } else {
                greeting = '晚上好';
            }
            
            // 获取用户昵称并更新问候语
            const userInfo = {{ user_info|tojson }};
            const username = userInfo ? userInfo.nick : '访客';
            document.getElementById('greeting').textContent = `${greeting}，${username}`;
        }
        
        // 只在页面加载时调用一次
        updateDateTime();
    });

    // 格式化日期时间
    function formatDateTime(dateStr) {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).replace(/\//g, '-');
    }

    // 获取公告类型对应的标签样式
    function getAnnouncementTagType(type) {
        const typeMap = {
            'notice': 'info',
            'update': 'success',
            'maintenance': 'warning',
            'guide': 'primary'
        };
        return typeMap[type] || 'info';
    }

    // 获取公告类型的中文名称
    function getAnnouncementTypeName(type) {
        const typeMap = {
            'notice': '通知',
            'update': '更新',
            'maintenance': '维护',
            'guide': '指南'
        };
        return typeMap[type] || '通知';
    }

    // 加载公告列表
    function loadAnnouncements() {
        fetch('/api/announcements?page=1&page_size=5')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('announcements-container');
                if (!container) return;

                if (data.announcements && data.announcements.length > 0) {
                    const announcementsHtml = data.announcements.map(announcement => `
                        <div class="announcement-item">
                            <i class="fas fa-info-circle announcement-icon"></i>
                            <div class="announcement-content">
                                <div class="announcement-title">${announcement.title}</div>
                                <div class="announcement-meta">
                                    <span>${formatDateTime(announcement.created_at)}</span>
                                    <span class="tag tag-${getAnnouncementTagType(announcement.type)}">
                                        ${getAnnouncementTypeName(announcement.type)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    `).join('');
                    
                    container.innerHTML = announcementsHtml;
                } else {
                    container.innerHTML = '<div class="no-data">暂无公告</div>';
                }
            })
            .catch(error => {
                console.error('加载公告失败:', error);
                const container = document.getElementById('announcements-container');
                if (container) {
                    container.innerHTML = '<div class="error-message">加载失败，请稍后重试</div>';
                }
            });
    }
</script>
{% endblock %}

{% block extra_js %}
    <!-- 定义emoticons变量 -->
    <script>    window.emoticons = [{% for emoticon in emoticons %}'{{ emoticon }}'{% if not loop.last %}, {% endif %}{% endfor %}];
    </script>
    <!-- 引入IndexedDB存储支持 -->
    <script src="{{ url_for('static', filename='js/auth_storage.js') }}"></script>
    <script>
    // 同步服务器的token到IndexedDB
    async function syncAuthToken() {
        try {
            const currentToken = "{{ session.get('token', '') }}";
            if (currentToken) {
                // 如果服务器有token，则保存到IndexedDB
                await window.authStorage.saveAuthToken(currentToken);
                console.log('已同步登录信息到IndexedDB');
            }
        } catch (error) {
            console.error('同步登录信息失败:', error);
        }
    }
    
    // 页面加载时同步token
    document.addEventListener('DOMContentLoaded', function() {
        syncAuthToken();
    });
    </script>
    <!-- 引入钉钉API JS -->
    <script src="{{ cdn_static('js/dingtalk_api.js') }}"></script>
    <!-- 引入富文本编辑器 -->
    <script src="{{ cdn_static('js/axios.min.js') }}"></script>
    <script src="{{ cdn_static('js/wangeditor.js') }}"></script>
    <!-- 引入主页面JS -->
    <script src="{{ cdn_static('js/mainpage_base.js') }}"></script>
    {#{% if enable_difys %}<script> window.difyChatbotConfig = {  token: 'NB8K2prfZQCvhszR',  baseUrl: 'https://difys.youxiangjt.com',  systemVariables: {    // user_id: 'YOU CAN DEFINE USER ID HERE',  }, }</script><script src="https://difys.youxiangjt.com/embed.min.js" id="NB8K2prfZQCvhszR" defer></script>{% endif %}#}

{% endblock %}