/**
 * 项目管理移动端"我的关注"视图适配脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否为移动设备
    const isMobile = window.innerWidth <= 1023;
    
    if (isMobile) {
        // 初始化
        initMobileReceivedView();
        
        // 监听视图变化
        watchViewChanges();
    }
    
    /**
     * 初始化移动端"我的关注"视图
     */
    function initMobileReceivedView() {
        // 立即显示加载状态提示
        showMobileLoadingHint();

        // 等待Vue完成渲染
        setTimeout(() => {
            adaptReceivedTableForMobile();
            removeTableScrollbars();
        }, 1000);
    }

    /**
     * 显示移动端加载提示
     */
    function showMobileLoadingHint() {
        // 检查是否在"我的关注"视图
        const currentView = window.pmUtils?.currentView?.value;
        if (currentView !== 'received') return;

        // 查找移动端表格容器
        const mobileContainer = document.querySelector('.mobile-task-table-container.is-mobile');
        if (!mobileContainer) return;

        // 如果已经有加载提示，不重复添加
        if (mobileContainer.querySelector('.mobile-loading-hint')) return;

        // 创建加载提示元素
        const loadingHint = document.createElement('div');
        loadingHint.className = 'mobile-loading-hint';
        loadingHint.innerHTML = `
            <div class="loading-hint-content">
                <div class="loading-hint-icon">
                    <svg class="loading-spinner" viewBox="0 0 24 24" width="24" height="24">
                        <circle cx="12" cy="12" r="10" stroke="#409eff" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                            <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                            <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </svg>
                    </div>
                    <div class="loading-hint-text">正在加载任务数据，请稍候...</div>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .mobile-loading-hint {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 100;
                min-height: 200px;
            }

            .loading-hint-content {
                text-align: center;
                padding: 20px;
            }

            .loading-hint-icon {
                margin-bottom: 12px;
            }

            .loading-spinner {
                color: #409eff;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .loading-hint-text {
                font-size: 14px;
                color: #606266;
                font-weight: 500;
            }
        `;

        // 添加样式到头部
        if (!document.querySelector('#mobile-loading-hint-style')) {
            style.id = 'mobile-loading-hint-style';
            document.head.appendChild(style);
        }

        // 设置容器为相对定位
        mobileContainer.style.position = 'relative';

        // 添加加载提示到容器
        mobileContainer.appendChild(loadingHint);

        // 5秒后自动移除提示（防止卡住）
        setTimeout(() => {
            removeMobileLoadingHint();
        }, 5000);

        console.log('已显示移动端加载提示');
    }

    /**
     * 移除移动端加载提示
     */
    function removeMobileLoadingHint() {
        const loadingHints = document.querySelectorAll('.mobile-loading-hint');
        loadingHints.forEach(hint => {
            hint.remove();
        });
    }

    // 暴露工具函数给全局使用
    window.MobileReceivedUtils = {
        showLoadingHint: showMobileLoadingHint,
        removeLoadingHint: removeMobileLoadingHint
    };
    
    /**
     * 监听视图变化，确保在切换到"我的关注"视图时应用表格适配
     */
    function watchViewChanges() {
        // 监听视图切换事件
        document.addEventListener('click', function(e) {
            // 检查是否点击了"我的关注"导航项
            const navItem = e.target.closest('[data-view="received"]');
            if (navItem) {
                // 立即显示加载提示
                setTimeout(() => {
                    showMobileLoadingHint();
                }, 100);
            }
        });

        // 使用MutationObserver监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    // 检查是否有表格被添加或修改
                    const tables = document.querySelectorAll('.project-main-area .el-table');
                    if (tables.length > 0) {
                        // 移除加载提示
                        removeMobileLoadingHint();
                        adaptReceivedTableForMobile();
                        removeTableScrollbars();
                    }
                }
            });
        });
        
        // 监听主内容区域的变化
        const mainArea = document.querySelector('.project-main-area');
        if (mainArea) {
            observer.observe(mainArea, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class']
            });
        }
        
        // 监听Vue路由变化（如果是SPA应用）
        window.addEventListener('hashchange', function() {
            setTimeout(() => {
                adaptReceivedTableForMobile();
                removeTableScrollbars();
            }, 500);
        });

        // 监听分页变化
        document.addEventListener('click', function(e) {
            if (e.target.closest('.el-pagination')) {
                setTimeout(() => {
                    adaptReceivedTableForMobile();
                    removeTableScrollbars();
                }, 500);
            }
        });

        // 监听Vue的tableLoading状态变化
        if (window.pmUtils && window.pmUtils.tableLoading) {
            // 创建一个观察者来监听tableLoading的变化
            let lastLoadingState = false;

            const checkLoadingState = () => {
                const currentView = window.pmUtils.currentView?.value;
                const isLoading = window.pmUtils.tableLoading?.value;

                if (currentView === 'received' && isLoading !== lastLoadingState) {
                    if (isLoading) {
                        showMobileLoadingHint();
                    } else {
                        removeMobileLoadingHint();
                    }
                    lastLoadingState = isLoading;
                }
            };

            // 定期检查加载状态
            setInterval(checkLoadingState, 200);
        }
    }
    
    /**
     * 移除表格内部滚动条
     */
    function removeTableScrollbars() {
        // 获取移动版表格相关元素
        const tableElements = document.querySelectorAll('.is-mobile .el-table, .is-mobile .el-table__body-wrapper, .is-mobile .el-table__inner-wrapper');
        
        tableElements.forEach(function(element) {
            // 移除可能导致滚动条的样式
            element.style.maxHeight = 'none';
            element.style.height = 'auto';
            element.style.overflow = 'visible';
            
            // 移除可能导致滚动的类
            element.classList.remove('el-table--scrollable-x');
            element.classList.remove('el-table--scrollable-y');
        });
        
        // 确保表格容器也不会出现滚动条
        const tableContainers = document.querySelectorAll('.mobile-task-table-container');
        tableContainers.forEach(function(container) {
            container.style.overflow = 'visible';
        });
        
        console.log('已移除移动版表格内部滚动条');
    }
    
    /**
     * 为"我的关注"视图表格添加移动端适配
     */
    function adaptReceivedTableForMobile() {
        // 获取主区域内的所有表格
        const tables = document.querySelectorAll('.project-main-area .el-table');

        tables.forEach(function(table) {
            // 检查表格是否已经处理过
            if (table.getAttribute('data-mobile-adapted') === 'true') {
                return;
            }

            // 获取表头
            const headers = table.querySelectorAll('th.el-table__cell');
            const headerTexts = [];

            // 收集表头文本
            headers.forEach(function(header) {
                const headerText = header.textContent.trim();
                headerTexts.push(headerText);
            });

            // 为每个单元格添加data-label属性
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(function(row) {
                const cells = row.querySelectorAll('td.el-table__cell');

                cells.forEach(function(cell, index) {
                    if (index < headerTexts.length) {
                        cell.setAttribute('data-label', headerTexts[index]);
                    }
                });
            });

            // 应用任务名称文本省略处理
            applyTaskNameEllipsis(table);

            // 标记表格已经处理过
            table.setAttribute('data-mobile-adapted', 'true');

            console.log('已为"我的关注"视图表格应用移动端适配');
        });
    }

    /**
     * 应用任务名称文本省略处理
     */
    function applyTaskNameEllipsis(table) {
        // 获取所有任务名称元素
        const taskNameElements = table.querySelectorAll('.task-name-text, .overdue-name, .completed-name');

        taskNameElements.forEach(function(element) {
            // 确保元素有正确的样式类
            if (!element.classList.contains('mobile-text-ellipsis')) {
                element.classList.add('mobile-text-ellipsis');
            }

            // 为长文本添加title属性，方便用户查看完整内容
            const text = element.textContent.trim();
            if (text.length > 12) { // 进一步降低阈值，12个字符就显示title和省略号
                element.setAttribute('title', text);
            }

            // 应用更严格的宽度限制，让省略号更早出现
            element.style.maxWidth = 'calc(100vw - 160px)';
            element.style.whiteSpace = 'nowrap';
            element.style.overflow = 'hidden';
            element.style.textOverflow = 'ellipsis';
            element.style.display = 'inline-block';
            element.style.verticalAlign = 'middle';
        });

        // 优化移动端任务名称区域布局
        const mobileTaskNames = table.querySelectorAll('.mobile-task-name');
        mobileTaskNames.forEach(function(taskName) {
            // 确保任务名称容器有正确的flex布局
            taskName.style.display = 'flex';
            taskName.style.alignItems = 'center';
            taskName.style.gap = '8px';
            taskName.style.width = '100%';
            taskName.style.overflow = 'hidden';

            // 优化复选框样式
            const checkbox = taskName.querySelector('.task-checkbox');
            if (checkbox) {
                checkbox.style.flexShrink = '0';
                checkbox.style.marginRight = '0';
            }

            // 优化任务名称文本样式
            const taskNameText = taskName.querySelector('.task-name-text, .completed-name, .overdue-name');
            if (taskNameText) {
                taskNameText.style.flex = '1';
                taskNameText.style.minWidth = '0';
                taskNameText.style.display = 'inline-block';
                taskNameText.style.whiteSpace = 'nowrap';
                taskNameText.style.overflow = 'hidden';
                taskNameText.style.textOverflow = 'ellipsis';
                taskNameText.style.verticalAlign = 'middle';
            }
        });

        // 处理移动端卡片内的任务名称容器
        const mobileTaskContents = table.querySelectorAll('.mobile-task-content');
        mobileTaskContents.forEach(function(content) {
            const taskNameContainer = content.querySelector('div:first-child');
            if (taskNameContainer) {
                taskNameContainer.style.overflow = 'hidden';
                taskNameContainer.style.width = '100%';
            }
        });

        // 优化移动端任务信息图标对齐
        const mobileTaskInfos = table.querySelectorAll('.mobile-task-info');
        mobileTaskInfos.forEach(function(info) {
            // 确保任务信息容器有正确的样式
            info.style.display = 'flex';
            info.style.flexWrap = 'wrap';
            info.style.gap = '15px';
            info.style.alignItems = 'center';

            // 优化图标样式
            const icons = info.querySelectorAll('.el-icon');
            icons.forEach(function(icon) {
                icon.style.fontSize = '14px';
                icon.style.lineHeight = '1';
                icon.style.verticalAlign = 'middle';
                icon.style.display = 'inline-flex';
                icon.style.alignItems = 'center';
                icon.style.justifyContent = 'center';
                icon.style.flexShrink = '0';
                icon.style.width = '14px';
                icon.style.height = '14px';
            });
        });
    }
    
    // 处理表格内部的操作按钮点击事件
    document.addEventListener('click', function(e) {
        // 检查是否点击了操作按钮
        const actionButton = e.target.closest('.el-button');
        if (actionButton && actionButton.closest('.project-main-area .el-table')) {
            // 标记表格需要重新适配
            const table = actionButton.closest('.el-table');
            if (table) {
                table.setAttribute('data-mobile-adapted', 'false');
                // 延迟执行适配，等待表格内容更新
                setTimeout(() => {
                    adaptReceivedTableForMobile();
                    removeTableScrollbars();
                }, 500);
            }
        }
    });
}); 