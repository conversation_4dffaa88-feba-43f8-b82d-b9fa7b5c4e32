document.addEventListener('DOMContentLoaded', function() {
    // 初始化表情包
    loadRandomEmoticon();

    // 更新反馈按钮事件绑定
    document.querySelector('.add-feedback-btn')?.addEventListener('click', function() {
        // TODO: 实现打开反馈表单的逻辑
    });

    // 加载公告
    loadAnnouncements();
    
    // 加载项目更新
    loadProjectUpdates();

    // 加载反馈列表
    loadFeedbackList();

    // 加载待办事项
    loadTodoItems();
    
    // 更新待办事项统计数据
    updateTodoStats();
});

// 随机显示表情包
function loadRandomEmoticon() {
    // emoticons变量将通过window.emoticons在HTML中定义
    if (window.emoticons && window.emoticons.length > 0) {
        // 随机选择一个表情包
        const randomIndex = Math.floor(Math.random() * window.emoticons.length);
        const randomEmoticon = window.emoticons[randomIndex];
        
        // 显示表情包
        const emoticonContainer = document.getElementById('random-emoticon');
        if (emoticonContainer) {
            emoticonContainer.innerHTML = `<img src="/static/images/emoticon/${randomEmoticon}" alt="表情包">`;
        }
    }
}

// 格式化日期时间
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).replace(/\//g, '-');
}

// 获取公告类型对应的标签样式
function getAnnouncementTagType(type) {
    const typeMap = {
        'notice': 'info',
        'update': 'success',
        'maintenance': 'warning',
        'guide': 'primary'
    };
    return typeMap[type] || 'info';
}

// 获取公告类型的中文名称
function getAnnouncementTypeName(type) {
    const typeMap = {
        'notice': '通知',
        'update': '更新',
        'maintenance': '维护',
        'guide': '指南'
    };
    return typeMap[type] || '通知';
}

let currentPage = 1;
const PAGE_SIZE = 3;

// 修改加载公告列表函数
function loadAnnouncements(page = 1) {
    fetch(`/api/mainpage/announcements?page=${page}&page_size=3`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('announcements-container');
            if (!container) return;

            if (data.announcements && data.announcements.length > 0) {
                const announcementsHtml = data.announcements.map(announcement => `
                    <div class="announcement-item" onclick="showAnnouncementDetail(${JSON.stringify(announcement).replace(/"/g, '&quot;')})">
                        <i class="fas fa-info-circle announcement-icon"></i>
                        <div class="announcement-content">
                            <div class="announcement-title">${announcement.title}</div>
                            <div class="announcement-meta">
                                <span>${formatDateTime(announcement.created_at)}</span>
                                <span class="tag tag-${getAnnouncementTagType(announcement.type)}">
                                    ${getAnnouncementTypeName(announcement.type)}
                                </span>
                            </div>
                        </div>
                    </div>
                `).join('');
                
                // 添加分页控件
                const paginationHtml = `
                    <div class="pagination-controls">
                        <button onclick="loadAnnouncements(${Math.max(1, data.pagination.current_page - 1)})" 
                                class="page-btn"
                                ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 ${data.pagination.current_page} / ${data.pagination.total_pages || 1} 页</span>
                        <button onclick="loadAnnouncements(${Math.min(data.pagination.total_pages || 1, data.pagination.current_page + 1)})" 
                                class="page-btn"
                                ${!data.pagination.has_next ? 'disabled' : ''}>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;

                container.innerHTML = announcementsHtml + paginationHtml;
            } else {
                // 即使没有数据也显示分页控件
                container.innerHTML = `
                    <div class="no-data">暂无公告</div>
                    <div class="pagination-controls">
                        <button class="page-btn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 1 / 1 页</span>
                        <button class="page-btn" disabled>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载公告失败:', error);
            const container = document.getElementById('announcements-container');
            if (container) {
                container.innerHTML = '<div class="error-message">加载失败，请稍后重试</div>';
            }
        });
}

// 显示公告详情
function showAnnouncementDetail(announcement) {
    const detailDialog = document.getElementById('announcement-detail');
    const detailTitle = document.getElementById('detail-title');
    const detailTime = document.getElementById('detail-time');
    const detailTag = document.getElementById('detail-tag');
    const detailContent = document.getElementById('detail-content');
    
    if (!detailDialog || !detailTitle || !detailTime || !detailTag || !detailContent) return;
    
    // 填充内容
    detailTitle.textContent = announcement.title;
    detailTime.textContent = formatDateTime(announcement.created_at);
    detailTag.textContent = getAnnouncementTypeName(announcement.type);
    detailTag.className = `tag tag-${getAnnouncementTagType(announcement.type)}`;
    detailContent.innerHTML = announcement.content;
    
    // 为内容中的所有图片添加点击事件
    const images = detailContent.querySelectorAll('img');
    images.forEach(img => {
        img.onclick = function() {
            showImageViewer(this.src);
        };
    });
    
    // 显示弹窗
    detailDialog.style.display = 'flex';
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
}

// 关闭公告详情
function closeAnnouncementDetail() {
    const detailDialog = document.getElementById('announcement-detail');
    if (detailDialog) {
        detailDialog.style.display = 'none';
        // 移除所有特殊类
        detailDialog.classList.remove('feedback-detail-dialog');
        detailDialog.classList.remove('todo-detail-dialog');
    }
    
    // 恢复背景滚动
    document.body.style.overflow = '';
}

// 图片查看器相关变量
let currentScale = 1;
const MIN_SCALE = 0.1;
const MAX_SCALE = 5;
const SCALE_STEP = 0.1;
const DOUBLE_TAP_ZOOM = 2;

// 触摸和鼠标相关变量
let initialDistance = 0;
let initialScale = 1;
let lastTapTime = 0;
let isDragging = false;
let startX = 0;
let startY = 0;
let translateX = 0;
let translateY = 0;
let lastTranslateX = 0;
let lastTranslateY = 0;
let isMouseDown = false;

// 显示图片查看器
function showImageViewer(src) {
    const viewer = document.getElementById('image-viewer');
    const image = document.getElementById('viewer-image');
    image.src = src;
    viewer.style.display = 'flex';
    
    // 重置所有变量
    resetImageViewer();
    
    // 添加事件监听
    viewer.addEventListener('wheel', handleImageWheel);
    viewer.addEventListener('touchstart', handleTouchStart, { passive: false });
    viewer.addEventListener('touchmove', handleTouchMove, { passive: false });
    viewer.addEventListener('touchend', handleTouchEnd);
    
    // 添加鼠标事件监听
    viewer.addEventListener('mousedown', handleMouseDown);
    viewer.addEventListener('mousemove', handleMouseMove);
    viewer.addEventListener('mouseup', handleMouseUp);
    viewer.addEventListener('mouseleave', handleMouseUp);
}

// 重置图片查看器状态
function resetImageViewer() {
    const image = document.getElementById('viewer-image');
    currentScale = 1;
    translateX = 0;
    translateY = 0;
    lastTranslateX = 0;
    lastTranslateY = 0;
    updateImageTransform(image);
}

// 关闭图片查看器
function closeImageViewer() {
    const viewer = document.getElementById('image-viewer');
    viewer.style.display = 'none';
    
    // 移除事件监听
    viewer.removeEventListener('wheel', handleImageWheel);
    viewer.removeEventListener('touchstart', handleTouchStart);
    viewer.removeEventListener('touchmove', handleTouchMove);
    viewer.removeEventListener('touchend', handleTouchEnd);
    
    // 移除鼠标事件监听
    viewer.removeEventListener('mousedown', handleMouseDown);
    viewer.removeEventListener('mousemove', handleMouseMove);
    viewer.removeEventListener('mouseup', handleMouseUp);
    viewer.removeEventListener('mouseleave', handleMouseUp);
}

// 处理触摸开始事件
function handleTouchStart(event) {
    event.preventDefault();
    const image = document.getElementById('viewer-image');
    
    if (event.touches.length === 2) {
        // 双指触摸 - 准备缩放
        initialDistance = getTouchDistance(event.touches);
        initialScale = currentScale;
        isDragging = false;
    } else if (event.touches.length === 1) {
        // 单指触摸 - 准备拖动或检测双击
        const touch = event.touches[0];
        startX = touch.clientX - lastTranslateX;
        startY = touch.clientY - lastTranslateY;
        isDragging = true;
        
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTapTime;
        
        if (tapLength < 300 && tapLength > 0) {
            // 双击检测到
            isDragging = false;
            handleDoubleTap(event);
        }
        lastTapTime = currentTime;
    }
}

// 处理触摸移动事件
function handleTouchMove(event) {
    event.preventDefault();
    const image = document.getElementById('viewer-image');
    
    if (event.touches.length === 2) {
        // 双指缩放
        const currentDistance = getTouchDistance(event.touches);
        const scale = currentDistance / initialDistance;
        let newScale = initialScale * scale;
        
        // 限制缩放范围
        newScale = Math.min(Math.max(newScale, MIN_SCALE), MAX_SCALE);
        
        // 计算缩放中心点
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];
        const centerX = (touch1.clientX + touch2.clientX) / 2;
        const centerY = (touch1.clientY + touch2.clientY) / 2;
        
        // 应用变换
        currentScale = newScale;
        updateImageTransform(image);
    } else if (event.touches.length === 1 && isDragging) {
        // 单指拖动
        const touch = event.touches[0];
        translateX = touch.clientX - startX;
        translateY = touch.clientY - startY;
        
        // 限制拖动范围
        const maxTranslateX = (currentScale - 1) * image.width / 2;
        const maxTranslateY = (currentScale - 1) * image.height / 2;
        
        translateX = Math.min(Math.max(translateX, -maxTranslateX), maxTranslateX);
        translateY = Math.min(Math.max(translateY, -maxTranslateY), maxTranslateY);
        
        updateImageTransform(image);
    }
}

// 处理触摸结束事件
function handleTouchEnd(event) {
    isDragging = false;
    lastTranslateX = translateX;
    lastTranslateY = translateY;
}

// 处理双击事件
function handleDoubleTap(event) {
    const image = document.getElementById('viewer-image');
    const touch = event.touches[0];
    
    if (currentScale === 1) {
        // 放大到指定比例
        currentScale = DOUBLE_TAP_ZOOM;
    } else {
        // 重置到原始大小
        currentScale = 1;
        translateX = 0;
        translateY = 0;
        lastTranslateX = 0;
        lastTranslateY = 0;
    }
    
    updateImageTransform(image);
}

// 更新图片变换
function updateImageTransform(image) {
    image.style.transform = `translate(${translateX}px, ${translateY}px) scale(${currentScale})`;
}

// 计算两个触摸点之间的距离
function getTouchDistance(touches) {
    const touch1 = touches[0];
    const touch2 = touches[1];
    return Math.hypot(
        touch2.clientX - touch1.clientX,
        touch2.clientY - touch1.clientY
    );
}

// 处理滚轮事件
function handleImageWheel(event) {
    event.preventDefault();
    
    const image = document.getElementById('viewer-image');
    
    // 确定缩放方向
    const delta = event.deltaY < 0 ? 1 : -1;
    
    // 计算新的缩放比例
    let newScale = currentScale + (delta * SCALE_STEP);
    
    // 限制缩放范围
    newScale = Math.min(Math.max(newScale, MIN_SCALE), MAX_SCALE);
    
    // 应用新的缩放比例
    currentScale = newScale;
    image.style.transform = `scale(${currentScale})`;
}

// 按ESC键关闭弹窗
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeImageViewer();
        closeAnnouncementDetail();
    }
});

// 查看更多公告
function viewMoreAnnouncements() {
    currentPage++;
    loadAnnouncements(currentPage);
}

// 显示项目更新详情
function showProjectUpdateDetail(update) {
    const detailDialog = document.getElementById('announcement-detail');
    const detailTitle = document.getElementById('detail-title');
    const detailTime = document.getElementById('detail-time');
    const detailTag = document.getElementById('detail-tag');
    const detailContent = document.getElementById('detail-content');
    
    if (!detailDialog || !detailTitle || !detailTime || !detailTag || !detailContent) return;
    
    // 填充内容
    detailTitle.textContent = update.project_name;
    detailTime.textContent = update.formatted_date;
    detailTag.textContent = update.category;
    detailTag.className = `tag tag-${getCategoryTagType(update.category)}`;
    detailContent.innerHTML = `<p>${update.description}</p>`;
    
    // 显示弹窗
    detailDialog.style.display = 'flex';
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
}

// 获取项目分类对应的标签样式
function getCategoryTagType(category) {
    const categoryMap = {
        '新功能': 'success',
        '新项目': 'primary',
        '更新': 'info',
        '优化': 'warning',
        '修复': 'danger'
    };
    return categoryMap[category] || 'info';
}

// 添加项目搜索和过滤相关功能

// 全局变量存储项目分类
let projectCategories = [];

// 切换项目搜索面板显示/隐藏
function toggleProjectSearch() {
    const searchContainer = document.getElementById('project-search-container');
    const searchToggleBtn = document.getElementById('project-search-toggle');
    
    if (searchContainer.style.display === 'none') {
        searchContainer.style.display = 'block';
        // 加载分类数据
        loadProjectCategories();
        // 更新按钮样式
        searchToggleBtn.classList.add('active');
    } else {
        searchContainer.style.display = 'none';
        // 恢复按钮样式
        searchToggleBtn.classList.remove('active');
    }
}

// 加载项目分类
function loadProjectCategories() {
    // 如果已经加载过分类，不重复请求
    if (projectCategories.length > 0) {
        fillCategoryOptions(projectCategories);
        return;
    }
    
    // 请求分类数据
    fetch('/api/project_updates')
        .then(response => response.json())
        .then(data => {
            if (data.categories && data.categories.length > 0) {
                projectCategories = data.categories;
                fillCategoryOptions(projectCategories);
            }
        })
        .catch(error => {
            console.error('加载项目分类失败:', error);
        });
}

// 填充分类选项
function fillCategoryOptions(categories) {
    const categorySelect = document.getElementById('project-category-filter');
    if (!categorySelect) return;
    
    // 保留第一个"所有分类"选项
    categorySelect.innerHTML = '<option value="">所有分类</option>';
    
    // 添加分类选项
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categorySelect.appendChild(option);
    });
}

// 添加全局变量存储当前筛选条件
let currentFilters = {
    search: '',
    category: '',
    start_date: '',
    end_date: '',
    page: 1
};

// 修改加载项目更新的函数
function loadProjectUpdates(page = 1) {
    // 构建查询参数
    const params = new URLSearchParams({
        page: page,
        search: currentFilters.search,
        category: currentFilters.category,
        start_date: currentFilters.start_date,
        end_date: currentFilters.end_date
    });

    fetch(`/api/project_updates?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('project-updates-container');
            if (!container) return;

            if (data.updates && data.updates.length > 0) {
                const updatesHtml = data.updates.map(update => `
                    <div class="announcement-item" onclick="showProjectUpdateDetail(${JSON.stringify(update).replace(/"/g, '&quot;')})">
                        <i class="fas fa-code-branch announcement-icon"></i>
                        <div class="announcement-content">
                            <div class="project-update-content">
                                <div class="project-update-title">${update.project_name}</div>
                                <div class="project-update-description">${update.description.substring(0, 100)}${update.description.length > 100 ? '...' : ''}</div>
                            </div>
                            <div class="announcement-meta">
                                <span>${update.formatted_date}</span>
                                <span class="tag tag-${getCategoryTagType(update.category)}">${update.category}</span>
                            </div>
                        </div>
                    </div>
                `).join('');

                // 修改分页控件，始终显示上一页和下一页按钮
                const paginationHtml = `
                    <div class="pagination-controls">
                        <button onclick="loadProjectUpdates(${Math.max(1, data.pagination.current_page - 1)})" 
                                class="page-btn"
                                ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 ${data.pagination.current_page} / ${data.pagination.total_pages || 1} 页</span>
                        <button onclick="loadProjectUpdates(${Math.min(data.pagination.total_pages || 1, data.pagination.current_page + 1)})" 
                                class="page-btn"
                                ${!data.pagination.has_next ? 'disabled' : ''}>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;

                container.innerHTML = updatesHtml + paginationHtml;
            } else {
                // 即使没有数据也显示分页控件
                container.innerHTML = `
                    <div class="no-data">没有找到符合条件的项目更新</div>
                    <div class="pagination-controls">
                        <button class="page-btn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 1 / 1 页</span>
                        <button class="page-btn" disabled>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载项目更新失败:', error);
            const container = document.getElementById('project-updates-container');
            if (container) {
                container.innerHTML = '<div class="error-message">加载失败，请稍后重试</div>';
            }
        });
}

// 修改应用筛选函数
function applyProjectFilters() {
    currentFilters = {
        search: document.getElementById('project-search-input').value.trim(),
        category: document.getElementById('project-category-filter').value,
        start_date: document.getElementById('project-start-date').value,
        end_date: document.getElementById('project-end-date').value,
        page: 1  // 重置为第一页
    };
    
    loadProjectUpdates(1);
}

// 修改清除筛选函数
function clearProjectFilters() {
    document.getElementById('project-search-input').value = '';
    document.getElementById('project-category-filter').value = '';
    document.getElementById('project-start-date').value = '';
    document.getElementById('project-end-date').value = '';
    
    currentFilters = {
        search: '',
        category: '',
        start_date: '',
        end_date: '',
        page: 1
    };
    
    loadProjectUpdates(1);
}

// 修改加载反馈列表的函数，增加分页功能
let feedbackCurrentPage = 1;
const FEEDBACK_PAGE_SIZE = 3;

function loadFeedbackList(page = 1) {
    fetch(`/api/feedback?page=${page}&page_size=${FEEDBACK_PAGE_SIZE}`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('feedback-list');
            if (!container) return;

            if (data.success && data.feedback && data.feedback.length > 0) {
                const feedbackHtml = data.feedback.map(feedback => {
                    // 处理状态样式
                    const statusClass = getStatusClass(feedback.status);
                    
                    // 如果有回复，添加一个回复标识类
                    const hasReplyClass = feedback.developer_response ? 'has-reply' : '';
                    
                    return `
                        <div class="announcement-item feedback-card ${hasReplyClass}" onclick="showFeedbackDetail(${JSON.stringify(feedback).replace(/"/g, '&quot;')})">
                            <i class="fas fa-comment-alt announcement-icon"></i>
                            <div class="announcement-content">
                                <div class="project-update-content">
                                    <div class="project-update-title">${feedback.request_name}</div>
                                    <div class="project-update-description">${feedback.description.substring(0, 100)}${feedback.description.length > 100 ? '...' : ''}</div>
                                </div>
                                <div class="announcement-meta">
                                    <span>${feedback.formatted_created_at}</span>
                                    <span class="tag ${statusClass}">${feedback.status}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
                
                // 修改分页控件，始终显示上一页和下一页按钮
                const paginationHtml = `
                    <div class="pagination-controls">
                        <button onclick="loadFeedbackList(${Math.max(1, data.pagination.current_page - 1)})" 
                                class="page-btn"
                                ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 ${data.pagination.current_page} / ${data.pagination.total_pages || 1} 页</span>
                        <button onclick="loadFeedbackList(${Math.min(data.pagination.total_pages || 1, data.pagination.current_page + 1)})" 
                                class="page-btn"
                                ${!data.pagination.has_next ? 'disabled' : ''}>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;

                container.innerHTML = feedbackHtml + paginationHtml;
            } else {
                // 即使没有数据也显示分页控件
                container.innerHTML = `
                    <div class="no-data">暂无反馈记录</div>
                    <div class="pagination-controls">
                        <button class="page-btn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 1 / 1 页</span>
                        <button class="page-btn" disabled>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载反馈失败:', error);
            const container = document.getElementById('feedback-list');
            if (container) {
                container.innerHTML = '<div class="error-message">加载失败，请稍后重试</div>';
            }
        });
}

// 优化显示反馈详情函数
function showFeedbackDetail(feedback) {
    const detailDialog = document.getElementById('announcement-detail');
    const detailTitle = document.getElementById('detail-title');
    const detailTime = document.getElementById('detail-time');
    const detailTag = document.getElementById('detail-tag');
    const detailContent = document.getElementById('detail-content');
    
    if (!detailDialog || !detailTitle || !detailTime || !detailTag || !detailContent) return;
    
    // 填充内容
    detailTitle.textContent = feedback.request_name;
    detailTime.textContent = feedback.formatted_created_at;
    detailTag.textContent = feedback.status;
    detailTag.className = `tag ${getStatusClass(feedback.status)}`;
    
    // 构建详情内容，包括请求和回复
    let contentHtml = `
        <div class="feedback-detail-section">
            <h4 class="feedback-detail-heading">
                <i class="fas fa-question-circle"></i> 我的请求
            </h4>
            <div class="feedback-detail-content">${feedback.description}</div>
        </div>
    `;
    
    if (feedback.developer_response) {
        contentHtml += `
            <div class="feedback-detail-section feedback-reply-section">
                <div class="feedback-reply-header">
                    <h4 class="feedback-detail-heading">
                        <i class="fas fa-reply"></i> 回复内容
                    </h4>
                    <div class="feedback-responder">
                        <i class="fas fa-user"></i> ${feedback.responder || '系统管理员'}
                    </div>
                </div>
                <div class="feedback-detail-content">${feedback.developer_response}</div>
            </div>
        `;
    }
    
    detailContent.innerHTML = contentHtml;
    
    // 添加特殊类以应用不同的样式
    detailDialog.classList.add('feedback-detail-dialog');
    
    // 显示弹窗
    detailDialog.style.display = 'flex';
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
}

// 提交反馈后更新函数
function submitFeedback() {
    const requestName = document.getElementById('request-name').value.trim();
    const description = document.getElementById('description').value.trim();
    
    if (!requestName || !description) {
        alert('请填写完整信息');
        return;
    }
    
    fetch('/api/feedback', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            request_name: requestName,
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('反馈提交成功');
            closeFeedbackModal();
            loadFeedbackList(1);  // 重置到第一页并重新加载反馈列表
        } else {
            alert(data.error || '提交失败，请稍后重试');
        }
    })
    .catch(error => {
        console.error('提交反馈失败:', error);
        alert('提交失败，请稍后重试');
    });
}

// 辅助函数：根据状态获取CSS类
function getStatusClass(status) {
    const statusMap = {
        '待处理': 'status-pending',
        '处理中': 'status-processing',
        '已完成': 'status-completed',
        '已拒绝': 'status-rejected',
        '已关闭': 'status-closed'
    };
    return statusMap[status] || 'status-default';
}

// 显示反馈弹窗
function showFeedbackModal() {
    const modal = document.getElementById('feedback-modal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

// 关闭反馈弹窗
function closeFeedbackModal() {
    const modal = document.getElementById('feedback-modal');
    if (modal) {
        modal.style.display = 'none';
        // 清空表单
        document.getElementById('request-name').value = '';
        document.getElementById('description').value = '';
    }
}

// 加载待办事项函数
function loadTodoItems(page = 1, status = 'unfinished') {
    fetch(`/api/todo_items?page=${page}&page_size=3&status=${status}`)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                throw new Error(data.error || '获取待办事项失败');
            }
            
            // 更新待办事项列表
            const container = document.querySelector('.panel-content');
            if (!container) return;
            
            if (data.todos && data.todos.length > 0) {
                const todosHtml = data.todos.map(todo => {
                    // 获取优先级对应的标签样式
                    const priorityClass = getPriorityClass(todo.priority);
                    const priorityLabel = getPriorityLabel(todo.priority);
                    
                    // 获取状态对应的标签样式
                    const statusClass = getStatusClass(todo.status);
                    
                    // 计算是否逾期
                    const isOverdue = todo.due_date && new Date(todo.due_date) < new Date() && 
                                     !['已完成', '已拒绝', '已撤销'].includes(todo.status);
                    
                    // 使用 formatted_due_date 替代原来的 due_date
                    const dueDate = todo.formatted_due_date || todo.due_date;
                    
                    // 获取创建时间
                    const createdTime = todo.source_created_time || '';
                    
                    // 构建待办项HTML
                    return `
                        <div class="todo-item ${isOverdue ? 'overdue' : ''}" 
                             data-id="${todo.todo_id}" 
                             data-interaction="${todo.interaction_type}"
                             data-source_type="${todo.source_type}"
                             data-source-created="${todo.source_created_time || ''}" 
                             data-interaction-data="${encodeURIComponent(JSON.stringify({
                                 ...todo.interaction_data || {},
                                 source_created_time: todo.source_created_time
                             }))}">
                            <div class="todo-status ${todo.status === '已完成' ? 'completed' : ''}" style="cursor: pointer;">
                                ${todo.status === '已完成' ? '<i class="fas fa-check"></i>' : ''}
                            </div>
                            <div class="todo-content">
                                <div class="todo-main">
                                    <div class="todo-title">${todo.title}</div>
                                  
                                </div>
                                <div class="todo-meta">
                                    <div class="todo-tags">
                                        <span class="tag ${statusClass}">${todo.status}</span>
                                        <span class="tag ${priorityClass}">${priorityLabel}</span>
                                        <span class="tag tag-source">${todo.source_type}</span>
                                    </div>
                                    <div class="todo-info">
                                        <span class="todo-time">创建：${createdTime}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
                
                // 修改分页控件，始终显示上一页和下一页按钮
                const paginationHtml = `
                    <div class="pagination-controls">
                        <button onclick="loadTodoItems(${Math.max(1, data.pagination.current_page - 1)}, '${status}')" 
                                class="page-btn"
                                ${data.pagination.current_page <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 ${data.pagination.current_page} / ${data.pagination.total_pages || 1} 页</span>
                        <button onclick="loadTodoItems(${Math.min(data.pagination.total_pages || 1, data.pagination.current_page + 1)}, '${status}')" 
                                class="page-btn"
                                ${!data.pagination.has_next ? 'disabled' : ''}>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;
                
                container.innerHTML = todosHtml + paginationHtml;
                
                // 添加点击事件处理
                document.querySelectorAll('.todo-item').forEach(item => {
                    item.addEventListener('click', handleTodoItemClick);
                });
                
                // 为todo-status添加点击事件处理
                document.querySelectorAll('.todo-status').forEach(status => {
                    status.addEventListener('click', handleTodoStatusClick);
                });

            } else {
                // 即使没有数据也显示分页控件
                container.innerHTML = `
                    <div class="no-data">暂无待办事项</div>
                    <div class="pagination-controls">
                        <button class="page-btn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">第 1 / 1 页</span>
                        <button class="page-btn" disabled>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                `;
            }
            
            // 更新统计数据
            updateTodoStatsDisplay(data.stats);
        })
        .catch(error => {
            console.error('加载待办事项失败:', error);
            const container = document.querySelector('.panel-content');
            if (container) {
                container.innerHTML = '<div class="error-message">加载失败，请稍后重试</div>';
            }
        });
}

// 更新待办事项统计数据
function updateTodoStats() {
    fetch('/api/todo_items?page=1&page_size=1')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.stats) {
                updateTodoStatsDisplay(data.stats);
            }
        })
        .catch(error => {
            console.error('获取待办统计失败:', error);
        });
}

// 更新统计数据显示
function updateTodoStatsDisplay(stats) {
    if (!stats) return;
    
    // 更新未完成数量
    const unfinishedCount = document.getElementById('unfinished-count');
    if (unfinishedCount) {
        unfinishedCount.textContent = stats.unfinished_count || 0;
    }
    
    // 更新已完成数量
    const finishedCount = document.getElementById('finished-count');
    if (finishedCount) {
        finishedCount.textContent = stats.finished_count || 0;
    }
    
    // 更新本月到期数量
    const dueThisMonthCount = document.getElementById('due-this-month-count');
    if (dueThisMonthCount) {
        dueThisMonthCount.textContent = stats.due_this_month_count || 0;
    }
    
    // 更新逾期数量
    const overdueCount = document.getElementById('overdue-count');
    if (overdueCount) {
        overdueCount.textContent = stats.overdue_count || 0;
        
        // 如果有逾期任务，添加警示效果
        const overdueItem = document.querySelector('.overdue-item');
        if (overdueItem) {
            if (stats.overdue_count > 0) {
                overdueItem.classList.add('has-overdue');
            } else {
                overdueItem.classList.remove('has-overdue');
            }
        }
    }
}

// 处理待办项点击事件
function handleTodoItemClick(event) {
    const todoItem = event.currentTarget;
    const todoId = todoItem.dataset.id;
    const interactionType = todoItem.dataset.interaction;
    const sourceType = todoItem.dataset.source_type;

    // 尝试将 interactionData 从 HTML 属性中提取
    let interactionData;
    try {
        interactionData = JSON.parse(decodeURIComponent(todoItem.dataset.interactionData || '{}'));
    } catch (e) {
        console.error('解析交互数据失败:', e);
        interactionData = {};
    }
    
    // console.log('点击待办项:', todoId, '类型:', interactionType, '数据:', interactionData);
    
    // 根据交互类型处理不同的行为
    if (interactionType === 'OA接口弹窗查看') {
        // 使用钉钉API处理OA弹窗交互
        if (window.DingTalkApi && typeof window.DingTalkApi.handleTodoClick === 'function') {
            window.DingTalkApi.handleTodoClick(interactionData);
        } else {
            console.error('钉钉API模块未加载');
            alert('系统错误，请刷新页面后重试');
        }
    } else if ( sourceType === '任务管理') {
        // 解析 interactionData
        try {
            const todoId = todoItem.dataset.id;
            const taskData = typeof interactionData === 'string' ? JSON.parse(interactionData) : interactionData;
            const statusElement = todoItem.querySelector('.tag:nth-child(1)');
            const status = statusElement ? statusElement.textContent : '';

            // 先获取项目信息
            fetch('/api/manage/getProject', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    project_id: taskData.project_id
                })
            })
            .then(response => response.json())
            .then(projectData => {
                if (!projectData.success) {
                    throw new Error(projectData.message || '获取项目信息失败');
                }

                // 使用现有的公告详情弹窗
                const detailDialog = document.getElementById('announcement-detail');
                const detailTitle = document.getElementById('detail-title');
                const detailTime = document.getElementById('detail-time');
                const detailTag = document.getElementById('detail-tag');
                const detailContent = document.getElementById('detail-content');
                
                if (!detailDialog || !detailTitle || !detailTime || !detailTag || !detailContent) {
                    console.error('弹窗元素不存在');
                    return;
                }
                
                // 填充标题和基本信息
                detailTitle.textContent = taskData.name || '未命名项目';
                detailTime.textContent = `创建时间：${taskData.created_at ? formatDateTime(taskData.created_at) : '未知'}`;
                detailTag.textContent = status;
                detailTag.className = `tag ${getStatusClass(status)}`;

                // 构建详情内容
                let contentHtml = `
                    <div class="task-detail-container">
                  
                    <div class="task-description task-info-card">
                            <div class="task-info-item">
                                <strong><i class="fas fa-tasks"></i> 任务描述：</strong> <span class="task-desc-text">${taskData.description || '暂无描述'}</span>
                            </div>
                            <div class="task-info-item">
                                <strong><i class="fas fa-project-diagram"></i> 项目名称：</strong> <span class="project-name-text">${projectData.data.name || '未知项目'}</span>
                            </div>
                            <div class="task-info-item">
                                <strong><i class="fas fa-file-alt"></i> 项目描述：</strong> <span class="project-desc-text">${projectData.data.description || '暂无描述'}</span>
                            </div>  
                            <div class="task-info-item">
                                <strong><i class="fas fa-file-alt"></i> 主任务：</strong> <span id="creator-parentTaskName"  class="project-desc-text">无</span>
                            </div>
                            <div class="task-info-item" id="task-creator-section">
                                <strong><i class="fas fa-user"></i> 任务发起人：</strong> <span id="creator-name-display" class="creator-name-text">${taskData.creator_id || '未知'}</span>
                            </div>
                            <div class="task-info-item">
                                <strong><i class="fas fa-calendar-alt"></i> 截止日期：</strong> <span class="deadline-text">${taskData.deadline ? taskData.deadline.split(' ')[0] : '无'}</span>
                            </div>
                    </div>

                     
                        
                       
                        <div class="task-completion-section">
                            ${status === '已完成' ? `
                            <div class="completion-actions">
                                <button class="complete-btn" onclick="handleTaskComplete(${todoId},${taskData.id},'处理中')" style="background-color: #ff7875;">
                                    <i class="fas fa-check"></i> 任务重启
                                </button>
                                <button class="cancel-task-btn" onclick="closeAnnouncementDetail()">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                            ` : `
                            
                             ${status === '处理中' ? `
                             <div class="completion-input-group">
                                <!-- 富文本编辑器容器 -->
                                <div class="rich-editor-container">
                                    <div id="taskCompletionEditorToolbar" class="rich-editor-toolbar"></div>
                                    <div id="taskCompletionEditorContent" class="rich-editor-content" style="height:150px;"></div>
                                </div>
                            </div>
                            <div class="completion-actions">
                                <button class="complete-btn" onclick="handleTaskComplete(${todoId},${taskData.id},'已完成')" style="background-color: #52c41a;">
                                    <i class="fas fa-check"></i> 确认完成
                                </button>
                                <button class="cancel-task-btn" onclick="closeAnnouncementDetail()">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                            ` : ''}
                              ${status === '待处理' ? `
                            <div class="completion-actions">
                                <button class="complete-btn" onclick="handleTaskComplete(${todoId},${taskData.id},'处理中')">
                                    <i class="fas fa-check"></i> 开始处理
                                </button>
                                <button class="cancel-task-btn" onclick="closeAnnouncementDetail()">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                            ` : ''}
                            `}
                        </div>
                        
                    </div>
                `;
                
                // 设置内容
                detailContent.innerHTML = contentHtml;
                
                // 为任务描述中的图片添加点击事件
                setTimeout(() => {
                    const descriptionImages = document.querySelectorAll('.task-desc-text img');
                    if (descriptionImages && descriptionImages.length > 0) {
                        descriptionImages.forEach(img => {
                            img.style.cursor = 'pointer';
                            img.onclick = function() {
                                showImageViewer(this.src);
                            };
                        });
                        console.log('成功为', descriptionImages.length, '张图片添加了点击事件');
                    }
                }, 300);
                
                // 查询任务创建者名称
                if (taskData.creator_id) {
                    fetch('/api/manage/getUserName', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            user_id: taskData.creator_id
                        })
                    })
                    .then(response => response.json())
                    .then(userData => {
                        if (userData.success && userData.data && userData.data.name) {
                            // 更新显示的创建者名称
                            const creatorNameElement = document.getElementById('creator-name-display');
                            if (creatorNameElement) {
                                creatorNameElement.textContent = userData.data.name;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('获取用户名称失败:', error);
                    });
                }
                // 查询主任务名称
                if (taskData.parent_id) {
                    fetch('/api/manage/getParentTaskName', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            parent_id: taskData.parent_id
                        })
                    })
                    .then(response => response.json())
                    .then(parentTask => {
                        if (parentTask.success && parentTask.data ) {
                            // 更新显示
                            const creatorParentTaskNameElement = document.getElementById('creator-parentTaskName');
                            if (creatorParentTaskNameElement) {
                                creatorParentTaskNameElement.textContent = parentTask.data.name;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('获取主任务名称失败:', error);
                    });
                }
                // 显示弹窗
                detailDialog.style.display = 'flex';
                detailDialog.classList.add('task-detail-dialog');
                
                // 禁止背景滚动
                document.body.style.overflow = 'hidden';
                
                // 如果状态不是已完成，初始化富文本编辑器
                if (status !== '已完成') {
                    setTimeout(() => {
                        initTaskCompletionEditor();
                    }, 100);
                }
            })
            .catch(error => {
                console.error('获取项目信息失败:', error);
                alert('获取项目信息失败，请稍后重试');
            });
            
        } catch (error) {
            console.error('解析任务数据失败:', error);
            alert('数据格式错误，无法显示详情');
        }
    } else {
            // 其他交互类型使用默认处理方式
            showTodoDetail(todoItem, interactionType, interactionData);
        }

    // 阻止事件冒泡，避免触发其他点击事件
    event.stopPropagation();
}

// 标记待办为已读
function markTodoAsRead(todoId) {
    // 这里可以实现标记已读的API调用
    console.log('标记待办已读:', todoId);
    // TODO: 实现标记已读API
}

// 显示待办详情
function showTodoDetail(todoItem, interactionType, interactionData) {
    // 从待办项元素获取信息
    const todoId = todoItem.dataset.id;
    const title = todoItem.querySelector('.todo-title').textContent;
    const description = todoItem.querySelector('.todo-description')?.textContent || '';
    const statusElement = todoItem.querySelector('.tag:nth-child(2)');
    const status = statusElement ? statusElement.textContent : '';
    
    // 获取来源信息
    const sourceElement = todoItem.querySelector('.tag-source');
    const source = sourceElement ? sourceElement.textContent : '';
    
    // 获取创建时间
    const createdTime = todoItem.dataset.sourceCreated || '';
    
    // 使用现有的公告详情弹窗展示待办详情
    const detailDialog = document.getElementById('announcement-detail');
    const detailTitle = document.getElementById('detail-title');
    const detailTime = document.getElementById('detail-time');
    const detailTag = document.getElementById('detail-tag');
    const detailContent = document.getElementById('detail-content');
    
    if (!detailDialog || !detailTitle || !detailTime || !detailTag || !detailContent) {
        console.error('弹窗元素不存在');
        return;
    }
    
    // 填充内容
    detailTitle.textContent = title;
    detailTime.textContent = source;
    detailTag.textContent = status;
    detailTag.className = `tag ${getStatusClass(status)}`;
    
    // 构建详情内容 HTML
    let contentHtml = '';
    
    // 添加源系统创建时间
    if (createdTime) {
        contentHtml += `<p><strong>创建时间：</strong>${createdTime}</p>`;
    }
    
    // 添加描述
    if (description) {
        contentHtml += `<p>${description}</p>`;
    }
    
    // 设置内容
    detailContent.innerHTML = contentHtml || '<p>无详细信息</p>';
    
    // 显示弹窗
    detailDialog.style.display = 'flex';
    detailDialog.classList.add('todo-detail-dialog'); // 添加特殊类用于样式定制
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
    
    // 标记为已读
    if (todoId) {
        console.log('标记待办已读:', todoId);
        // 如果后续实现了标记已读API，可以在这里调用
        // markTodoAsRead(todoId);
    }
}

// 显示待办处理弹窗
function showTodoProcessing(todoId, interactionData) {
    // 这里可以实现处理待办的弹窗功能
    console.log('显示待办处理:', todoId, interactionData);
    // TODO: 实现处理弹窗
}

// 获取优先级对应的标签样式
function getPriorityClass(priority) {
    const priorityMap = {
        0: 'priority-normal',
        1: 'priority-important',
        2: 'priority-urgent'
    };
    return priorityMap[priority] || 'priority-normal';
}

// 获取优先级对应的文本
function getPriorityLabel(priority) {
    const priorityMap = {
        0: '普通',
        1: '重要',
        2: '紧急'
    };
    return priorityMap[priority] || '普通';
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // 如果是今天
    if (date >= today && date < tomorrow) {
        return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
    
    // 如果是明天
    const nextDay = new Date(tomorrow);
    nextDay.setDate(nextDay.getDate() + 1);
    if (date >= tomorrow && date < nextDay) {
        return `明天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
    
    // 如果是本周
    if (date - today < 7 * 24 * 60 * 60 * 1000) {
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return `${weekdays[date.getDay()]} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
    
    // 其他日期
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

// 处理待办操作按钮点击
function handleTodoAction(todoId, action) {
    console.log(`处理待办 ${todoId} 的 ${action} 操作`);
    
    // 这里可以根据不同的 action 类型进行不同的处理
    // 目前只是记录日志，后续可以实现具体的API调用
    switch (action) {
        case 'approve':
            alert('已同意此待办事项');
            break;
        case 'reject':
            alert('已拒绝此待办事项');
            break;
        default:
            alert('执行了操作: ' + action);
            break;
    }
    
    // 关闭弹窗
    closeAnnouncementDetail();
    
    // 刷新待办列表
    loadTodoItems();
}

// 添加任务完成处理函数
function handleTaskComplete(todoId, taskId, state) {
    // 如果是重启任务（从已完成到处理中），不需要输入说明
    if (state === '处理中') {
        // 直接调用API更新状态
        updateTaskState(todoId, taskId, state, "处理中");
        return;
    }
    
    // 获取富文本编辑器内容或回退到textarea
    let completionText = '';
    let errorElement;
    
    if (window.taskCompletionEditor) {
        // 从富文本编辑器获取HTML内容
        completionText = window.taskCompletionEditor.getHtml();
        const editorElement = document.querySelector('.rich-editor-container');
        
        // 获取或创建错误提示元素
        errorElement = document.querySelector('.completion-error');
        if (!errorElement && editorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'completion-error';
            editorElement.parentNode.appendChild(errorElement);
        }
    } else {
        // 回退到旧版本：从textarea获取内容
        const completionInput = document.querySelector('.completion-input');
        if (completionInput) {
            completionText = completionInput.value.trim();
            
            // 获取或创建错误提示元素
            errorElement = document.querySelector('.completion-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'completion-error';
                completionInput.parentNode.appendChild(errorElement);
            }
        }
    }
    
    // 检查是否为空内容
    if (!completionText || completionText === '<p><br></p>') {
        if (errorElement) {
            errorElement.textContent = '请输入说明';
            errorElement.style.display = 'block';
        }
        
        // 如果有编辑器元素，添加错误样式
        const editorElement = document.querySelector('.rich-editor-content');
        if (editorElement) {
            editorElement.classList.add('error');
        } else if (document.querySelector('.completion-input')) {
            document.querySelector('.completion-input').classList.add('error');
        }
        
        return;
    }
    
    // 清除错误提示
    if (errorElement) {
        errorElement.style.display = 'none';
    }
    
    // 清除错误样式
    const editorElement = document.querySelector('.rich-editor-content');
    if (editorElement) {
        editorElement.classList.remove('error');
    } else if (document.querySelector('.completion-input')) {
        document.querySelector('.completion-input').classList.remove('error');
    }
    
    // 调用更新状态函数
    updateTaskState(todoId, taskId, state, completionText);
}

// 添加更新任务状态的函数
function updateTaskState(todoId, taskId, state, completionNote) {
    // 这里添加调用后端API的代码
    fetch('/api/manage/tasks/complete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            todo_id: todoId,
            task_id: taskId,
            state: state,
            completion_note: completionNote
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // alert('操作成功');
            closeAnnouncementDetail();
            // 刷新待办列表
            loadTodoItems();
        } else {
            alert(data.error || '操作失败，请稍后重试');
        }
    })
    .catch(error => {
        console.error('失败:', error);
        alert('操作失败，请稍后重试');
    });
}

// 添加状态切换处理函数
function handleStatusChange(event) {
    const status = event.target.value;
    loadTodoItems(1, status);
}

// 初始化任务完成富文本编辑器
function initTaskCompletionEditor() {
    // 确保之前的编辑器实例被销毁
    if (window.taskCompletionEditor) {
        window.taskCompletionEditor.destroy();
        window.taskCompletionEditor = null;
    }
    
    if (window.taskCompletionToolbar) {
        window.taskCompletionToolbar.destroy();
        window.taskCompletionToolbar = null;
    }
    
    // 获取编辑器元素
    const editorContent = document.getElementById('taskCompletionEditorContent');
    const editorToolbar = document.getElementById('taskCompletionEditorToolbar');
    
    if (!editorContent || !editorToolbar) {
        console.error('找不到编辑器DOM元素');
        return;
    }
    
    try {
        if (typeof window.wangEditor === 'undefined') {
            console.error('编辑器组件未加载');
            return;
        }
        
        const { createEditor, createToolbar } = window.wangEditor;
        
        // 编辑器配置
        const editorConfig = {
            placeholder: '请输入状态变更说明...',
            MENU_CONF: {
                uploadImage: {
                    server: '/projectmanagement/proxy/upload_file',
                    fieldName: 'files',
                    headers: {},
                    maxFileSize: 10 * 1024 * 1024,
                
                    // 自定义上传参数
                    customInsert(res, insertFn) {
                        if (res.success && res.files && res.files.length > 0) {
                            const file = res.files[0];
                            insertFn(file.url, file.original_name, file.url);
                        } else {
                            console.error('上传图片失败');
                        }
                    },
                    onError(file, err, res) {
                        alert('图片上传错误!');
                        console.error('图片上传错误: ' + (res?.message || err?.message || '未知错误'));
                    }
                },
                // 添加视频上传配置
                uploadVideo: {
                    server: '/projectmanagement/proxy/upload_file',
                    fieldName: 'files',
                    headers: {},
                    maxFileSize: 500 * 1024 * 1024, // 设置最大文件大小为50MB
                    allowedFileTypes: ['video/*'], // 允许的文件类型
                    customInsert(res, insertFn) {
                        // 处理上传成功的响应
                        if (res.success && res.files && res.files.length > 0) {
                            const file = res.files[0];
                            insertFn(file.url);
                        } else {
                            console.error('视频上传失败');
                        }
                    },
                    onError(file, err, res) {
                        alert('视频上传错误!');
                        console.error('视频上传错误: ' + (res?.message || err?.message || '未知错误'));
                    }
                }
            }
        };
        
        // 工具栏配置
        const toolbarConfig = {
            toolbarKeys: [
                'bold',
                'italic',
                'underline',
                'uploadImage',
                'uploadVideo'  // 添加视频上传按钮
            ]
        };
        
        // 创建编辑器
        window.taskCompletionEditor = createEditor({
            selector: editorContent,
            config: editorConfig,
            mode: 'default'
        });
        
        // 创建工具栏
        window.taskCompletionToolbar = createToolbar({
            editor: window.taskCompletionEditor,
            selector: editorToolbar,
            config: toolbarConfig,
            mode: 'default'
        });
        
        return window.taskCompletionEditor;
    } catch (error) {
        console.error('初始化编辑器失败', error);
        return null;
    }
}

function handleTodoStatusClick(event) {
    // 阻止事件冒泡，这样就不会触发todo-item的点击事件
    event.stopPropagation();
    
    const todoStatus = event.currentTarget;
    const todoItem = todoStatus.closest('.todo-item');
    const interactionType = todoItem.dataset.interaction;
    const sourceType = todoItem.dataset.source_type;
    console.log('点击了待办状态切换按钮', todoItem.dataset)
    if (sourceType !== '任务管理') {
        return;
    }
    const todoId = todoItem.dataset.id;
    const taskId = todoItem.dataset.interactionData ? JSON.parse(decodeURIComponent(todoItem.dataset.interactionData)).id : null;
    const currentStatus = todoStatus.classList.contains('completed');
    const title = todoItem.querySelector('.todo-title').textContent;
    
    // 如果当前是已完成状态，则变为处理中，否则变为已完成
    const newState = currentStatus ? '处理中' : '已完成';
    
    // 使用现有的公告详情弹窗
    const detailDialog = document.getElementById('announcement-detail');
    const detailTitle = document.getElementById('detail-title');
    const detailTime = document.getElementById('detail-time');
    const detailTag = document.getElementById('detail-tag');
    const detailContent = document.getElementById('detail-content');
    
    if (!detailDialog || !detailTitle || !detailTime || !detailTag || !detailContent) {
        console.error('弹窗元素不存在');
        return;
    }
    
    // 填充弹窗内容
    detailTitle.textContent = title;
    detailTime.textContent = '状态更新';
    detailTag.textContent = `${currentStatus ? '已完成 → 处理中' : '处理中 → 已完成'}`;
    detailTag.className = `tag ${currentStatus ? 'status-processing' : 'status-completed'}`;
    
    // 构建表单内容
    let contentHtml;
    
    // 如果是从已完成到处理中，不需要输入框
    if (currentStatus) { // 已完成 -> 处理中
        contentHtml = `
            <div class="task-completion-section" style="margin-top: 0;">
                <div class="completion-actions">
                    <button class="complete-btn" onclick="handleQuickStatusUpdate(${todoId}, ${taskId}, '${newState}')" style="background-color: #ff7875;">
                        <i class="fas fa-check"></i> 确认重启任务
                    </button>
                    <button class="cancel-task-btn" onclick="closeAnnouncementDetail()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        `;
    } else { // 处理中 -> 已完成
        contentHtml = `
            <div class="task-completion-section" style="margin-top: 0;">
                <div class="completion-input-group">
                    <!-- 富文本编辑器容器 -->
                    <div class="rich-editor-container">
                        <div id="taskCompletionEditorToolbar" class="rich-editor-toolbar"></div>
                        <div id="taskCompletionEditorContent" class="rich-editor-content" style="height:350px;"></div>
                    </div>
                </div>
                <div class="completion-actions">
                    <button class="complete-btn" onclick="handleQuickStatusUpdate(${todoId}, ${taskId}, '${newState}')" ${newState === '已完成' ? 'style="background-color: #52c41a;"' : ''}>
                        <i class="fas fa-check"></i> 确认${newState === '已完成' ? '完成' : '重启'}
                    </button>
                    <button class="cancel-task-btn" onclick="closeAnnouncementDetail()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        `;
    }
    
    // 设置内容
    detailContent.innerHTML = contentHtml;
    
    // 显示弹窗
    detailDialog.style.display = 'flex';
    detailDialog.classList.add('task-detail-dialog');
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
    
    // 初始化富文本编辑器
    if (!currentStatus) {
        setTimeout(() => {
            initTaskCompletionEditor();
        }, 100);
    }
}

// 添加快速状态更新处理函数
function handleQuickStatusUpdate(todoId, taskId, newState) {
    // 如果是重启任务（从已完成到处理中），不需要输入说明
    if (newState === '处理中') {
        updateTaskState(todoId, taskId, newState, "任务重启");
        return;
    }
    
    // 获取富文本编辑器内容
    let completionText = '';
    let errorElement;
    
    if (window.taskCompletionEditor) {
        // 从富文本编辑器获取HTML内容
        completionText = window.taskCompletionEditor.getHtml();
        const editorElement = document.querySelector('.rich-editor-container');
        
        // 获取或创建错误提示元素
        errorElement = document.querySelector('.completion-error');
        if (!errorElement && editorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'completion-error';
            editorElement.parentNode.appendChild(errorElement);
        }
    } else {
        // 回退到旧版本：从textarea获取内容
        const completionInput = document.querySelector('.completion-input');
        if (completionInput) {
            completionText = completionInput.value.trim();
            
            // 获取或创建错误提示元素
            errorElement = document.querySelector('.completion-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'completion-error';
                completionInput.parentNode.appendChild(errorElement);
            }
        }
    }
    
    // 检查是否为空内容
    if (!completionText || completionText === '<p><br></p>') {
        if (errorElement) {
            errorElement.textContent = '请输入说明';
            errorElement.style.display = 'block';
        }
        
        // 如果有编辑器元素，添加错误样式
        const editorElement = document.querySelector('.rich-editor-content');
        if (editorElement) {
            editorElement.classList.add('error');
        }
        
        return;
    }
    
    // 清除错误提示
    if (errorElement) {
        errorElement.style.display = 'none';
    }
    
    // 清除错误样式
    const editorElement = document.querySelector('.rich-editor-content');
    if (editorElement) {
        editorElement.classList.remove('error');
    }
    
    // 调用API更新状态
    updateTaskState(todoId, taskId, newState, completionText);
}

// 处理鼠标按下事件
function handleMouseDown(event) {
    // 只响应鼠标左键
    if (event.button !== 0) return;
    
    event.preventDefault();
    isMouseDown = true;
    startX = event.clientX - lastTranslateX;
    startY = event.clientY - lastTranslateY;
    
    // 改变鼠标样式
    const viewer = document.getElementById('image-viewer');
    viewer.style.cursor = 'grabbing';
}

// 处理鼠标移动事件
function handleMouseMove(event) {
    if (!isMouseDown) return;
    
    event.preventDefault();
    const image = document.getElementById('viewer-image');
    
    // 计算新的位置
    translateX = event.clientX - startX;
    translateY = event.clientY - startY;
    
    // 限制拖动范围
    const maxTranslateX = (currentScale - 1) * image.width / 2;
    const maxTranslateY = (currentScale - 1) * image.height / 2;
    
    translateX = Math.min(Math.max(translateX, -maxTranslateX), maxTranslateX);
    translateY = Math.min(Math.max(translateY, -maxTranslateY), maxTranslateY);
    
    updateImageTransform(image);
}

// 处理鼠标松开事件
function handleMouseUp(event) {
    if (!isMouseDown) return;
    
    event.preventDefault();
    isMouseDown = false;
    lastTranslateX = translateX;
    lastTranslateY = translateY;
    
    // 恢复鼠标样式
    const viewer = document.getElementById('image-viewer');
    viewer.style.cursor = 'grab';
} 