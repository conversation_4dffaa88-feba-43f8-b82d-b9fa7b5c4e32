from flask import jsonify, session, request
import datetime
import os
from dingtalk_send_msg import send_task_assigned_message, get_access_token
def get_descendant_ids(task_id, cur):
    """递归获取一个任务的所有子孙任务ID"""
    descendants = set()
    queue = [task_id]
    processed_parents = set() # 防止无限循环（虽然理论上不该有）

    while queue:
        parent_id = queue.pop(0)
        if parent_id in processed_parents:
            continue
        processed_parents.add(parent_id)

        cur.execute("SELECT id FROM project_management_t_template_tasks WHERE parent_id = %s AND deleted_at IS NULL", [parent_id])
        direct_children = cur.fetchall()
        for child in direct_children:
            child_id = child['id']
            if child_id not in descendants:
                descendants.add(child_id)
                queue.append(child_id)
    return list(descendants)

def init_project_management_templates(app, mysql):
    """初始化项目管理模板相关的API路由"""

    @app.route('/projectmanagement/api/templates')
    def project_api_get_templates():
        """获取模板列表API"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        user_id = session['user_id'] # 虽然目前没用到，但保留以备后续权限控制

        try:
            cur = mysql.connection.cursor()
            
            # 查询模板表，并关联用户表获取创建者名称
            cur.execute("""
                SELECT 
                    t.id, 
                    t.name, 
                    t.description, 
                    t.created_at, 
                    t.updated_at,
                    u.name as creator_name 
                FROM project_management_t_templates t
                LEFT JOIN project_management_t_users u ON t.creator_id = u.id
                WHERE t.deleted_at IS NULL
                ORDER BY t.created_at DESC
            """)
            
            templates_raw = cur.fetchall()
            cur.close()
            
            templates_list = []
            for template in templates_raw:
                # 移除时间增量，直接格式化数据库时间
                created_at_str = template['created_at'].strftime('%Y-%m-%d %H:%M') if template['created_at'] else None
                updated_at_str = template['updated_at'].strftime('%Y-%m-%d %H:%M') if template['updated_at'] else None
                
                templates_list.append({
                    'id': template['id'],
                    'name': template['name'],
                    'description': template['description'],
                    'creator': template['creator_name'] or '未知', # 处理创建者为空的情况
                    'created_at': created_at_str,
                    'last_updated': updated_at_str # 前端卡片用的是 last_updated
                })

            return jsonify({'success': True, 'templates': templates_list})

        except Exception as e:
            # 记录详细错误日志会更好
            print(f"获取模板列表时出错: {str(e)}") 
            return jsonify({'success': False, 'message': f'获取模板列表失败: {str(e)}'}), 500

    @app.route('/projectmanagement/api/template/<int:template_id>')
    def project_api_get_template_details(template_id):
        """获取单个模板详情及其任务列表API"""
        # 检查用户是否登录
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        try:
            cur = mysql.connection.cursor()
            
            # 1. 获取模板基本信息
            cur.execute("""
                SELECT t.id, t.name, t.description, u.name as creator_name, t.created_at, t.updated_at
                FROM project_management_t_templates t
                LEFT JOIN project_management_t_users u ON t.creator_id = u.id
                WHERE t.id = %s AND t.deleted_at IS NULL
            """, [template_id])
            template_info = cur.fetchone()
            
            if not template_info:
                cur.close()
                return jsonify({'success': False, 'message': '模板不存在'}), 404
            
            # 2. 获取模板下的任务列表，并关联建议负责人角色
            cur.execute("""
                SELECT 
                    tt.id, 
                    tt.name, 
                    tt.description, 
                    tt.urgency,
                    tt.parent_id, 
                    tt.relative_deadline_days, 
                    tt.is_milestone,
                    tt.assignees_type,
                    GROUP_CONCAT(DISTINCT assignee.name SEPARATOR ', ') as assignee_name,
                    GROUP_CONCAT(tta.assignee_role SEPARATOR ', ') as assignee_roles,
                    GROUP_CONCAT(tta.assignee_description SEPARATOR '; ') as assignee_descriptions,
                    CASE WHEN tt.parent_id IS NULL THEN 0 ELSE 1 END as is_child, -- 用于前端排序
                    CASE WHEN tt.parent_id IS NULL THEN tt.id ELSE tt.parent_id END as sort_group -- 用于前端排序
                FROM project_management_t_template_tasks tt
                LEFT JOIN project_management_t_template_task_assignees tta ON tt.id = tta.template_task_id
                LEFT JOIN project_management_t_users assignee ON tta.assignee_description = assignee.id
                AND tta.deleted_at IS NULL
                WHERE tt.template_id = %s
                AND tt.deleted_at IS NULL
                GROUP BY tt.id, tt.name, tt.description, tt.urgency, tt.parent_id, 
                         tt.relative_deadline_days, tt.is_milestone, is_child, sort_group
                ORDER BY tt.sort_order ASC -- 使用新的 sort_order 列排序
            """, [template_id])
            
            template_tasks_raw = cur.fetchall()
            cur.close()
            
            template_tasks_list = []
            for task in template_tasks_raw:
                # 组合负责人信息
                assignees_display =  task['assignee_name'] or task['assignee_descriptions']
                
                template_tasks_list.append({
                    'id': task['id'],
                    'name': task['name'],
                    'description': task['description'],
                    'urgency': task['urgency'],
                    'parent_id': task['parent_id'],
                    'relative_deadline_days': task['relative_deadline_days'],
                    'is_milestone': task['is_milestone'],
                    'is_child': task['is_child'] == 1, # 转为布尔值
                    'assignees': assignees_display, # 使用组合后的负责人/角色显示
                    'assignee_descriptions': task['assignee_descriptions'],
                    'assignees_type': task['assignees_type']
                    # 可以根据需要添加更多字段，例如创建时间等
                })
                
            # 格式化模板信息中的时间戳
            # 移除时间增量，直接格式化数据库时间
            created_at_str = template_info['created_at'].strftime('%Y-%m-%d %H:%M') if template_info['created_at'] else None
            updated_at_str = template_info['updated_at'].strftime('%Y-%m-%d %H:%M') if template_info['updated_at'] else None

            formatted_template_info = {
                'id': template_info['id'],
                'name': template_info['name'],
                'description': template_info['description'],
                'creator': template_info['creator_name'] or '未知',
                'created_at': created_at_str,
                'last_updated': updated_at_str
            }

            return jsonify({
                'success': True, 
                'template': formatted_template_info,
                'tasks': template_tasks_list
            })

        except Exception as e:
            print(f"获取模板详情时出错: {str(e)}") 
            # 可以在这里关闭游标，如果它还打开的话
            if 'cur' in locals() and cur and cur.connection:
                 cur.close()
            return jsonify({'success': False, 'message': f'获取模板详情失败: {str(e)}'}), 500

    # 新增模板创建API
    @app.route('/projectmanagement/api/template', methods=['POST'])
    def project_api_create_template():
        """创建新模板API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        user_id = session['user_id']
        data = request.get_json()

        if not data or not data.get('name'):
            return jsonify({'success': False, 'message': '缺少模板名称'}), 400

        name = data.get('name')
        description = data.get('description', '')

        try:
            cur = mysql.connection.cursor()
            
            # 创建新模板
            cur.execute("""
                INSERT INTO project_management_t_templates 
                (name, description, creator_id, created_at, updated_at) 
                VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """, [name, description, user_id])
            
            new_template_id = cur.lastrowid
            
            # 获取创建者信息
            cur.execute("SELECT name FROM project_management_t_users WHERE id = %s", [user_id])
            creator = cur.fetchone()
            creator_name = creator['name'] if creator else '未知'
            
            # 获取新创建的模板信息返回给前端
            cur.execute("""
                SELECT id, name, description, created_at, updated_at
                FROM project_management_t_templates
                WHERE id = %s
            """, [new_template_id])
            
            template_info = cur.fetchone()
            mysql.connection.commit()
            cur.close()
            
            if template_info:
                created_at_str = template_info['created_at'].strftime('%Y-%m-%d %H:%M') if template_info['created_at'] else None
                updated_at_str = template_info['updated_at'].strftime('%Y-%m-%d %H:%M') if template_info['updated_at'] else None
                
                return jsonify({
                    'success': True, 
                    'message': '模板创建成功',
                    'template': {
                        'id': template_info['id'],
                        'name': template_info['name'],
                        'description': template_info['description'],
                        'creator': creator_name,
                        'created_at': created_at_str,
                        'last_updated': updated_at_str
                    }
                })
            else:
                return jsonify({'success': False, 'message': '模板创建成功但获取详情失败'}), 500
                
        except Exception as e:
            mysql.connection.rollback()
            print(f"创建模板时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'创建模板失败: {str(e)}'}), 500

    # --- 新增API端点 ---

    @app.route('/projectmanagement/api/template/<int:template_id>/task', methods=['POST'])
    def project_api_add_template_task(template_id):
        """添加模板任务API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        user_id = session['user_id']
        data = request.get_json()

        if not data or not data.get('name'):
            return jsonify({'success': False, 'message': '缺少任务名称'}), 400

        name = data.get('name')
        description = data.get('description', '')
        urgency = data.get('urgency', '普通')
        relative_deadline_days = data.get('relative_deadline_days') # 可以是None
        is_milestone = data.get('is_milestone', False)
        assignees_type = data.get('assignees_type', '')
        assignee_roles = data.get('assignees', '') # 期望是逗号分隔的字符串或单一描述
        parent_id = data.get('parent_id') # For adding subtasks directly

        try:
            cur = mysql.connection.cursor()
            
            # 检查模板是否存在
            cur.execute("SELECT 1 FROM project_management_t_templates WHERE id = %s", [template_id])
            if not cur.fetchone():
                cur.close()
                return jsonify({'success': False, 'message': '模板不存在'}), 404
            
            # 计算 sort_order
            new_sort_order = 1.0 # Default for the very first task
            
            if parent_id:
                # --- Subtask Logic ---
                # Get parent's sort_order
                cur.execute("SELECT sort_order FROM project_management_t_template_tasks WHERE id = %s AND template_id = %s", [parent_id, template_id])
                parent_task = cur.fetchone()
                if not parent_task:
                    cur.close()
                    return jsonify({'success': False, 'message': '父任务无效或不属于该模板'}), 400
                parent_sort_order = parent_task['sort_order']
                
                # Find max sort_order among siblings
                cur.execute("""
                    SELECT MAX(sort_order) as max_so 
                    FROM project_management_t_template_tasks 
                    WHERE template_id = %s AND parent_id = %s AND deleted_at IS NULL
                """, [template_id, parent_id])
                max_sibling_result = cur.fetchone()
                max_sibling_so = max_sibling_result['max_so']
                
                if max_sibling_so is None:
                    # First child
                    new_sort_order = parent_sort_order + 0.1
                else:
                    # Add after the last sibling
                    # Be careful with potential floating point inaccuracies if we just add 0.1
                    # A safer approach might involve finding the integer part and the next decimal
                    # For now, let's stick to adding 0.1, assuming reasonable precision needs
                    new_sort_order = max_sibling_so + 0.1
            else:
                # --- Top-Level Task Logic ---
                cur.execute("""
                    SELECT MAX(sort_order) as max_so 
                    FROM project_management_t_template_tasks 
                    WHERE template_id = %s AND parent_id IS NULL AND deleted_at IS NULL
                """, [template_id])
                max_top_level_result = cur.fetchone()
                max_top_level_so = max_top_level_result['max_so']
                
                if max_top_level_so is not None:
                    new_sort_order = float(int(max_top_level_so)) + 1.0 # Next integer
                # else: new_sort_order remains 1.0 (first top-level task)

            # 插入任务, 包括计算好的 sort_order
            cur.execute("""
                INSERT INTO project_management_t_template_tasks 
                (template_id, name, description, urgency, relative_deadline_days, is_milestone, parent_id, sort_order,assignees_type) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s,%s)
            """, [template_id, name, description, urgency, relative_deadline_days, is_milestone, parent_id, new_sort_order,assignees_type])
            
            new_task_id = cur.lastrowid

            # 插入负责人信息 (简化处理，只存一个assignee记录，内容是完整的字符串)
            if assignee_roles:
                 cur.execute("""
                    INSERT INTO project_management_t_template_task_assignees
                    (template_task_id, assignee_role, assignee_description)
                    VALUES (%s, %s, %s)
                 """, [new_task_id, None, assignee_roles]) # 将整个字符串存入description

            mysql.connection.commit()
            cur.close()

            # 获取新创建的任务详情返回给前端
            new_task = get_single_template_task(new_task_id, mysql)
            if new_task:
                 return jsonify({'success': True, 'message': '模板任务创建成功', 'task': new_task})
            else:
                 # 即使创建成功，获取失败也应视为部分失败
                 return jsonify({'success': False, 'message': '任务创建成功但获取详情失败'}), 500

        except Exception as e:
            mysql.connection.rollback() # Ensure rollback on error
            print(f"添加模板任务时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'添加模板任务失败: {str(e)}'}), 500

    @app.route('/projectmanagement/api/template/task/<int:task_id>', methods=['PUT'])
    def project_api_update_template_task(task_id):
        """更新模板任务API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '缺少更新数据'}), 400

        # 获取允许更新的字段
        name = data.get('name')
        description = data.get('description')
        urgency = data.get('urgency')
        relative_deadline_days = data.get('relative_deadline_days')
        is_milestone = data.get('is_milestone') # 通常不在此更新，但保留可能
        assignee_roles = data.get('assignees') # 期望是逗号分隔的字符串或单一描述
        assignees_type = data.get('assignees_type')

        update_fields = {}
        if name is not None: update_fields['name'] = name
        if description is not None: update_fields['description'] = description
        if urgency is not None: update_fields['urgency'] = urgency
        if relative_deadline_days is not None: update_fields['relative_deadline_days'] = relative_deadline_days
        if is_milestone is not None: update_fields['is_milestone'] = is_milestone
        if assignees_type is not None: update_fields['assignees_type'] = assignees_type

        if not update_fields and assignee_roles is None:
             return jsonify({'success': False, 'message': '没有提供可更新的字段'}), 400

        try:
            cur = mysql.connection.cursor()

            # 检查任务是否存在
            cur.execute("SELECT template_id FROM project_management_t_template_tasks WHERE id = %s", [task_id])
            task_exists = cur.fetchone()
            if not task_exists:
                cur.close()
                return jsonify({'success': False, 'message': '任务不存在'}), 404
            
            # 更新任务主表
            if update_fields:
                set_clause = ", ".join([f"{key} = %s" for key in update_fields.keys()])
                values = list(update_fields.values()) + [task_id]
                cur.execute(f"UPDATE project_management_t_template_tasks SET {set_clause} WHERE id = %s", values)

            # 更新负责人信息 (简化处理：删除旧的，插入新的)
            if assignee_roles is not None:
                 cur.execute("DELETE FROM project_management_t_template_task_assignees WHERE template_task_id = %s", [task_id])
                 if assignee_roles: # 只有在提供了新的非空assignees时才插入
                     cur.execute("""
                        INSERT INTO project_management_t_template_task_assignees
                        (template_task_id, assignee_role, assignee_description)
                        VALUES (%s, %s, %s)
                     """, [task_id, None, assignee_roles])

            mysql.connection.commit()
            cur.close()
            
            # 获取更新后的任务详情返回给前端
            updated_task = get_single_template_task(task_id, mysql)
            if updated_task:
                 return jsonify({'success': True, 'message': '模板任务更新成功', 'task': updated_task})
            else:
                 return jsonify({'success': False, 'message': '任务更新成功但获取详情失败'}), 500

        except Exception as e:
            mysql.connection.rollback()
            print(f"更新模板任务时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'更新模板任务失败: {str(e)}'}), 500

    @app.route('/projectmanagement/api/template/task/<int:task_id>', methods=['DELETE'])
    def project_api_delete_template_task(task_id):
        """删除模板任务API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        try:
            cur = mysql.connection.cursor()
            
            # 检查任务是否存在
            cur.execute("SELECT parent_id FROM project_management_t_template_tasks WHERE id = %s", [task_id])
            task = cur.fetchone()
            if not task:
                cur.close()
                return jsonify({'success': False, 'message': '任务不存在'}), 404

            # 如果是父任务，检查是否有子任务
            if task['parent_id'] is None:
                cur.execute("SELECT 1 FROM project_management_t_template_tasks WHERE parent_id = %s", [task_id])
                if cur.fetchone():
                     cur.close()
                     return jsonify({'success': False, 'message': '无法删除父任务，请先删除其所有子任务'}), 400

            # 删除任务相关数据 (负责人和任务本身)
            # 注意：数据库应设置级联删除或在这里显式删除子任务的负责人（如果需要更复杂的逻辑）
            cur.execute("DELETE FROM project_management_t_template_task_assignees WHERE template_task_id = %s", [task_id])
            cur.execute("DELETE FROM project_management_t_template_tasks WHERE id = %s", [task_id])
            
            affected_rows = cur.rowcount
            
            mysql.connection.commit()
            cur.close()

            if affected_rows > 0:
                return jsonify({'success': True, 'message': '模板任务删除成功'})
            else:
                # 可能任务刚好被别人删除
                return jsonify({'success': False, 'message': '任务未找到或已被删除'}), 404

        except Exception as e:
            mysql.connection.rollback()
            print(f"删除模板任务时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'删除模板任务失败: {str(e)}'}), 500

    @app.route('/projectmanagement/api/template/task/reorder', methods=['POST'])
    def project_api_reorder_template_task():
        """重新排序模板任务API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401

        data = request.get_json()
        if not data or 'task_id' not in data or 'direction' not in data:
            return jsonify({'success': False, 'message': '缺少必要参数 (task_id, direction)'}), 400

        task_id = data['task_id']
        direction = data['direction'] # 'up' or 'down'

        if direction not in ['up', 'down']:
             return jsonify({'success': False, 'message': '无效的方向参数'}), 400

        try:
            cur = mysql.connection.cursor()

            # Get the task to move and its context
            cur.execute("""
                SELECT id, template_id, parent_id, sort_order 
                FROM project_management_t_template_tasks 
                WHERE id = %s AND deleted_at IS NULL
            """, [task_id])
            task_to_move = cur.fetchone()

            if not task_to_move:
                cur.close()
                return jsonify({'success': False, 'message': '任务不存在或已被删除'}), 404

            task_a_id = task_to_move['id']
            template_id = task_to_move['template_id']
            parent_id = task_to_move['parent_id'] # Could be None
            sort_order_a = task_to_move['sort_order']

            # Find the sibling task to swap with
            sibling_to_swap = None
            if direction == 'up':
                # Find the task immediately before the current one
                query = """
                    SELECT id, sort_order 
                    FROM project_management_t_template_tasks 
                    WHERE template_id = %s AND (%s IS NULL AND parent_id IS NULL OR parent_id = %s)
                    AND sort_order < %s AND deleted_at IS NULL
                    ORDER BY sort_order DESC 
                    LIMIT 1
                """
            else: # direction == 'down'
                # Find the task immediately after the current one
                query = """
                    SELECT id, sort_order 
                    FROM project_management_t_template_tasks 
                    WHERE template_id = %s AND (%s IS NULL AND parent_id IS NULL OR parent_id = %s)
                    AND sort_order > %s AND deleted_at IS NULL
                    ORDER BY sort_order ASC 
                    LIMIT 1
                """
            
            cur.execute(query, [template_id, parent_id, parent_id, sort_order_a])
            sibling_to_swap = cur.fetchone()

            if not sibling_to_swap:
                cur.close()
                # It's already the first or last task, no action needed, return success
                return jsonify({'success': True, 'message': '任务已在顶部或底部'})

            task_b_id = sibling_to_swap['id']
            sort_order_b = sibling_to_swap['sort_order']

            # --- Revision Start: Preserve relative child order --- 
            
            # 1. Get all descendants for both tasks AND their original sort orders
            all_involved_ids = {task_a_id, task_b_id}
            descendants_a_ids = get_descendant_ids(task_a_id, cur)
            descendants_b_ids = get_descendant_ids(task_b_id, cur)
            all_involved_ids.update(descendants_a_ids)
            all_involved_ids.update(descendants_b_ids)
            
            original_sort_orders = {}
            if all_involved_ids:
                format_strings_involved = ',' .join(['%s'] * len(all_involved_ids))
                cur.execute(f"SELECT id, sort_order FROM project_management_t_template_tasks WHERE id IN ({format_strings_involved})", list(all_involved_ids))
                results = cur.fetchall()
                for row in results:
                    original_sort_orders[row['id']] = row['sort_order']
            
            # Ensure we have original orders for the main tasks too
            original_sort_orders[task_a_id] = sort_order_a
            original_sort_orders[task_b_id] = sort_order_b

            # 2. Perform updates within a transaction
            
            # Update Task A and its descendants
            new_sort_order_a = sort_order_b # A moves to B's original spot
            cur.execute("UPDATE project_management_t_template_tasks SET sort_order = %s WHERE id = %s", [new_sort_order_a, task_a_id])
            for descendant_id in descendants_a_ids:
                original_descendant_so = original_sort_orders.get(descendant_id)
                if original_descendant_so is not None:
                     offset = original_descendant_so - sort_order_a # Offset from original parent A
                     new_descendant_so = new_sort_order_a + offset
                     cur.execute("UPDATE project_management_t_template_tasks SET sort_order = %s WHERE id = %s", [new_descendant_so, descendant_id])
                         
            # Update Task B and its descendants
            new_sort_order_b = sort_order_a # B moves to A's original spot
            cur.execute("UPDATE project_management_t_template_tasks SET sort_order = %s WHERE id = %s", [new_sort_order_b, task_b_id])
            for descendant_id in descendants_b_ids:
                original_descendant_so = original_sort_orders.get(descendant_id)
                if original_descendant_so is not None:
                     offset = original_descendant_so - sort_order_b # Offset from original parent B
                     new_descendant_so = new_sort_order_b + offset
                     cur.execute("UPDATE project_management_t_template_tasks SET sort_order = %s WHERE id = %s", [new_descendant_so, descendant_id])

            # --- Revision End ---

            mysql.connection.commit()
            cur.close()

            return jsonify({'success': True, 'message': '任务排序已更新'})

        except Exception as e:
            mysql.connection.rollback()
            print(f"排序模板任务时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'排序模板任务失败: {str(e)}'}), 500

    # 辅助函数：获取单个模板任务的详细信息
    def get_single_template_task(task_id, mysql):
        try:
            cur = mysql.connection.cursor()
            cur.execute("""
                SELECT 
                    tt.id, 
                    tt.name, 
                    tt.description, 
                    tt.urgency,
                    tt.parent_id, 
                    tt.relative_deadline_days, 
                    tt.is_milestone,
                    tt.assignees_type,
                    GROUP_CONCAT(tta.assignee_role SEPARATOR ', ') as assignee_roles,
                    GROUP_CONCAT(tta.assignee_description SEPARATOR '; ') as assignee_descriptions,
                    CASE WHEN tt.parent_id IS NULL THEN 0 ELSE 1 END as is_child
                FROM project_management_t_template_tasks tt
                LEFT JOIN project_management_t_template_task_assignees tta ON tt.id = tta.template_task_id
                WHERE tt.id = %s
                GROUP BY tt.id, tt.name, tt.description, tt.urgency, tt.parent_id, 
                         tt.relative_deadline_days, tt.is_milestone, is_child
            """, [task_id])
            task = cur.fetchone()
            cur.close()

            if task:
                assignees_display = task['assignee_roles'] or task['assignee_descriptions'] or '' # Use empty string if none
                return {
                    'id': task['id'],
                    'name': task['name'],
                    'description': task['description'],
                    'urgency': task['urgency'],
                    'parent_id': task['parent_id'],
                    'relative_deadline_days': task['relative_deadline_days'],
                    'is_milestone': task['is_milestone'],
                    'is_child': task['is_child'] == 1,
                    'assignees_type': task['assignees_type'] ,
                    'assignees': assignees_display
                }
            return None
        except Exception as e:
             print(f"获取单个模板任务详情时出错: {str(e)}")
             return None

    @app.route('/projectmanagement/api/template/<int:template_id>', methods=['DELETE'])
    def project_api_delete_template(template_id):
        """删除模板API（软删除）"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        try:
            cur = mysql.connection.cursor()
            
            # 检查模板是否存在且未被删除
            cur.execute("""
                SELECT 1 FROM project_management_t_templates 
                WHERE id = %s AND deleted_at IS NULL
            """, [template_id])
            
            if not cur.fetchone():
                cur.close()
                return jsonify({'success': False, 'message': '模板不存在或已被删除'}), 404
            
            # 软删除模板
            cur.execute("""
                UPDATE project_management_t_templates 
                SET deleted_at = CURRENT_TIMESTAMP 
                WHERE id = %s
            """, [template_id])
            
            # 软删除相关的任务
            cur.execute("""
                UPDATE project_management_t_template_tasks 
                SET deleted_at = CURRENT_TIMESTAMP 
                WHERE template_id = %s
            """, [template_id])
            
            # 软删除相关的任务分配
            cur.execute("""
                UPDATE project_management_t_template_task_assignees tta
                INNER JOIN project_management_t_template_tasks tt 
                    ON tta.template_task_id = tt.id
                SET tta.deleted_at = CURRENT_TIMESTAMP 
                WHERE tt.template_id = %s
            """, [template_id])
            
            mysql.connection.commit()
            cur.close()
            
            return jsonify({
                'success': True, 
                'message': '模板已删除'
            })
            
        except Exception as e:
            mysql.connection.rollback()
            print(f"删除模板时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'删除模板失败: {str(e)}'}), 500

    @app.route('/projectmanagement/api/template/<int:template_id>', methods=['PUT'])
    def project_api_update_template(template_id):
        """更新模板信息API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '缺少更新数据'}), 400

        name = data.get('name')
        description = data.get('description')

        if not name:
            return jsonify({'success': False, 'message': '模板名称不能为空'}), 400

        try:
            cur = mysql.connection.cursor()
            
            # 检查模板是否存在且未被删除
            cur.execute("""
                SELECT 1 FROM project_management_t_templates 
                WHERE id = %s AND deleted_at IS NULL
            """, [template_id])
            
            if not cur.fetchone():
                cur.close()
                return jsonify({'success': False, 'message': '模板不存在或已被删除'}), 404
            
            # 更新模板信息
            cur.execute("""
                UPDATE project_management_t_templates 
                SET name = %s, description = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, [name, description, template_id])
            
            mysql.connection.commit()
            
            # 获取更新后的模板信息
            cur.execute("""
                SELECT t.*, u.name as creator_name
                FROM project_management_t_templates t
                LEFT JOIN project_management_t_users u ON t.creator_id = u.id
                WHERE t.id = %s
            """, [template_id])
            
            template = cur.fetchone()
            cur.close()
            
            if template:
                return jsonify({
                    'success': True,
                    'message': '模板信息已更新',
                    'template': {
                        'id': template['id'],
                        'name': template['name'],
                        'description': template['description'],
                        'creator': template['creator_name'] or '未知',
                        'created_at': template['created_at'].strftime('%Y-%m-%d %H:%M') if template['created_at'] else None,
                        'last_updated': template['updated_at'].strftime('%Y-%m-%d %H:%M') if template['updated_at'] else None
                    }
                })
            else:
                return jsonify({'success': False, 'message': '获取更新后的模板信息失败'}), 500
            
        except Exception as e:
            mysql.connection.rollback()
            print(f"更新模板信息时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'更新模板信息失败: {str(e)}'}), 500

    @app.route('/projectmanagement/api/template/import/<int:template_id>/to_project/<int:project_id>', methods=['POST'])
    def project_api_import_template(template_id, project_id):
        """将模板导入为项目任务"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        user_id = session['user_id']
        
        # 获取请求中的角色分配信息
        request_data = request.get_json() or {}
        role_assignees = request_data.get('roleAssignees', {})
        
        try:
            cur = mysql.connection.cursor()
            
            # 检查模板是否存在
            cur.execute("SELECT 1 FROM project_management_t_templates WHERE id = %s AND deleted_at IS NULL", [template_id])
            if not cur.fetchone():
                cur.close()
                return jsonify({'success': False, 'message': '模板不存在'}), 404
            
            # 检查项目是否存在
            cur.execute("SELECT 1 FROM project_management_t_projects WHERE id = %s", [project_id])
            if not cur.fetchone():
                cur.close()
                return jsonify({'success': False, 'message': '项目不存在'}), 404
            
            # 获取模板任务列表（按sort_order排序）
            cur.execute("""
                SELECT id, parent_id, name, description, urgency, relative_deadline_days, sort_order, assignees_type
                FROM project_management_t_template_tasks 
                WHERE template_id = %s AND deleted_at IS NULL
                ORDER BY sort_order ASC
            """, [template_id])
            template_tasks = cur.fetchall()
            
            # 用于存储模板任务ID到新任务ID的映射
            task_id_mapping = {}
            
            # 计算截止日期的基准日期（当前日期）
            base_date = datetime.datetime.now().date()
            
            # 开始导入任务
            for template_task in template_tasks:
                # 计算截止日期（如果有相对天数）
                deadline = None
                deadline_date="无截至日期"
                if template_task['relative_deadline_days'] is not None:
                    deadline = base_date + datetime.timedelta(days=template_task['relative_deadline_days'])
                    deadline_date = deadline.strftime('%Y-%m-%d')
                
                # 获取父任务ID（如果有）
                parent_id = None
                if template_task['parent_id'] is not None:
                    parent_id = task_id_mapping.get(template_task['parent_id'])
                
                # 插入新任务
                cur.execute("""
                    INSERT INTO project_management_t_tasks 
                    (project_id, parent_id, creator_id, name, description, urgency, deadline)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, [
                    project_id,
                    parent_id,
                    user_id,
                    template_task['name'],
                    template_task['description'],
                    template_task['urgency'] or '一般',
                    deadline
                ])
                
                new_task_id = cur.lastrowid
                # 保存新旧任务ID的映射关系
                task_id_mapping[template_task['id']] = new_task_id
                
                
                # 获取模板任务的角色信息
                cur.execute("""
                    SELECT assignee_description 
                    FROM project_management_t_template_task_assignees
                    WHERE template_task_id = %s AND deleted_at IS NULL
                 
                """, [template_task['id']])
                assignee_record = cur.fetchone()
                if assignee_record and assignee_record['assignee_description']:
                    assignee_user_id = None
                    # 处理任务角色负责人
                    if template_task['assignees_type'] == 'role':
                        # 检查是否有为该角色指定负责人
                        role = assignee_record['assignee_description']
                        assignee_user_id = role_assignees[role]
                    if template_task['assignees_type'] == 'person':            
                        assignee_user_id = assignee_record['assignee_description']
                    # 插入任务负责人记录
                    if assignee_user_id:
                        try:
                            cur.execute("""
                                INSERT INTO project_management_t_task_assignees
                                (task_id, user_id)
                                VALUES (%s, %s)
                            """, [new_task_id, assignee_user_id])
                            
                            # 获取钉钉配置信息
                            dingtalk_appkey = os.getenv('DINGTALK_APPKEY', 'ding6hyjguivqnldmwl2')
                            dingtalk_appsecret = os.getenv('DINGTALK_APPSECRET', 'o3EI-ke49Dl9ZQZake1ioHc7f0yMB4gxYjyLECNF8ZmkD9NzF_fJyMbUhUyN10N0')
                            
                            # 获取钉钉token
                            access_token = get_access_token(dingtalk_appkey, dingtalk_appsecret)
                            
                            if access_token:

                                
                                # 获取创建者名称
                                creator_name = session.get('user_name', '未知用户')

                                # 发送通知
                                send_task_assigned_message(
                                    access_token=access_token,
                                    appkey=dingtalk_appkey,
                                    task_name=template_task['name'],
                                    creator=creator_name,
                                    description=template_task['description'] or '无描述',
                                    deadline=deadline_date,  # deadline已经是字符串格式 'YYYY-MM-DD'
                                    user_ids=[assignee_user_id]
                                )
                                print(f"已发送任务通知给新负责人: {assignee_user_id}")
                            else:
                                print("获取钉钉token失败，无法发送任务通知")
                        except Exception as e:
                            print(f"发送钉钉通知失败: {str(e)}")
                            # 通知失败不影响任务更新，只记录日志
                
                # 记录任务创建日志
                cur.execute("""
                    INSERT INTO project_management_t_task_logs 
                    (task_id, user_id, message)
                    VALUES (%s, %s, %s)
                """, [new_task_id, user_id, '从模板导入的任务'])
            
            mysql.connection.commit()
            cur.close()
            
            return jsonify({
                'success': True, 
                'message': '模板任务已成功导入',
                'task_count': len(template_tasks)
            })
            
        except Exception as e:
            mysql.connection.rollback()
            print(f"导入模板任务时出错: {str(e)}")
            return jsonify({'success': False, 'message': f'导入模板任务失败: {str(e)}'}), 500

    # 新增API：获取模板中所有role类型的assignee_description值
    @app.route('/projectmanagement/api/template/<int:template_id>/roles')
    def project_api_get_template_roles(template_id):
        """获取模板中所有role类型的assignee_description值"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '用户未登录'}), 401
        
        try:
            cur = mysql.connection.cursor()
            
            # 检查模板是否存在
            cur.execute("SELECT 1 FROM project_management_t_templates WHERE id = %s AND deleted_at IS NULL", [template_id])
            if not cur.fetchone():
                cur.close()
                return jsonify({'success': False, 'message': '模板不存在或已被删除'}), 404
            
            # 查询模板中所有assignees_type='role'的任务的assignee_description
            cur.execute("""
                SELECT DISTINCT tta.assignee_description 
                FROM project_management_t_template_tasks tt
                LEFT JOIN project_management_t_template_task_assignees tta ON tt.id = tta.template_task_id
                WHERE tt.template_id = %s 
                AND tt.assignees_type = 'role'
                AND tt.deleted_at IS NULL
                AND tta.deleted_at IS NULL
                AND tta.assignee_description IS NOT NULL
            """, [template_id])
            
            results = cur.fetchall()
            cur.close()
            
            # 提取所有角色描述
            roles = []
            for row in results:
                desc = row['assignee_description']
                if desc:
                    # 如果描述中包含逗号，则拆分为多个角色
                    for role in desc.split(','):
                        role = role.strip()
                        if role and role not in roles:
                            roles.append(role)
            
            return jsonify({
                'success': True,
                'roles': roles
            })
            
        except Exception as e:
            print(f"获取模板角色时出错: {str(e)}")
            if 'cur' in locals() and cur and cur.connection:
                cur.close()
            return jsonify({'success': False, 'message': f'获取模板角色失败: {str(e)}'}), 500
