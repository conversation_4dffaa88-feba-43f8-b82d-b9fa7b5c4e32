/**
 * 项目管理移动端适配脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否为移动设备
    const isMobile = window.innerWidth <= 1023;
    
    // 性能优化：使用防抖函数减少频繁执行
    const debounce = (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    };
    
    if (isMobile) {
        // 使用requestAnimationFrame优化DOM操作，避免频繁重排和重绘
        requestAnimationFrame(() => {
            // 为表格添加移动端适配
            adaptTableForMobile();
            
            // 移动端居中优化
            adaptMobileCentering();
            
            // 重组快速任务创建表单
            restructureQuickTaskInput();
            
            // 移动端抽屉栏适配
            adaptTaskDrawer();
            
            // 修复日期选择器点击问题
            fixDatePickerClickIssue();
        });
        
        // 监听窗口大小变化 - 使用防抖函数优化
        window.addEventListener('resize', debounce(function() {
            if (window.innerWidth <= 1023) {
                requestAnimationFrame(() => {
                    adaptTableForMobile();
                    adaptMobileCentering();
                    restructureQuickTaskInput();
                    adaptTaskDrawer();
                    fixDatePickerClickIssue();
                });
            }
        }, 250)); // 250ms防抖延迟
    }
    
    /**
     * 为表格添加移动端适配
     */
    function adaptTableForMobile() {
        // 获取所有表格
        const tables = document.querySelectorAll('.el-table');

        tables.forEach(function(table) {
            // 获取表头
            const headers = table.querySelectorAll('th.el-table__cell');
            const headerTexts = [];

            // 收集表头文本
            headers.forEach(function(header) {
                const headerText = header.textContent.trim();
                headerTexts.push(headerText);
            });

            // 为每个单元格添加data-label属性
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(function(row) {
                const cells = row.querySelectorAll('td.el-table__cell');

                cells.forEach(function(cell, index) {
                    if (index < headerTexts.length) {
                        cell.setAttribute('data-label', headerTexts[index]);
                    }
                });
            });

            // 应用任务名称文本省略处理
            applyTaskNameEllipsis(table);
        });
    }

    /**
     * 应用任务名称文本省略处理
     */
    function applyTaskNameEllipsis(table) {
        // 获取所有任务名称元素
        const taskNameElements = table.querySelectorAll('.task-name-text, .overdue-name, .completed-name');

        taskNameElements.forEach(function(element) {
            // 确保元素有正确的样式类
            if (!element.classList.contains('mobile-text-ellipsis')) {
                element.classList.add('mobile-text-ellipsis');
            }

            // 为长文本添加title属性，方便用户查看完整内容
            const text = element.textContent.trim();
            if (text.length > 12) { // 进一步降低阈值，12个字符就显示title和省略号
                element.setAttribute('title', text);
            }

            // 应用更严格的宽度限制，让省略号更早出现
            element.style.maxWidth = 'calc(100vw - 160px)';
            element.style.whiteSpace = 'nowrap';
            element.style.overflow = 'hidden';
            element.style.textOverflow = 'ellipsis';
            element.style.display = 'inline-block';
            element.style.verticalAlign = 'middle';
        });

        // 优化移动端任务名称区域布局
        const mobileTaskNames = table.querySelectorAll('.mobile-task-name');
        mobileTaskNames.forEach(function(taskName) {
            // 确保任务名称容器有正确的flex布局
            taskName.style.display = 'flex';
            taskName.style.alignItems = 'center';
            taskName.style.gap = '8px';
            taskName.style.width = '100%';
            taskName.style.overflow = 'hidden';

            // 优化复选框样式
            const checkbox = taskName.querySelector('.task-checkbox');
            if (checkbox) {
                checkbox.style.flexShrink = '0';
                checkbox.style.marginRight = '0';
            }

            // 优化任务名称文本样式
            const taskNameText = taskName.querySelector('.task-name-text, .completed-name, .overdue-name');
            if (taskNameText) {
                taskNameText.style.flex = '1';
                taskNameText.style.minWidth = '0';
                taskNameText.style.display = 'inline-block';
                taskNameText.style.whiteSpace = 'nowrap';
                taskNameText.style.overflow = 'hidden';
                taskNameText.style.textOverflow = 'ellipsis';
                taskNameText.style.verticalAlign = 'middle';
            }
        });

        // 处理移动端卡片内的任务名称容器
        const mobileTaskContents = table.querySelectorAll('.mobile-task-content');
        mobileTaskContents.forEach(function(content) {
            const taskNameContainer = content.querySelector('div:first-child');
            if (taskNameContainer) {
                taskNameContainer.style.overflow = 'hidden';
                taskNameContainer.style.width = '100%';
            }
        });

        // 优化移动端任务信息图标对齐
        const mobileTaskInfos = table.querySelectorAll('.mobile-task-info');
        mobileTaskInfos.forEach(function(info) {
            // 确保任务信息容器有正确的样式
            info.style.display = 'flex';
            info.style.flexWrap = 'wrap';
            info.style.gap = '15px';
            info.style.alignItems = 'center';

            // 优化图标样式
            const icons = info.querySelectorAll('.el-icon');
            icons.forEach(function(icon) {
                icon.style.fontSize = '14px';
                icon.style.lineHeight = '1';
                icon.style.verticalAlign = 'middle';
                icon.style.display = 'inline-flex';
                icon.style.alignItems = 'center';
                icon.style.justifyContent = 'center';
                icon.style.flexShrink = '0';
                icon.style.width = '14px';
                icon.style.height = '14px';
            });
        });
    }
    
    /**
     * 移动端任务抽屉适配
     */
    function adaptTaskDrawer() {
        // 获取任务抽屉
        const taskDrawer = document.querySelector('.task-drawer');
        
        if (taskDrawer) {
            // 添加关闭按钮点击事件
            const closeButton = taskDrawer.querySelector('.drawer-close');
            
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    taskDrawer.classList.remove('open');
                });
            }

            // 添加手势滑动关闭功能
            let touchStartX = 0;
            let touchEndX = 0;
            const MIN_SWIPE_DISTANCE = 100; // 最小滑动距离
            
            taskDrawer.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            }, { passive: true });
            
            taskDrawer.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipeGesture();
            }, { passive: true });
            
            function handleSwipeGesture() {
                // 从左向右滑动超过阈值则关闭抽屉
                if (touchEndX - touchStartX > MIN_SWIPE_DISTANCE) {
                    taskDrawer.classList.remove('open');
                }
            }
            
            // 如果抽屉中有内容区域，禁用默认滚动事件冒泡，避免手势冲突
            const drawerContent = taskDrawer.querySelector('.drawer-content');
            if (drawerContent) {
                drawerContent.addEventListener('touchmove', function(e) {
                    e.stopPropagation();
                }, { passive: true });
            }
            
            // 抽屉标题显示完整内容（详情页面不应省略）
            const drawerTitle = taskDrawer.querySelector('.drawer-title');
            if (drawerTitle) {
                // 确保抽屉标题显示完整内容，不进行截断
                // 详情页面用户需要看到完整的任务名称
                drawerTitle.style.whiteSpace = 'normal';
                drawerTitle.style.overflow = 'visible';
                drawerTitle.style.textOverflow = 'unset';
                drawerTitle.style.wordWrap = 'break-word';
                drawerTitle.style.wordBreak = 'break-all';
            }
            
            // 调整回复区域高度
            const replySection = taskDrawer.querySelector('.reply-section');
            const richEditorContent = taskDrawer.querySelector('#replyEditorContent');
            const richEditorToolbar = taskDrawer.querySelector('#replyEditorToolbar');
            
            if (replySection && richEditorContent) {
                // 动态调整富文本编辑器高度以适应移动端屏幕
                adjustEditorHeight();
                window.addEventListener('resize', adjustEditorHeight);
                
                function adjustEditorHeight() {
                    const viewportHeight = window.innerHeight;
                    // 为编辑器分配适当的高度，避免占据过多屏幕空间
                    const newHeight = Math.min(120, Math.max(60, viewportHeight * 0.2));
                    richEditorContent.style.height = newHeight + 'px';
                }
                
                // 增强富文本编辑器移动端体验 - 性能优化版
                enhanceRichEditor(richEditorContent, richEditorToolbar);
            }
            
            // 在抽屉打开时阻止底层滚动
            const mainArea = document.querySelector('.project-main-area');
            
            if (mainArea) {
                // 监听抽屉的open类变化
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.attributeName === 'class') {
                            const isOpen = taskDrawer.classList.contains('open');
                            document.body.style.overflow = isOpen ? 'hidden' : '';
                        }
                    });
                });
                
                observer.observe(taskDrawer, { attributes: true });
            }
            
            // 美化抽屉页面中的按钮 - 性能优化版
            beautifyDrawerButtons();
        }
    }
    
    /**
     * 增强富文本编辑器移动端体验 - 性能优化版
     */
    function enhanceRichEditor(editorContent, editorToolbar) {
        if (!editorContent || !editorToolbar) return;
        
        // 使用MutationObserver代替轮询，更高效地检测编辑器初始化
        const observer = new MutationObserver((mutations, observer) => {
            const toolbar = editorToolbar.querySelector('.ql-toolbar');
            if (toolbar) {
                // 找到工具栏后停止观察
                observer.disconnect();
                
                // 简化移动端工具栏，只保留最常用的按钮
                const unnecessaryButtons = toolbar.querySelectorAll(
                    '.ql-blockquote, .ql-code-block, .ql-script, .ql-indent, .ql-direction, .ql-formula, .ql-color, .ql-background'
                );
                
                // 使用requestAnimationFrame优化样式修改
                requestAnimationFrame(() => {
                    unnecessaryButtons.forEach(button => {
                        if (button) {
                            button.style.display = 'none';
                        }
                    });
                    
                    // 增加工具栏按钮的点击区域
                    const toolbarButtons = toolbar.querySelectorAll('button, .ql-picker');
                    toolbarButtons.forEach(button => {
                        button.style.margin = '2px';
                        button.style.padding = '3px';
                    });
                });

                // 动态调整编辑器高度
                const editor = editorContent.querySelector('.ql-editor');
                if (editor) {
                    // 初始化时设置最小高度
                    editor.style.minHeight = '40px';
                    editor.style.maxHeight = '80px';
                    
                    // 监听输入事件
                    editor.addEventListener('input', () => {
                        requestAnimationFrame(() => {
                            // 获取内容实际高度
                            const contentHeight = editor.scrollHeight;
                            // 根据内容高度动态调整，但不超过最大高度
                            const newHeight = Math.min(Math.max(contentHeight, 40), 80);
                            editor.style.height = `${newHeight}px`;
                        });
                    });

                    // 监听粘贴事件
                    editor.addEventListener('paste', () => {
                        // 使用setTimeout确保在内容粘贴后调整高度
                        setTimeout(() => {
                            const contentHeight = editor.scrollHeight;
                            const newHeight = Math.min(Math.max(contentHeight, 40), 80);
                            editor.style.height = `${newHeight}px`;
                        }, 0);
                    });

                    // 监听清空内容事件
                    const observer = new MutationObserver(() => {
                        if (editor.innerHTML === '<p><br></p>' || editor.innerHTML === '') {
                            editor.style.height = '40px';
                        }
                    });

                    observer.observe(editor, {
                        childList: true,
                        characterData: true,
                        subtree: true
                    });
                }
                
                // 为编辑器添加聚焦/失焦状态样式
                const styleElement = document.createElement('style');
                styleElement.textContent = `
                    #replyEditorContent.editor-focused {
                        box-shadow: 0 0 0 2px rgba(240, 81, 35, 0.2);
                    }
                `;
                document.head.appendChild(styleElement);
                
                editorContent.addEventListener('focus', () => {
                    editorContent.classList.add('editor-focused');
                }, true);
                
                editorContent.addEventListener('blur', () => {
                    editorContent.classList.remove('editor-focused');
                }, true);
            }
        });
        
        // 开始观察编辑器工具栏区域的变化
        observer.observe(editorToolbar, { 
            childList: true,
            subtree: true 
        });
        
        // 设置超时保护，确保观察器不会无限期运行
        setTimeout(() => {
            observer.disconnect();
        }, 5000); // 5秒后停止观察
    }
    
    /**
     * 美化抽屉页面中的按钮 - 性能优化版
     */
    function beautifyDrawerButtons() {
        // 检查是否已添加样式，避免重复添加
        if (document.getElementById('ripple-style')) {
            return;
        }
        
        // 添加波纹样式 - 只添加一次
        const styleElement = document.createElement('style');
        styleElement.id = 'ripple-style';
        styleElement.textContent = `
            .ripple-effect {
                position: absolute;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            }
            
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            .el-button {
                position: relative;
                overflow: hidden;
            }
            
            .reply-section .el-button .el-icon {
                position: relative;
                top: -1px;
            }
        `;
        
        document.head.appendChild(styleElement);
        
        // 使用事件委托而不是为每个按钮添加监听器
        const replySection = document.querySelector('.reply-section');
        if (replySection) {
            // 只添加一个事件监听器到父元素
            replySection.addEventListener('click', function(e) {
                // 检查点击的是否是按钮
                const button = e.target.closest('.el-button');
                if (button) {
                    createRippleEffect(e, button);
                }
            });
        }
        
        // 点击波纹效果 - 优化版
        function createRippleEffect(e, button) {
            // 如果已经有波纹效果，则不再添加
            if (button.querySelector('.ripple-effect')) {
                return;
            }
            
            // 创建波纹元素
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            
            // 设置波纹样式
            const buttonRect = button.getBoundingClientRect();
            const size = Math.max(buttonRect.width, buttonRect.height);
            
            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${e.clientX - buttonRect.left - size / 2}px`;
            ripple.style.top = `${e.clientY - buttonRect.top - size / 2}px`;
            
            // 添加到按钮并设置动画
            button.appendChild(ripple);
            
            // 动画结束后移除波纹元素
            setTimeout(() => {
                if (ripple.parentNode === button) {
                    button.removeChild(ripple);
                }
            }, 600);
        }
    }
    
    /**
     * 重组快速任务创建表单
     */
    function restructureQuickTaskInput() {
        // 获取所有快速任务创建表单
        const quickTaskInputs = document.querySelectorAll('.quick-task-input');
        
        quickTaskInputs.forEach(function(quickTaskInput) {
            // 检查是否已经重组
            if (!quickTaskInput.querySelector('.quick-task-options-row')) {
                // 获取输入框和加号按钮
                const plusButton = quickTaskInput.querySelector('.quick-task-plus');
                const textInput = quickTaskInput.querySelector('.quick-task-text');
                
                // 获取所有选项按钮
                const optionElements = Array.from(quickTaskInput.children).filter(el => {
                    return el !== plusButton && 
                           el !== textInput && 
                           (el.classList.contains('quick-task-assignee') || 
                            el.classList.contains('quick-task-date') || 
                            el.classList.contains('quick-task-project') || 
                            el.classList.contains('quick-task-urgency') ||
                            el.tagName === 'EL-BUTTON');
                });
                
                // 创建选项行容器
                const optionsRow = document.createElement('div');
                optionsRow.className = 'quick-task-options-row';
                
                // 移动选项到新容器
                optionElements.forEach(el => {
                    quickTaskInput.removeChild(el);
                    optionsRow.appendChild(el);
                });
                
                // 添加选项行到表单
                quickTaskInput.appendChild(optionsRow);
            }
        });
    }
    
    /**
     * 移动端居中优化 - 性能优化版
     */
    function adaptMobileCentering() {
        // 创建一个CSS样式表而不是直接操作样式
        if (document.getElementById('mobile-centering-style')) {
            return; // 如果样式已经添加，则不重复添加
        }
        
        const styleElement = document.createElement('style');
        styleElement.id = 'mobile-centering-style';
        
        // 使用CSS选择器批量应用样式，避免逐个元素操作
        styleElement.textContent = `
            /* 优化首页显示 */
            .home-header {
                text-align: center;
            }
            
            /* 任务区域显示优化 */
            .home-task-section .task-section-header {
                text-align: center;
            }
            
            .home-task-section .task-section-tabs {
                justify-content: center;
            }
            
            /* 表单控件宽度优化 */
            .el-select, .el-input, .el-date-picker {
                width: 100%;
            }
            
            /* 查询区域优化 */
            .filter-item-style {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            
            /* 图表区域优化 */
            .chart-header {
                text-align: center;
            }
            
            /* 确保分页居中 */
            .pagination-container {
                display: flex;
                justify-content: center;
            }
            
            /* 统计区域显示优化 */
            .stats-items-container {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 10px;
                margin-top: 10px;
            }
        `;
        
        document.head.appendChild(styleElement);
        
        // 只处理需要DOM操作的部分，使用批处理减少重排重绘
        const statsBar = document.querySelector('.stats-bar');
        if (statsBar && !statsBar.querySelector('.stats-items-container')) {
            const statItems = Array.from(statsBar.querySelectorAll('.stats-item'));
            
            if (statItems.length > 0) {
                // 使用文档片段减少DOM操作
                const fragment = document.createDocumentFragment();
                const itemsContainer = document.createElement('div');
                itemsContainer.className = 'stats-items-container';
                
                // 将所有统计项添加到容器
                statItems.forEach(item => {
                    // 克隆节点而不是移动，避免重排
                    const clonedItem = item.cloneNode(true);
                    itemsContainer.appendChild(clonedItem);
                });
                
                fragment.appendChild(itemsContainer);
                
                // 一次性批量更新DOM
                requestAnimationFrame(() => {
                    // 清除原有的统计项
                    statItems.forEach(item => {
                        if (item.parentNode === statsBar) {
                            statsBar.removeChild(item);
                        }
                    });
                    
                    // 添加新的容器
                    statsBar.appendChild(fragment);
                });
            }
        }
    }
    
    /**
     * 修复日期选择器点击问题
     */
    function fixDatePickerClickIssue() {
        // 获取所有快速任务日期选择器
        const quickTaskDates = document.querySelectorAll('.quick-task-date');
        
        quickTaskDates.forEach(dateContainer => {
            // 获取实际的日期输入框和按钮
            const dateInput = dateContainer.querySelector('.el-date-editor');
            const dateButton = dateContainer.querySelector('.date-button');
            const dateIcon = dateContainer.querySelector('.quick-task-date-icon');
            const dateText = dateContainer.querySelector('.quick-task-date-text');
            
            if (dateInput && dateButton) {
                // 移除可能导致问题的样式
                if (dateInput.style.opacity === '0') {
                    // 保持隐藏但改变方式，确保事件能够被捕获
                    dateInput.style.opacity = '0.01';  // 几乎不可见但仍能接收事件
                    dateInput.style.width = '100%';    // 确保宽度足够
                    dateInput.style.height = '100%';   // 确保高度足够
                    dateInput.style.position = 'absolute';
                    dateInput.style.top = '0';
                    dateInput.style.left = '0';
                    dateInput.style.zIndex = '1';      // 确保在按钮上方以接收事件
                }
                
                // 确保容器有相对定位，以便绝对定位的子元素能正确定位
                dateContainer.style.position = 'relative';
                
                // 确保整个按钮区域都可点击
                if (dateButton) {
                    // 确保按钮内的所有元素都能正确接收事件
                    const allButtonElements = dateButton.querySelectorAll('*');
                    allButtonElements.forEach(element => {
                        element.style.pointerEvents = 'none'; // 禁用子元素的指针事件，让事件穿透到按钮
                    });
                    
                    // 确保文本可见且可交互
                    if (dateText) {
                        dateText.style.pointerEvents = 'none';
                    }
                    
                    // 修复日期文本区域的点击问题
                    dateButton.style.position = 'relative';
                    dateButton.style.zIndex = '2'; // 确保按钮在输入框之上
                }
                
                // 为按钮添加点击事件，而不是容器
                dateButton.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    
                    // 添加选中状态样式
                    this.classList.add('date-button-active');
                    if (dateIcon) dateIcon.style.color = '#F05123';
                    if (dateText) dateText.style.color = '#F05123';
                    
                    // 获取实际输入框元素
                    const inputElement = dateInput.querySelector('input');
                    
                    if (inputElement) {
                        // 使用更可靠的方式触发点击
                        setTimeout(() => {
                            // 创建并触发鼠标事件
                            const clickEvent = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            });
                            inputElement.dispatchEvent(clickEvent);
                            
                            // 聚焦输入框
                            inputElement.focus();
                            
                            // 如果上述方法不起作用，尝试模拟触摸事件
                            const touchEvent = new TouchEvent('touchend', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            inputElement.dispatchEvent(touchEvent);
                        }, 10);
                    }
                }, { passive: false });
                
                // 监听日期变化事件，当日期被选中后恢复默认样式
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'characterData' || 
                            mutation.type === 'childList' || 
                            (mutation.type === 'attributes' && mutation.attributeName === 'value')) {
                            // 当日期值变化时，恢复默认样式
                            setTimeout(() => {
                                dateButton.classList.remove('date-button-active');
                                if (dateIcon) dateIcon.style.color = '';
                                if (dateText) dateText.style.color = '';
                            }, 300); // 延迟恢复，以便用户能看到选中状态
                            
                            // 一旦检测到变化，可以断开观察
                            observer.disconnect();
                            
                            // 重新添加观察器，以便下次选择
                            setTimeout(() => {
                                setupObserver();
                            }, 500);
                        }
                    });
                });
                
                function setupObserver() {
                    // 观察输入框值的变化
                    const inputElement = dateInput.querySelector('input');
                    if (inputElement) {
                        observer.observe(inputElement, { 
                            attributes: true, 
                            childList: true, 
                            characterData: true,
                            subtree: true
                        });
                    }
                    
                    // 观察日期文本的变化
                    if (dateText) {
                        observer.observe(dateText, { 
                            characterData: true,
                            childList: true,
                            subtree: true
                        });
                    }
                }
                
                setupObserver();
                
                // 监听日历面板关闭事件
                document.addEventListener('click', function(e) {
                    // 如果点击的不是日期选择器内的元素，则恢复默认样式
                    if (!dateContainer.contains(e.target)) {
                        dateButton.classList.remove('date-button-active');
                        if (dateIcon) dateIcon.style.color = '';
                        if (dateText) dateText.style.color = '';
                    }
                });
                
                // 添加触摸反馈效果
                dateButton.addEventListener('touchstart', function(e) {
                    e.stopPropagation(); // 阻止冒泡
                    this.style.backgroundColor = 'rgba(240, 81, 35, 0.1)';
                    if (dateIcon) dateIcon.style.color = '#F05123';
                    if (dateText) dateText.style.color = '#F05123';
                });
                
                dateButton.addEventListener('touchend', function(e) {
                    e.stopPropagation(); // 阻止冒泡
                    // 不立即清除背景色，让用户能看到反馈
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                        // 不清除文字颜色，由选择事件处理
                    }, 200);
                });
                
                // 添加CSS样式，确保整个按钮区域可点击
                dateButton.style.cursor = 'pointer';
                dateButton.style.userSelect = 'none';
                dateButton.style.webkitTapHighlightColor = 'transparent';
                
                // 确保日期文本区域可点击
                if (dateText) {
                    dateText.style.pointerEvents = 'none'; // 让事件穿透到按钮
                }
            }
        });
        
        // 添加选中状态的样式
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            .date-button-active {
                color: #F05123 !important;
                border-color: #F05123 !important;
            }
            
            .date-button-active .el-icon {
                color: #F05123 !important;
            }
            
            .date-button-active .quick-task-date-text {
                color: #F05123 !important;
            }
            
            .quick-task-date .el-input__inner:focus {
                border-color: #F05123 !important;
            }
            
            .el-picker-panel__icon-btn {
                color: #F05123 !important;
            }
            
            .el-date-table td.current:not(.disabled) span {
                background-color: #F05123 !important;
            }
            
            /* 确保整个按钮区域可点击 */
            .date-button {
                position: relative;
                z-index: 2;
                cursor: pointer;
                user-select: none;
                -webkit-tap-highlight-color: transparent;
                width: 100%;
                height: 100%;
            }
        `;
        document.head.appendChild(styleElement);
    }
}); 